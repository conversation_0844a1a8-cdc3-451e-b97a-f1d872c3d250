#!/bin/bash

# Define your bucket and the local target directory
bucket="s3://lernen-documents-prod-v5"
target_directory="/Users/<USER>/Desktop/bulk_student_image_download/10266"

# List all files in the bucket recursively
aws s3 ls $bucket/10266/STUDENT/ --recursive --profile aws_prod_admin_2025 | while read -r line; do
    # Extract the full S3 path of the file
    full_s3_path=$(echo $line | awk '{print $4}')

    # Extract just the base filename
    filename=$(basename $full_s3_path)

    # Check if the filename contains 'thumbnail'
    if [[ ! "$filename" =~ thumbnail ]]; then
        # Copy the file from S3 to the local directory with the new path
        aws s3 cp "$bucket/$full_s3_path" "$target_directory/$filename" --profile aws_prod_admin_2025
    fi
done
