SELECT 
    student_id,
    JSON_UNQUOTE(
        JSON_EXTRACT(
            student_documents,
            REPLACE(
                JSON_UNQUOTE(
                    JSON_SEARCH(student_documents, 'one', 'Student Profile Image', NULL, '$[*].documentName')
                ), 
                '.documentName', 
                '.documentId'
            )
        )
    ) AS document_id
FROM
    students
WHERE 
    institute_id = 10265
    AND JSON_SEARCH(student_documents, 'one', 'Student Profile Image', NULL, '$[*].documentName') IS NOT NULL;
--LIMIT 2;



SELECT
    admission_number, name, standard_name, section_name, students.student_id,
    JSON_UNQUOTE(
        JSON_EXTRACT(
            student_documents,
            REPLACE(
                JSON_UNQUOTE(
                    JSON_SEARCH(student_documents, 'one', 'STUDENT_PROFILE_IMAGE', NULL, '$[*].documentType')
                ),
                '.documentType',
                '.documentId'
            )
        )
    ) AS document_id
FROM
    students
INNER JOIN student_academic_session_details on student_academic_session_details.student_id = students.student_id and student_academic_session_details.academic_session_id = 329
INNER JOIN standards on student_academic_session_details.standard_id = standards.standard_id
LEFT JOIN standard_section_mapping on standard_section_mapping.section_id = student_academic_session_details.section_id and student_academic_session_details.academic_session_id = 329
WHERE
    students.institute_id = 10265
    AND JSON_SEARCH(student_documents, 'one', 'STUDENT_PROFILE_IMAGE', NULL, '$[*].documentType') IS NOT NULL
ORDER BY level, section_name, name;
--LIMIT 2;



SELECT
    admission_number, name, standard_name, section_name, students.student_id,
    JSON_UNQUOTE(
        JSON_EXTRACT(
            student_documents,
            REPLACE(
                JSON_UNQUOTE(
                    JSON_SEARCH(student_documents, 'one', 'STUDENT_PROFILE_IMAGE', NULL, '$[*].documentType')
                ),
                '.documentType',
                '.documentId'
            )
        )
    ) AS document_id, student_documents
FROM
    students
INNER JOIN student_academic_session_details on student_academic_session_details.student_id = students.student_id and student_academic_session_details.academic_session_id = 329
INNER JOIN standards on student_academic_session_details.standard_id = standards.standard_id
LEFT JOIN standard_section_mapping on standard_section_mapping.section_id = student_academic_session_details.section_id and student_academic_session_details.academic_session_id = 329
WHERE
    students.institute_id = 10265
    AND JSON_SEARCH(student_documents, 'one', 'STUDENT_PROFILE_IMAGE', NULL, '$[*].documentType') IS NOT NULL and
      JSON_UNQUOTE(
              JSON_EXTRACT(
                  student_documents,
                  REPLACE(
                      JSON_UNQUOTE(
                          JSON_SEARCH(student_documents, 'one', 'STUDENT_PROFILE_IMAGE', NULL, '$[*].documentType')
                      ),
                      '.documentType',
                      '.documentId'
                  )
              )
          ) is null
ORDER BY level, section_name, name;