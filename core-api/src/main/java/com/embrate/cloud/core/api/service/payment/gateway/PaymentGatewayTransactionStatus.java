package com.embrate.cloud.core.api.service.payment.gateway;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public enum  PaymentGatewayTransactionStatus {
    INITIATED, SUCCESS, FAILED, PENDING, CANC<PERSON>LED, INCOMPLETE, FLAGGED, UNKNOWN;

    public static PaymentGatewayTransactionStatus getStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        }
        for (PaymentGatewayTransactionStatus transactionStatus : PaymentGatewayTransactionStatus
                .values()) {
            if (transactionStatus.name()
                    .equalsIgnoreCase(status)) {
                return transactionStatus;
            }
        }
        return null;
    }

    public static boolean finalStatus(PaymentGatewayTransactionStatus status){
        //removing failed status from final status, so support jodo case as they are sending success after failed
//        return status == SUCCESS || status == FAILED || status == CANCELLED || status == INCOMPLETE || status == FLAGGED;
        return status == SUCCESS || status == CANCELLED || status == INCOMPLETE || status == FLAGGED;
    }
}
