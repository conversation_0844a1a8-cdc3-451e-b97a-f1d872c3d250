package com.lernen.cloud.core.api.examination.report.configs;

/**
 * <AUTHOR>
 */

public class GridConfigs {

    private final GridHeaderConfigs gridHeaderConfigs;

    private final GridTotalMarksRowConfigs gridTotalMarksRowConfigs;

    private final GridPersonalityTraitsConfigs gridPersonalityTraitsConfigs;

    private String maxMarksColorHexCode;

    private final boolean showPercentSymbolInGrid;

    public GridConfigs(GridHeaderConfigs gridHeaderConfigs, GridTotalMarksRowConfigs gridTotalMarksRowConfigs) {
        this.gridHeaderConfigs = gridHeaderConfigs;
        this.gridTotalMarksRowConfigs = gridTotalMarksRowConfigs;
        this.showPercentSymbolInGrid = true;
        this.gridPersonalityTraitsConfigs = null;
    }

    public GridConfigs(GridHeaderConfigs gridHeaderConfigs, GridTotalMarksRowConfigs gridTotalMarksRowConfigs, boolean showPercentSymbolInGrid) {
        this.gridHeaderConfigs = gridHeaderConfigs;
        this.gridTotalMarksRowConfigs = gridTotalMarksRowConfigs;
        this.showPercentSymbolInGrid = showPercentSymbolInGrid;
        this.gridPersonalityTraitsConfigs = null;
    }

    public GridConfigs(GridHeaderConfigs gridHeaderConfigs, GridTotalMarksRowConfigs gridTotalMarksRowConfigs, GridPersonalityTraitsConfigs gridPersonalityTraitsConfigs) {
        this.gridHeaderConfigs = gridHeaderConfigs;
        this.gridTotalMarksRowConfigs = gridTotalMarksRowConfigs;
        this.showPercentSymbolInGrid = true;
        this.gridPersonalityTraitsConfigs = gridPersonalityTraitsConfigs;
    }

    public GridTotalMarksRowConfigs getGridTotalMarksRowConfigs() {
        return gridTotalMarksRowConfigs;
    }

    public GridHeaderConfigs getGridHeaderConfigs() {
        return gridHeaderConfigs;
    }

    public String getMaxMarksColorHexCode() {
        return maxMarksColorHexCode;
    }

    public void setMaxMarksColorHexCode(String maxMarksColorHexCode) {
        this.maxMarksColorHexCode = maxMarksColorHexCode;
    }

    public boolean isShowPercentSymbolInGrid() {
        return showPercentSymbolInGrid;
    }

    public static GridConfigs forSkipTotalRow(){
        return new GridConfigs(new GridHeaderConfigs(false), GridTotalMarksRowConfigs.skipTotalRow());
    }

    public static GridConfigs forOnlyObtainedTotalRow(){
        return new GridConfigs(new GridHeaderConfigs(false), GridTotalMarksRowConfigs.onlyObtainedTotalRow());
    }

    public GridPersonalityTraitsConfigs getGridPersonalityTraitsConfigs() {
        return gridPersonalityTraitsConfigs;
    }

    @Override
    public String toString() {
        return "GridConfigs [gridHeaderConfigs=" + gridHeaderConfigs + ", gridTotalMarksRowConfigs="
                + gridTotalMarksRowConfigs + ", maxMarksColorHexCode=" + maxMarksColorHexCode
                + ", showPercentSymbolInGrid=" + showPercentSymbolInGrid + "]";
    }
}
