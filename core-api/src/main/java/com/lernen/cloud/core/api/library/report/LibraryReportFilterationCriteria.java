package com.lernen.cloud.core.api.library.report;

/**
 * Encapsulates all filteration criteria for library reports
 * Used to clean up endpoint method signatures and provide utility methods
 * for parsing string parameters into appropriate data types
 */
public class LibraryReportFilterationCriteria {
    
    public String reportTypeStr;
    public Integer startDate;
    public Integer endDate;
    public String genreIds;
    public String publicationIds;
    public String publisherIds;
    public String authorIds;
    public String libraryTypeIds;

    public LibraryReportFilterationCriteria() {
    }
    
    public LibraryReportFilterationCriteria(String reportTypeStr, Integer startDate, Integer endDate,
                                           String genreIds, String publicationIds, String publisherIds,
                                           String authorIds, String libraryTypeIds) {
        this.reportTypeStr = reportTypeStr;
        this.startDate = startDate;
        this.endDate = endDate;
        this.genreIds = genreIds;
        this.publicationIds = publicationIds;
        this.publisherIds = publisherIds;
        this.authorIds = authorIds;
        this.libraryTypeIds = libraryTypeIds;
    }

    //setters
    public void setReportTypeStr(String reportTypeStr) {
        this.reportTypeStr = reportTypeStr;
    }
    
    public void setStartDate(Integer startDate) {
        this.startDate = startDate;
    }
    
    public void setEndDate(Integer endDate) {
        this.endDate = endDate;
    }
    
    public void setGenreIds(String genreIds) {
        this.genreIds = genreIds;
    }

    public void setPublicationIds(String publicationIds) {
        this.publicationIds = publicationIds;
    }
    
    public void setPublisherIds(String publisherIds) {
        this.publisherIds = publisherIds;
    }
    
    public void setAuthorIds(String authorIds) {
        this.authorIds = authorIds;
    }

    public void setLibraryTypeIds(String libraryTypeIds) {
        this.libraryTypeIds = libraryTypeIds;
    }
    
    //getters
    public String getReportTypeStr() {
        return reportTypeStr;
    }
    
    public Integer getStartDate() {
        return startDate;
    }
    
    public Integer getEndDate() {
        return endDate;
    }
    
    public String getGenreIds() {
        return genreIds;
    }
    
    public String getPublicationIds() {
        return publicationIds;
    }
    
    public String getPublisherIds() {
        return publisherIds;
    }
    
    public String getAuthorIds() {
        return authorIds;
    }
    
    public String getLibraryTypeIds() {
        return libraryTypeIds;
    }
   
    @Override
    public String toString() {
        return "LibraryReportFilterationCriteria [" +
               "reportTypeStr=" + reportTypeStr +
               ", startDate=" + startDate +
               ", endDate=" + endDate +
               ", genreIds=" + genreIds +
               ", publicationIds=" + publicationIds +
               ", publisherIds=" + publisherIds +
               ", authorIds=" + authorIds +
               ", libraryTypeIds=" + libraryTypeIds +
               "]";
    }
}
