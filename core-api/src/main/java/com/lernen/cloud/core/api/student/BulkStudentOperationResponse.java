package com.lernen.cloud.core.api.student;

import java.util.List;

/**
 * Response for bulk student operations (enrollment, relieve, NSO, etc.)
 * Contains success/failure counts and error details
 *
 * <AUTHOR>
 */
public class BulkStudentOperationResponse {

    private int totalProcessed;
    private int successCount;
    private int failureCount;
    private List<String> errors;
    private List<String> successfulAdmissions;
    private boolean overallSuccess;

    public BulkStudentOperationResponse() {
    }

    public BulkStudentOperationResponse(int totalProcessed, List<String> errors) {
        this.totalProcessed = totalProcessed;
        this.errors = errors;
        this.failureCount = errors != null ? errors.size() : 0;
        this.successCount = totalProcessed - failureCount;
        this.overallSuccess = failureCount == 0;
    }

    public BulkStudentOperationResponse(int totalProcessed, int successCount, int failureCount,
                                   List<String> errors, List<String> successfulAdmissions) {
        this.totalProcessed = totalProcessed;
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.errors = errors;
        this.successfulAdmissions = successfulAdmissions;
        this.overallSuccess = failureCount == 0;
    }

    public int getTotalProcessed() {
        return totalProcessed;
    }

    public void setTotalProcessed(int totalProcessed) {
        this.totalProcessed = totalProcessed;
    }

    public int getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(int successCount) {
        this.successCount = successCount;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public List<String> getSuccessfulAdmissions() {
        return successfulAdmissions;
    }

    public void setSuccessfulAdmissions(List<String> successfulAdmissions) {
        this.successfulAdmissions = successfulAdmissions;
    }

    public boolean isOverallSuccess() {
        return overallSuccess;
    }

    public void setOverallSuccess(boolean overallSuccess) {
        this.overallSuccess = overallSuccess;
    }

    @Override
    public String toString() {
        return "BulkStudentOperationResponse{" +
                "totalProcessed=" + totalProcessed +
                ", successCount=" + successCount +
                ", failureCount=" + failureCount +
                ", errors=" + errors +
                ", successfulAdmissions=" + successfulAdmissions +
                ", overallSuccess=" + overallSuccess +
                '}';
    }
}
