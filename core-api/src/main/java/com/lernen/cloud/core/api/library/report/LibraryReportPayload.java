package com.lernen.cloud.core.api.library.report;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.core.api.report.util.IReportPayload;

public class LibraryReportPayload implements IReportPayload {

    private final int instituteId;
    private final UUID userId;
    private final LibraryReportType reportType;
    private final Integer startDate;
    private final Integer endDate;
    private final Set<UUID> genreIds;
    private final Set<UUID> publicationIds;
    private final Set<UUID> publisherIds;
    private final Set<UUID> authorIds;
    private final Set<UUID> libraryTypeIds;
    private final boolean showAccessionDetails;

    public LibraryReportPayload(int instituteId, UUID userId, LibraryReportType reportType, Integer startDate, Integer endDate,
                               Set<UUID> genreIds, Set<UUID> publicationIds, Set<UUID> publisherIds,
                               Set<UUID> authorIds, Set<UUID> libraryTypeIds, boolean showAccessionDetails) {
        this.instituteId = instituteId;
        this.userId = userId;
        this.reportType = reportType;
        this.startDate = startDate;
        this.endDate = endDate;
        this.genreIds = genreIds;
        this.publicationIds = publicationIds;
        this.publisherIds = publisherIds;
        this.authorIds = authorIds;
        this.libraryTypeIds = libraryTypeIds;
        this.showAccessionDetails = showAccessionDetails;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public UUID getUserId() {
        return userId;
    }

    public LibraryReportType getReportType() {
        return reportType;
    }

    public Integer getStartDate() {
        return startDate;
    }

    public Integer getEndDate() {
        return endDate;
    }

    public Set<UUID> getGenreIds() {
        return genreIds;
    }

    public Set<UUID> getPublicationIds() {
        return publicationIds;
    }

    public Set<UUID> getPublisherIds() {
        return publisherIds;
    }

    public Set<UUID> getAuthorIds() {
        return authorIds;
    }

    public Set<UUID> getLibraryTypeIds() {
        return libraryTypeIds;
    }

    public boolean isShowAccessionDetails() {
        return showAccessionDetails;
    }

    @Override
    public List<String> getSelectedColumns() {
        return Collections.emptyList();
    }

    @Override
    public String toString() {
        return "LibraryReportPayload [instituteId=" + instituteId + ", userId=" + userId + 
               ", reportType=" + reportType + 
               ", startDate=" + startDate + ", endDate=" + endDate + ", genreIds=" + genreIds + 
               ", publicationIds=" + publicationIds + ", publisherIds=" + publisherIds + 
               ", authorIds=" + authorIds + ", libraryTypeIds=" + libraryTypeIds + 
               ", showAccessionDetails=" + showAccessionDetails + "]";
    }
}
