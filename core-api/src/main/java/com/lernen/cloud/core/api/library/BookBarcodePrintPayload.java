package com.lernen.cloud.core.api.library;

import java.util.List;
import java.util.UUID;

public class BookBarcodePrintPayload {

    private List<UUID> bookIds;
    private List<String> accessionNumbers;
    private double barcodeWidthCm;
    private double barcodeHeightCm;

    public BookBarcodePrintPayload() {
    }

    public BookBarcodePrintPayload(List<UUID> bookIds, List<String> accessionNumbers, double barcodeWidthCm, double barcodeHeightCm) {
        this.bookIds = bookIds;
        this.accessionNumbers = accessionNumbers;
        this.barcodeWidthCm = barcodeWidthCm;
        this.barcodeHeightCm = barcodeHeightCm;
    }

    public List<UUID> getBookIds() {
        return bookIds;
    }

    public void setBookIds(List<UUID> bookIds) {
        this.bookIds = bookIds;
    }

    public List<String> getAccessionNumbers() {
        return accessionNumbers;
    }

    public void setAccessionNumbers(List<String> accessionNumbers) {
        this.accessionNumbers = accessionNumbers;
    }

    public double getBarcodeWidthCm() {
        return barcodeWidthCm;
    }

    public void setBarcodeWidthCm(double barcodeWidthCm) {
        this.barcodeWidthCm = barcodeWidthCm;
    }

    public double getBarcodeHeightCm() {
        return barcodeHeightCm;
    }

    public void setBarcodeHeightCm(double barcodeHeightCm) {
        this.barcodeHeightCm = barcodeHeightCm;
    }
    
    @Override
    public String toString() {
        return "BookBarcodePrintPayload{" +
                "bookIds=" + bookIds +
                ", accessionNumbers=" + accessionNumbers +
                ", barcodeWidthCm=" + barcodeWidthCm +
                ", barcodeHeightCm=" + barcodeHeightCm +
                '}';
    }
}
