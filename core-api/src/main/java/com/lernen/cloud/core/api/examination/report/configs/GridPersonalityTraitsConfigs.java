package com.lernen.cloud.core.api.examination.report.configs;

public class GridPersonalityTraitsConfigs {
    private final PersonalityTraitsColumnType personalityTraitsColumnType;
    private final PersonalityTraitsColumnStructure personalityTraitsColumnStructure;
    private final String personalityTraitColumnSubjectTitle;
    private final String personalityTraitColumnMarksTitle;
    private final float personalityTraitColumnSubjectWidth;
    private final float personalityTraitColumnMarksWidth;

    public GridPersonalityTraitsConfigs(PersonalityTraitsColumnType personalityTraitsColumnType,
                                        PersonalityTraitsColumnStructure personalityTraitsColumnStructure,
                                        String personalityTraitColumnSubjectTitle, String personalityTraitColumnMarksTitle,
                                        float personalityTraitColumnSubjectWidth, float personalityTraitColumnMarksWidth) {
        this.personalityTraitsColumnType = personalityTraitsColumnType;
        this.personalityTraitsColumnStructure = personalityTraitsColumnStructure;
        this.personalityTraitColumnSubjectTitle = personalityTraitColumnSubjectTitle;
        this.personalityTraitColumnMarksTitle = personalityTraitColumnMarksTitle;
        this.personalityTraitColumnSubjectWidth = personalityTraitColumnSubjectWidth;
        this.personalityTraitColumnMarksWidth = personalityTraitColumnMarksWidth;
    }

    public PersonalityTraitsColumnType getPersonalityTraitsColumnType() {
        return personalityTraitsColumnType;
    }

    public PersonalityTraitsColumnStructure getPersonalityTraitsColumnStructure() {
        return personalityTraitsColumnStructure;
    }

    public String getPersonalityTraitColumnSubjectTitle() {
        return personalityTraitColumnSubjectTitle;
    }

    public String getPersonalityTraitColumnMarksTitle() {
        return personalityTraitColumnMarksTitle;
    }

    public float getPersonalityTraitColumnSubjectWidth() {
        return personalityTraitColumnSubjectWidth;
    }

    public float getPersonalityTraitColumnMarksWidth() {
        return personalityTraitColumnMarksWidth;
    }

    @Override
    public String toString() {
        return "GridPersonalityTraitsConfigs [personalityTraitsColumnType=" + personalityTraitsColumnType
                + ", personalityTraitsColumnStructure=" + personalityTraitsColumnStructure
                + ", personalityTraitColumnSubjectTitle=" + personalityTraitColumnSubjectTitle
                + ", personalityTraitColumnMarksTitle=" + personalityTraitColumnMarksTitle
                + ", personalityTraitColumnSubjectWidth=" + personalityTraitColumnSubjectWidth
                + ", personalityTraitColumnMarksWidth=" + personalityTraitColumnMarksWidth + "]";
    }

}
