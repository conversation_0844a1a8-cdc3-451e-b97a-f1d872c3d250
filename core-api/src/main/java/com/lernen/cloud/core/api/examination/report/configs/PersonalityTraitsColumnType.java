package com.lernen.cloud.core.api.examination.report.configs;

import org.apache.commons.lang3.StringUtils;

/**
 * Enum for personality traits column Type configurations
 * 
 * <AUTHOR> Mittal
 */
public enum PersonalityTraitsColumnType {

    COSCHOLASTIC_WITH_PERSONALITY_TRAITS,
    ONLY_PERSONALITY_TRAITS;

    public static PersonalityTraitsColumnType getTypeFromString(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (PersonalityTraitsColumnType typeEnum : PersonalityTraitsColumnType.values()) {
            if (typeEnum.name().equalsIgnoreCase(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
