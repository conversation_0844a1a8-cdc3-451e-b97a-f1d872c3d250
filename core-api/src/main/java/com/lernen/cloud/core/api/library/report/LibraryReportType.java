package com.lernen.cloud.core.api.library.report;

import org.apache.commons.lang3.StringUtils;

import com.embrate.cloud.core.api.report.config.IReportType;

public enum LibraryReportType implements IReportType{

    LIBRARY_SUMMARY_REPORT("Library Summary Report"),
    LIBRARY_DETAILED_REPORT("Library Detailed Report");

    private final String displayName;

    LibraryReportType(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static LibraryReportType getLibraryReportType(String libraryReportType) {
        if (StringUtils.isBlank(libraryReportType)) {
            return null;
        }
        for (LibraryReportType libraryReportTypeEnum : LibraryReportType.values()) {
            if (libraryReportTypeEnum.name().equalsIgnoreCase(libraryReportType)) {
                return libraryReportTypeEnum;
            }
        }
        return null;
    }
    
}
