package com.lernen.cloud.core.api.course;

import java.util.UUID;

/**
 * Lightweight student details class for course-related operations
 * Contains only essential fields: name, admission number, father's name, class name, and section
 */
public class StudentInfoDetails {

    private final UUID studentId;
    private final String name;
    private final String admissionNumber;
    private final Integer admissionDate;
    private final String rollNumber;
    private final String fathersName;
    private final String className;
    private final String sectionName;
    private final int standardLevel;

    public StudentInfoDetails(UUID studentId, String name, String admissionNumber, Integer admissionDate, 
                              String fathersName, String className, String sectionName, String rollNumber, int standardLevel) {
        this.studentId = studentId;
        this.name = name;
        this.admissionNumber = admissionNumber;
        this.admissionDate = admissionDate;
        this.rollNumber = rollNumber;
        this.fathersName = fathersName;
        this.className = className;
        this.sectionName = sectionName;
        this.standardLevel = standardLevel;
    }

    public UUID getStudentId() {
        return studentId;
    }

    public String getName() {
        return name;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }
    public Integer getAdmissionDate() {
        return admissionDate;
    }

    public String getRollNumber() {
        return rollNumber;
    }

    public String getFathersName() {
        return fathersName;
    }

    public String getClassName() {
        return className;
    }

    public String getSectionName() {
        return sectionName;
    }

    public int getStandardLevel() {
        return standardLevel;
    }

    @Override
    public String toString() {
        return "StudentInfoDetails{" +
                "studentId=" + studentId +
                ", name='" + name + '\'' +
                ", admissionNumber='" + admissionNumber + '\'' +
                ", admissionDate='" + admissionDate + '\'' +
                ", rollNumber='" + rollNumber + '\'' +
                ", fathersName='" + fathersName + '\'' +
                ", className='" + className + '\'' +
                ", sectionName='" + sectionName + '\'' +
                ", standardLevel=" + standardLevel +
                '}';
    }
}
