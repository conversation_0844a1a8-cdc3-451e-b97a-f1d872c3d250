package com.lernen.cloud.core.api.examination.report.configs;

import org.apache.commons.lang3.StringUtils;

/**
 * Enum for personality traits column structure configurations
 * 
 * <AUTHOR> Mittal
 */
public enum PersonalityTraitsColumnStructure {

    STANDARDIZED_COLUMN_STRUCTURE,
    DIVIDED_DATA_STRUCTURE;

    public static PersonalityTraitsColumnStructure getStructureFromString(String structure) {
        if (StringUtils.isBlank(structure)) {
            return null;
        }
        for (PersonalityTraitsColumnStructure structureEnum : PersonalityTraitsColumnStructure.values()) {
            if (structureEnum.name().equalsIgnoreCase(structure)) {
                return structureEnum;
            }
        }
        return null;
    }
}