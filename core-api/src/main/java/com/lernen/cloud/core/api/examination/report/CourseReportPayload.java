package com.lernen.cloud.core.api.examination.report;

import java.util.*;
import com.embrate.cloud.core.api.report.util.IReportPayload;
import com.lernen.cloud.core.api.course.CourseReportType;

public class CourseReportPayload implements IReportPayload {

    private final int instituteId;
    private final Integer academicSessionId;
    private final UUID standardId;
    private final Set<Integer> sectionIds;
    private final Set<UUID> courseId;
    private final UUID userId;
    private final CourseReportType reportType;

    public CourseReportPayload(int instituteId, Integer academicSessionId, UUID standardId, Set<Integer> sectionIds, Set<UUID> courseId, UUID userId, CourseReportType reportType) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.standardId = standardId;
        this.sectionIds = sectionIds;
        this.courseId = courseId;
        this.userId = userId;
        this.reportType = reportType;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public Integer getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public Set<Integer> getSectionIds() {
        return sectionIds;
    }

    public Set<UUID> getCourseId() {
        return courseId;
    }

    public UUID getUserId() {
        return userId;
    }

    public CourseReportType getReportType() {
        return reportType;
    }

    @Override
    public List<String> getSelectedColumns() {
        return Collections.emptyList();
    }

    @Override
    public String toString() {
        return "CourseReportPayload{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", standardId=" + standardId +
                ", sectionIds=" + sectionIds +
                ", courseId='" + courseId + '\'' +
                ", userId=" + userId +
                ", reportType=" + reportType +
                '}';
    }
}
