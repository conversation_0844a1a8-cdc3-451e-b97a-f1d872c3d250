package com.lernen.cloud.core.api.library.report;

import java.util.UUID;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.LinkedHashSet;
import com.lernen.cloud.core.api.library.Author;
import com.lernen.cloud.core.api.library.Genre;
import com.lernen.cloud.core.api.library.Publication;
import com.lernen.cloud.core.api.library.Publisher;

/**
 * Represents summary-level book data for library reports (without accession
 * details)
 */
public class LibraryReportSummaryData {

    private final UUID bookId;
    private final String bookTitle;
    private final String isbn;
    private final String author;
    private final String genre;
    private final String publication;
    private final String publisher;
    private final String edition;
    private final int publishYear;
    private final String language;
    private final String libraryTypeName;
    private final Integer addedAt;
    private final Integer totalCopies;

    public LibraryReportSummaryData(UUID bookId, String bookTitle, String isbn, String author, String libraryTypeName,
            String genre, String publication, String publisher,
            String edition, int publishYear, String language,
            Integer totalCopies, Integer addedAt) {
        this.bookId = bookId;
        this.bookTitle = bookTitle;
        this.isbn = isbn;
        this.author = author;
        this.libraryTypeName = libraryTypeName;
        this.genre = genre;
        this.publication = publication;
        this.publisher = publisher;
        this.edition = edition;
        this.publishYear = publishYear;
        this.language = language;
        this.addedAt = addedAt;
        this.totalCopies = totalCopies;
    }

    public static List<LibraryReportSummaryData> groupByIsbnWithConcatenatedLibraryTypes(
            List<LibraryReportSummaryData> summaryDataList) {
        
        if (summaryDataList == null || summaryDataList.isEmpty()) {
            return new ArrayList<LibraryReportSummaryData>();
        }
        
        // Group by ISBN
        Map<String, List<LibraryReportSummaryData>> groupedByIsbn = 
                new HashMap<String, List<LibraryReportSummaryData>>();
        
        for (LibraryReportSummaryData data : summaryDataList) {
            String isbn = data.getIsbn();
            if (!groupedByIsbn.containsKey(isbn)) {
                groupedByIsbn.put(isbn, new ArrayList<LibraryReportSummaryData>());
            }
            groupedByIsbn.get(isbn).add(data);
        }
        
        List<LibraryReportSummaryData> result = new ArrayList<LibraryReportSummaryData>();
        
        for (Map.Entry<String, List<LibraryReportSummaryData>> entry : groupedByIsbn.entrySet()) {
            List<LibraryReportSummaryData> booksWithSameIsbn = entry.getValue();
            
            // Take the first book as the base (assuming same ISBN means same book details)
            LibraryReportSummaryData firstBook = booksWithSameIsbn.get(0);
            
            // Collect unique library type names
            Set<String> uniqueLibraryTypes = new LinkedHashSet<String>();
            
            for (LibraryReportSummaryData book : booksWithSameIsbn) {
                String libraryTypeName = book.getLibraryTypeName();
                if (libraryTypeName != null && !libraryTypeName.trim().isEmpty()) {
                    uniqueLibraryTypes.add(libraryTypeName);
                }
            }
            
            // Concatenate library types with comma
            String concatenatedLibraryTypes = String.join(", ", uniqueLibraryTypes);
            
            // Create new instance with concatenated library types
            LibraryReportSummaryData groupedData = new LibraryReportSummaryData(
                    firstBook.getBookId(),
                    firstBook.getBookTitle(),
                    firstBook.getIsbn(),
                    firstBook.getAuthor(),
                    concatenatedLibraryTypes, // This will contain comma-separated library types
                    firstBook.getGenre(),
                    firstBook.getPublication(),
                    firstBook.getPublisher(),
                    firstBook.getEdition(),
                    firstBook.getPublishYear(),
                    firstBook.getLanguage(),
                    firstBook.getTotalCopies(),
                    firstBook.getAddedAt()
            );
            
            result.add(groupedData);
        }
        
        return result;
    }

    public UUID getBookId() {
        return bookId;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public String getIsbn() {
        return isbn;
    }
    
    public String getLibraryTypeName() {
        return libraryTypeName;
    }
    
    public String getAuthor() {
        return author;
    }

    public String getGenre() {
        return genre;
    }

    public String getPublication() {
        return publication;
    }

    public String getPublisher() {
        return publisher;
    }

    public String getEdition() {
        return edition;
    }

    public int getPublishYear() {
        return publishYear;
    }

    public String getLanguage() {
        return language;
    }

    public Integer getAddedAt() {
        return addedAt;
    }

    public Integer getTotalCopies() {
        return totalCopies;
    }

    @Override
    public String toString() {
        return "LibraryReportSummaryData [bookId=" + bookId + ", bookTitle=" + bookTitle +
                ", isbn=" + isbn + ", author=" + author + ", genre=" + genre +
                ", publication=" + publication + ", publisher=" + publisher +
                ", edition=" + edition + ", publishYear=" + publishYear +
                ", language=" + language +
                ", libraryTypeName=" + libraryTypeName +
                ", addedAt=" + addedAt +
                ", totalCopies=" + totalCopies + 
                "]";
    }
}
