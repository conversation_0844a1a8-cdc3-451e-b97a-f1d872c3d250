package com.lernen.cloud.core.api.course;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.CollectionUtils;


public class StudentCoursesDetailsLite {
    
	private int academicSessionId;

	private final StudentInfoDetails studentInfoDetails;

	private final List<Course> courses;

	private final List<Course> scholasticCourses;

	private final List<Course> coScholasticCourses;

	public StudentCoursesDetailsLite(int academicSessionId, StudentInfoDetails studentInfoDetails, List<Course> courses) {
		this.academicSessionId = academicSessionId;
		this.studentInfoDetails = studentInfoDetails;
		this.courses = Course.sortCoursesBySequence(courses);
		this.scholasticCourses = Course.sortCoursesBySequence(filterScholasticCourses());
		this.coScholasticCourses = Course.sortCoursesBySequence(filterCoScholasticCourses());
	}

	private List<Course> filterScholasticCourses() {
		final List<Course> scholasticCourses = new ArrayList<>();
		if (CollectionUtils.isEmpty(courses)) {
			return scholasticCourses;
		}
		for (Course course : courses) {
			if (course.getCourseType() == CourseType.SCHOLASTIC) {
				scholasticCourses.add(course);
			}
		}
		return scholasticCourses;
	}

	private List<Course> filterCoScholasticCourses() {
		final List<Course> coScholasticCourses = new ArrayList<>();
		if (CollectionUtils.isEmpty(courses)) {
			return coScholasticCourses;
		}
		for (Course course : courses) {
			if (course.getCourseType() == CourseType.COSCHOLASTIC) {
				coScholasticCourses.add(course);
			}
		}
		return coScholasticCourses;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public void setAcademicSessionId(int academicSessionId) {
		this.academicSessionId = academicSessionId;
	}

	public StudentInfoDetails getStudentInfoDetails() {
		return studentInfoDetails;
	}

	public List<Course> getCourses() {
		return courses;
	}

	public List<Course> getScholasticCourses() {
		return scholasticCourses;
	}

	public List<Course> getCoScholasticCourses() {
		return coScholasticCourses;
	}
	
	@Override
	public String toString() {
		return "StudentCourses [academicSessionId=" + academicSessionId + ", studentInfoDetails=" + studentInfoDetails + ", courses="
				+ courses + ", scholasticCourses=" + scholasticCourses + ", coScholasticCourses="
				+ coScholasticCourses + "]";
	}

}
