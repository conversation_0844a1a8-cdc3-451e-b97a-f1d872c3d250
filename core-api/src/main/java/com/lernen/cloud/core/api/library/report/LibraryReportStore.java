package com.lernen.cloud.core.api.library.report;

import java.util.List;

import com.embrate.cloud.core.api.report.util.IReportStore;

/**
 * Store class for library report data - supports both summary and detailed flows
 */
public class LibraryReportStore implements IReportStore {

    private final boolean showAccessionDetails;
    private final String dateRange;
    private final List<LibraryReportSummaryData> summaryData;
    private final List<LibraryReportDetailedData> detailedData;
    private final String appliedFilters;

    public LibraryReportStore(boolean showAccessionDetails, String dateRange, 
                             List<LibraryReportSummaryData> summaryData, 
                             List<LibraryReportDetailedData> detailedData,
                             String appliedFilters) {
        this.showAccessionDetails = showAccessionDetails;
        this.dateRange = dateRange;
        this.summaryData = summaryData;
        this.detailedData = detailedData;
        this.appliedFilters = appliedFilters;
    }

    public boolean isShowAccessionDetails() {
        return showAccessionDetails;
    }

    public String getDateRange() {
        return dateRange;
    }

    public List<LibraryReportSummaryData> getSummaryData() {
        return summaryData;
    }

    public List<LibraryReportDetailedData> getDetailedData() {
        return detailedData;
    }

    public String getAppliedFilters() {
        return appliedFilters;
    }

    @Override
    public String toString() {
        return "LibraryReportStore [showAccessionDetails=" + showAccessionDetails + 
               ", dateRange=" + dateRange + ", summaryData=" + summaryData + 
               ", detailedData=" + detailedData + ", appliedFilters=" + appliedFilters + "]";
    }
}
