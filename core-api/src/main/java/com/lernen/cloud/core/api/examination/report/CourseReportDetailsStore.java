package com.lernen.cloud.core.api.examination.report;

import com.embrate.cloud.core.api.report.util.IReportStore;
import com.lernen.cloud.core.api.course.StudentCoursesDetailsLite;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseCountDetails;
import com.lernen.cloud.core.api.course.CourseType;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class CourseReportDetailsStore implements IReportStore {

    private final String className;
    private final List<StudentCoursesDetailsLite> studentCoursesList;

    // Added for optional courses
    private final List<Course> optionalCourses;
    private final Map<UUID, List<UUID>> studentOptionalCourseMapping;
    private final Map<UUID, Map<CourseType, CourseCountDetails>> studentCourseCountMap;

    public CourseReportDetailsStore(String className,
                                    List<StudentCoursesDetailsLite> studentCoursesList,
                                    List<Course> optionalCourses,
                                    Map<UUID, List<UUID>> studentOptionalCourseMapping,
                                    Map<UUID, Map<CourseType, CourseCountDetails>> studentCourseCountMap) {
        this.className = className;
        this.studentCoursesList = studentCoursesList;
        this.optionalCourses = optionalCourses;
        this.studentOptionalCourseMapping = studentOptionalCourseMapping;
        this.studentCourseCountMap = studentCourseCountMap;
    }

    public CourseReportDetailsStore(String className,
                                    List<StudentCoursesDetailsLite> studentCoursesList) {
        this(className, studentCoursesList, Collections.emptyList(), Collections.emptyMap(), Collections.emptyMap());
    }

    public String getClassName() {
        return className;
    }

    public List<StudentCoursesDetailsLite> getStudentCoursesList() {
        return studentCoursesList;
    }

    public List<Course> getOptionalCourses() {
        return optionalCourses;
    }

    public Map<UUID, List<UUID>> getStudentOptionalCourseMapping() {
        return studentOptionalCourseMapping;
    }

    public Map<UUID, Map<CourseType, CourseCountDetails>> getStudentCourseCountMap() {
        return studentCourseCountMap;
    }
}
