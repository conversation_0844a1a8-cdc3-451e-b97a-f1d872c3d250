package com.lernen.cloud.core.api.configurations;

import com.lernen.cloud.core.api.common.SessionDisplayFormat;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamAdmitCardPreferences {

	public static final String ITEMS_PER_ROW = "items_per_row";
	public static final String ROWS_PER_PAGE = "rows_per_page";
	public static final String TITLE_TEXT = "title_text";
	public static final String BOLD_EXAM_NAME = "bold_exam_name";
	public static final String SESSION_DISPLAY_FORMAT = "session_display_format";
	public static final String ADMISSION_NUMBER_KEY_TEXT = "admission_number_key_text";
	public static final String LEFT_SIGNATURE_DESIGNATION_TEXT = "left_signature_designation_text";
	public static final String SIGNATURE_DESIGNATION_TEXT = "signature_designation_text";
	public static final String INCLUDE_CLASS_ROLL_NUMBER = "include_class_roll_number";
	public static final String HEADER_SECTION_TABLE_WIDTHS = "header_section_table_widths";
	public static final String INCLUDE_DIMENSION_IN_ADMIT_CARD = "include_dimension_in_admit_card";

	public static final String INSTITUTE_NAME_COLOR = "institute_name_color";
	public static final String LETTER_HEAD1_COLOR = "letter_head1_color";
	public static final String LETTER_HEAD2_COLOR = "letter_head2_color";
	public static final String SIGNATURE_TEXT_COLOR = "signature_text_color";
	public static final String STUDENT_DETAILS_KEY_COLOR = "student_details_key_color";
	public static final String STUDENT_DETAILS_VALUE_COLOR = "student_details_value_color";
	public static final String ADMIT_CARD_TEXT_FONT_COLOR = "admit_card_text_font_color";

	
	public static final String LOGO_WIDTH = "logo_width";
	public static final String LOGO_HEIGHT = "logo_height";

	public static final String PRINCIPAL_SIGNATURE_IMAGE_WIDTH = "principal_signature_image_width";
	public static final String PRINCIPAL_SIGNATURE_IMAGE_HEIGHT = "principal_signature_image_height";
	public static final String PRINCIPAL_SIGNATURE_PADDING = "principal_signature_padding";

	private int itemsPerRow;

	private int rowsPerPage;

	private String titleText;

	private boolean boldExamName;

	private SessionDisplayFormat sessionDisplayFormat;

	private String admissionNumberKeyText;

	private String leftSignatureDesignationText;

	private String signatureDesignationText;

	private boolean includeClassRollNumber;

	private List<Float> headerSectionTableWidths;

	private boolean includeDimensionInAdmitCard;

	private Float logoWidth;
	
	private Float logoHeight;
	private String instituteNameColor;
	private String letterHead1Color;
	private String letterHead2Color;
	private String signatureTextColor;
	private String studentDetailsKeyColor;
	private String studentDetailsValueColor;
	private String admitCardTextFontColor;

	private Float principalSignatureImageWidth;
	private Float principalSignatureImageHeight;
	private Float principalSignaturePadding;

	public static String getConfigType() {
		return "exam_admit_card_preferences";
	}

	public int getItemsPerRow() {
		return itemsPerRow;
	}

	public void setItemsPerRow(int itemsPerRow) {
		this.itemsPerRow = itemsPerRow;
	}

	public int getRowsPerPage() {
		return rowsPerPage;
	}

	public void setRowsPerPage(int rowsPerPage) {
		this.rowsPerPage = rowsPerPage;
	}

	public String getTitleText() {
		return titleText;
	}

	public void setTitleText(String titleText) {
		this.titleText = titleText;
	}

	public boolean isBoldExamName() {
		return boldExamName;
	}

	public void setBoldExamName(boolean boldExamName) {
		this.boldExamName = boldExamName;
	}

	public SessionDisplayFormat getSessionDisplayFormat() {
		return sessionDisplayFormat;
	}

	public void setSessionDisplayFormat(SessionDisplayFormat sessionDisplayFormat) {
		this.sessionDisplayFormat = sessionDisplayFormat;
	}

	public String getAdmissionNumberKeyText() {
		return admissionNumberKeyText;
	}

	public void setAdmissionNumberKeyText(String admissionNumberKeyText) {
		this.admissionNumberKeyText = admissionNumberKeyText;
	}

	public String getSignatureDesignationText() {
		return signatureDesignationText;
	}

	public void setSignatureDesignationText(String signatureDesignationText) {
		this.signatureDesignationText = signatureDesignationText;
	}

	public boolean isIncludeClassRollNumber() {
		return includeClassRollNumber;
	}

	public void setIncludeClassRollNumber(boolean includeClassRollNumber) {
		this.includeClassRollNumber = includeClassRollNumber;
	}

	public List<Float> getHeaderSectionTableWidths() {
		return headerSectionTableWidths;
	}

	public void setHeaderSectionTableWidths(List<Float> headerSectionTableWidths) {
		this.headerSectionTableWidths = headerSectionTableWidths;
	}

	public boolean isIncludeDimensionInAdmitCard() {
		return includeDimensionInAdmitCard;
	}

	public void setIncludeDimensionInAdmitCard(boolean includeDimensionInAdmitCard) {
		this.includeDimensionInAdmitCard = includeDimensionInAdmitCard;
	}

	public Float getLogoWidth() {
		return logoWidth;
	}

	public void setLogoWidth(Float logoWidth) {
		this.logoWidth = logoWidth;
	}

	public Float getLogoHeight() {
		return logoHeight;
	}

	public void setLogoHeight(Float logoHeight) {
		this.logoHeight = logoHeight;
	}

	public String getLeftSignatureDesignationText() {
		return leftSignatureDesignationText;
	}

	public void setLeftSignatureDesignationText(String leftSignatureDesignationText) {
		this.leftSignatureDesignationText = leftSignatureDesignationText;
	}

	// Getters and Setters for color fields
	public String getInstituteNameColor() {
		return instituteNameColor;
	}

	public void setInstituteNameColor(String instituteNameColor) {
		this.instituteNameColor = instituteNameColor;
	}

	public String getLetterHead1Color() {
		return letterHead1Color;
	}

	public void setLetterHead1Color(String letterHead1Color) {
		this.letterHead1Color = letterHead1Color;
	}

	public String getLetterHead2Color() {
		return letterHead2Color;
	}

	public void setLetterHead2Color(String letterHead2Color) {
		this.letterHead2Color = letterHead2Color;
	}

	public String getSignatureTextColor() {
		return signatureTextColor;
	}

	public void setSignatureTextColor(String signatureTextColor) {
		this.signatureTextColor = signatureTextColor;
	}

	public String getStudentDetailsKeyColor() {
		return studentDetailsKeyColor;
	}

	public void setStudentDetailsKeyColor(String studentDetailskeyColor) {
		this.studentDetailsKeyColor = studentDetailskeyColor;
	}

	public String getStudentDetailsValueColor() {
		return studentDetailsValueColor;
	}

	public void setStudentDetailsValueColor(String studentDetailsValueColor) {
		this.studentDetailsValueColor = studentDetailsValueColor;
	}

	public String getAdmitCardTextFontColor() {
		return admitCardTextFontColor;
	}

	public void setAdmitCardTextFontColor(String admitCardTextFontColor) {
		this.admitCardTextFontColor = admitCardTextFontColor;
	}
	public Float getPrincipalSignatureImageWidth() {
		return principalSignatureImageWidth;
	}

	public void setPrincipalSignatureImageWidth(Float principalSignatureImageWidth) {
		this.principalSignatureImageWidth = principalSignatureImageWidth;
	}

	public Float getPrincipalSignatureImageHeight() {
		return principalSignatureImageHeight;
	}

	public void setPrincipalSignatureImageHeight(Float principalSignatureImageHeight) {
		this.principalSignatureImageHeight = principalSignatureImageHeight;
	}

	public Float getPrincipalSignaturePadding() {
		return principalSignaturePadding;
	}

	public void setPrincipalSignaturePadding(Float principalSignaturePadding) {
		this.principalSignaturePadding = principalSignaturePadding;
	}

	@Override
	public String toString() {
		return "ExamAdmitCardPreferences{" +
				"itemsPerRow=" + itemsPerRow +
				", rowsPerPage=" + rowsPerPage +
				", titleText='" + titleText + '\'' +
				", boldExamName=" + boldExamName +
				", sessionDisplayFormat=" + sessionDisplayFormat +
				", admissionNumberKeyText='" + admissionNumberKeyText + '\'' +
				", leftSignatureDesignationText='" + leftSignatureDesignationText + '\'' +
				", signatureDesignationText='" + signatureDesignationText + '\'' +
				", includeClassRollNumber=" + includeClassRollNumber +
				", headerSectionTableWidths=" + headerSectionTableWidths +
				", includeDimensionInAdmitCard=" + includeDimensionInAdmitCard +
				", logoWidth=" + logoWidth +
				", logoHeight=" + logoHeight +
				", instituteNameColor='" + instituteNameColor + '\'' +
				", letterHead1Color='" + letterHead1Color + '\'' +
				", letterHead2Color='" + letterHead2Color + '\'' +
				", signatureColor='" + signatureTextColor + '\'' +
				", keyColor='" + studentDetailsKeyColor + '\'' +
				", valueColor='" + studentDetailsValueColor + '\'' +
				", admitCardTextFontColor='" + admitCardTextFontColor + '\'' +
				", principalSignatureImageWidth=" + principalSignatureImageWidth +
				", principalSignatureImageHeight=" + principalSignatureImageHeight +
				", principalSignaturePadding=" + principalSignaturePadding +
				'}';
	}
}
