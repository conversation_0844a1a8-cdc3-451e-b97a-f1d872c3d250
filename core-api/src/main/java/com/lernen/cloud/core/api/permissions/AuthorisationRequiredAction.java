package com.lernen.cloud.core.api.permissions;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lernen.cloud.core.api.user.Module;

/**
 *
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AuthorisationRequiredAction {

	/**
	 * Admission Module Actions
	 */
	REGISTER_STUDENT("Register Students", Module.ADMISSION,
			"You are not allowed to register students."),
	ENROLL_STUDENT("Enroll Students", Module.ADMISSION,
			"You are not allowed to enroll students."),
	RELIEVE_STUDENT("Relieve Students", Module.ADMISSION,
			"You are not allowed to relieve students."),
	MARK_STUDENT_NSO("Mark Students NSO", Module.ADMISSION,
			"You are not allowed to mark students NSO."),
	UPDATE_STUDENT("Update Student Information", Module.ADMISSION,
			"You are not allowed update student information."),
	VIEW_STUDENT_REPORTS("View Student Reports", Module.ADMISSION,
			"You are not allowed view student reports."),
	DOB_CHANGE("DOB Change ", Module.ADMISSION,
			"You are not allowed change DOB of students."),
	ADMISSION_EXCEL_REPORTS("Admission Excel Reports", Module.ADMISSION,
			"You are not allowed to access admission excel reports."),
	ADMISSION_PDF_REPORTS("Admission Pdf Reports", Module.ADMISSION,
			"You are not allowed to access admission pdf reports."),
	EDIT_SIBLINGS_DETAILS("Edit Siblings Details", Module.ADMISSION,
			"You are not allowed to edit siblings details."),
	ADMISSION_STATS_PERMISSION("Admission Stats Permission", Module.ADMISSION,
			"You are not allowed to view admission stats."),
	UPDATE_ENQUIRY_STATUS("Update Enquiry Status", Module.ADMISSION,
			"You don't have permission to update enquiry status"),
	SOFT_DELETE_STUDENT("Soft Delete Student", Module.ADMISSION, false,
			"You don't have permission to soft delete student!"),

	/**
	 * Diary Module Actions
	 */
	DIARY_REMARK_CATEGORY_TYPE("Diary Remark Category Type",Module.STUDENT_DIARY,
			"You are not allowed to edit remark category type."),
	DIARY_REMARK("Diary Remarks",Module.STUDENT_DIARY,
			"You are not allowed to edit diary remarks."),

	/**
	 * Complain Box Module Actions
	 */
	COMPLAIN_BOX_CATEGORY_TYPE("Complain Box Category Type",Module.COMPLAINT_BOX,
			"You are not allowed to edit Complain Box category type."),
	COMPLAIN_BOX_STANDARD_RESPONSE("Complain Box Standard Response",Module.COMPLAINT_BOX,
	"You are not allowed to edit Complain Box Standard Response."),


	/**
	 * Fee Module Actions
	 */
	FEE_CONFIGURATION("Fee Configurations", Module.FEES,
			"You don't have access to configure fees!"),
	FEE_HEAD_CONFIGURATION("Fee Head Configurations", Module.FEES,
			"You don't have access to configure fee heads!"),
	FEE_STRUCTURE("Fee Structure Configurations", Module.FEES,
			"You don't have access to configure fee structures!"),
	FEE_ASSIGNMENT("Fee Assignments", Module.FEES,
			"You don't have access to assign fees!"),
	DISCOUNT_CONFIGURATION("Discount Configurations", Module.FEES,
			"You don't have access to configure discount!"),
	DISCOUNT_ASSIGNMENT("Discount Assignments", Module.FEES,
			"You don't have access to assign discounts!"),
	FEE_PAYMENT_COLLECTION("Fee Payment Collection", Module.FEES,
			"You don't have access to collect fees!"),
	BACK_DATE_FEE_PAYMENT_COLLECTION("Back Date Fee Payment Collection", Module.FEES,
			"You don't have access to do back dated fee collection!"),
	FEE_PAYMENT_CANCELLATION("Fee Payment Cancellation", Module.FEES,
			"You don't have access to cancel fee payments!"),
	SEND_DUE_FEE_REMINDERS("Due Fee Reminders" , Module.FEES,
			"You don't have access to send due fee reminders!"),
	DEMAND_NOTICE("Fee Notice" , Module.FEES,
			"You don't have access to generate fee notices!"),
	DISCOUNT_STRUCTURE("Discount Structure" , Module.FEES,
			"You don't have access to configure discount structures!"),
	INSTANT_DISCOUNT_ASSIGNMENT("Instant Discount Assignments", Module.FEES,
			"You don't have access to assign istand discounts!"),
	FEE_STRUCTURE_CHANGE("Fee Structure Change", Module.FEES,
			"You don't have access to change fee structures!"),
	BULK_FEE_INVOICE_GENERATION("Bulk Fee Invoice Generation", Module.FEES,
			"You don't have access to generate bulk fee invoices!"),
	FEES_ORGANISATION_DASHBOARD_STATS("Fees Organisation Dashboard Stats", Module.FEES,
			"You don't have access to view organisation fees stats!"),
	FEES_DASHBOARD_STATS("Fees Dashboard Stats", Module.FEES,
			"You don't have access to view institute fees stats!"),
	FEES_HOME_STATS("Fees Home Stats", Module.FEES,
			"You don't have access fees stats!"),
	EDIT_FEE_FOLLOW_UP_TRAIL("Edit Fee Follow Up Trail",Module.FEES,
			"You don't have access edit fee follow ups!"),
	UPDATE_FEE_FOLLOW_UP_STATUS("Update Fee Follow Up Status",Module.FEES,
			"You don't have access to update fee follow up status!"),
	UPDATE_FINE_ASSIGNED_AMOUNT("Update Fine Assigned Amount", Module.FEES,
			"You don't have access to update fine assigned amount!"),
	COLLECT_SIBLING_FEES("Collect Sibling Fees", Module.FEES,
			"You don't have access to collect sibling fees!"),
	GENERATE_FEE_CHALLAN("Download Fees Challan", Module.FEES,
			"You don't have access to generate fees challan!"),

	/**
	 * Fee Report Permissions
	 */
	FEE_REPORTS("Reports in Excel", Module.FEES,
			"You don't have access to download fees reports in excel!"),
	FEE_PDF_REPORTS("Reports in Pdf", Module.FEES,
			"You don't have access to download fees reports in pdf!"),
	FEE_MULTI_SESSION_DUE_REPORT("Aggregated Multi Session Fees Due Report", Module.FEES),
	STUDENT_AGGREGATED_PAYMENT_REPORT("Aggregated Fees Report", Module.FEES),
	STUDENT_FEES_PAYMENT_REPORT("Student Fees Level Report", Module.FEES),
	FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT_REPORT("Fees Collection Report (Head Wise)", Module.FEES),
	TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL_REPORT("Transaction Summary", Module.FEES),

	TRANSACTION_SUMMARY_DETAILED_REPORT("Transaction Summary Detailed Report", Module.FEES),
	CANCELLED_TRANSACTIONS_DETAIL_REPORT("Cancelled Transactions Summary", Module.FEES),
	USER_DAY_LEVEL_COLLECTION_REPORT("User Day Level Collection Report", Module.FEES),
	STUDENT_LEDGER_REPORT("Enrolled Students' Ledger Report", Module.FEES),
	STUDENT_WALLET_TRANSACTIONS_REPORT("Student Wallet Transactions Report", Module.FEES),
	STUDENT_DISCOUNT_ASSIGNMENT_REPORT("Discount Assignment Report", Module.FEES),
	STUDENT_WALLET_BALANCE_REPORT("Student Wallet Balance Report", Module.FEES),
	TRANSACTION_MODE_AMOUNTS_REPORT("Fees Collection Report (Mode Wise)", Module.FEES),
	STUDENT_FEES_HEAD_PAYMENT_REPORT("Student Fees Head Level Report", Module.FEES),
	DISCOUNT_SUMMARY_REPORT("Discount Summary Report", Module.FEES,
			"You don't have access to download Discount Summary Report!"),
	UPDATE_WALLET_TRANSACTION_DETAILS("Update Wallet Transaction Details (Opening Balance)", Module.FEES,
			"You don't have access to update wallet transaction details!"),

	/**
	 * Institute Management Actions
	 */
	UPDATE_STANDARD("Update Standard",Module.INSTITUTE_MANAGEMENT,
			"You don't have access to update standard."),
	UPDATE_STANDARD_SECTIONS("Update Standard Sections",Module.INSTITUTE_MANAGEMENT),
	DELETE_STANDARD_SECTIONS("Delete Standard Sections",Module.INSTITUTE_MANAGEMENT),
	ADD_STANDARD_SECTIONS("Add Standard Sections",Module.INSTITUTE_MANAGEMENT),
	EDIT_BULK_STANDARD("Edit Bulk Standard",Module.INSTITUTE_MANAGEMENT,
			"You don't have access to edit bulk standard."),
	STANDARD_STAFF_ASSIGNMENT("Edit Standard Staff Assignment",Module.INSTITUTE_MANAGEMENT,
			"You don't have access to update standard staff assignment"),

	UPDATE_INSTITUTE_DETAILS("Update Institute Details", Module.INSTITUTE_MANAGEMENT,
			"You don't have access to update institute details"),
	EDIT_INSTITUTE_IMAGES("Edit Institute Images", Module.INSTITUTE_MANAGEMENT,
			"You don't have access to edit institute images"),
	UPDATE_COUNTER_VALUE("Update counter value", Module.INSTITUTE_MANAGEMENT,
			"You don't have access to update counter value"),
	EDIT_STANDARD_SESSION_IMAGES("Edit Standard Session Documents", Module.INSTITUTE_MANAGEMENT,
			"You don't have access to edit standard session documents!"),
	UPDATE_INSTITUTE_ACTIVE_STATUS("Update institute active status", Module.INSTITUTE_MANAGEMENT,
			"You don't have access to update institute active status"),
	EDIT_INSTITUTE_BANK_DETAILS("Edit Institute Bank Account",Module.INSTITUTE_MANAGEMENT),

	/**
	 * Transport Module Actions
	 */
	TRANSPORT_CONFIGURATION("Transport Configurations", Module.TRANSPORT),
	TRANSPORT_VEHICLE("Transport Vehicle", Module.TRANSPORT),
	TRANSPORT_VEHICLE_TYPE("Transport Vehicle Type", Module.TRANSPORT),
	TRANSPORT_AREA("Transport Stoppage", Module.TRANSPORT),
	TRANSPORT_PARENT_AREA("Transport Area", Module.TRANSPORT),
	TRANSPORT_AREA_AMOUNT("Transport Area Amount", Module.TRANSPORT),
	TRANSPORT_STAFF("Transport Staff", Module.TRANSPORT, false),
	TRANSPORT_ROUTE("Transport Route", Module.TRANSPORT),
	TRANSPORT_FEE_CONFIGURATION("Transport Fee Configuration", Module.TRANSPORT),
	DELETE_TRANSPORT_ASSIGNMENT("Delete Transport Assignment", Module.TRANSPORT),
	SEND_TRANSPORT_MOBILE_NOTIFICATIONS("Send Transport Mobile Notifications", Module.TRANSPORT),
	RECEIVE_VEHICLE_DOCUMENT_RENEWAL_REMINDER("Receive Vehicle Document Renewal Reminder", Module.TRANSPORT),

	/**
	 * Transport Report Permissions
	 */
	TRANSPORT_EXCEL_REPORTS("Transport Reports in Excel", Module.TRANSPORT),
	TRANSPORT_PDF_REPORTS("Transport Reports in Pdf", Module.TRANSPORT),
	STUDENT_TRANSPORT_ATTENDANCE("Mark Student Transport Attendance", Module.TRANSPORT, "You are not authorised to mark student transport attendance"),

	/**
	 * Exam Module Actions
	 */
	UPDATE_MARKS("Update Marks", Module.EXAMINATION,
			"You don't have access to update exam marks."),
	UPDATE_STANDARD_GRADING_FLAG("Update Standard Grading", Module.EXAMINATION,
			"You don't have access to update standard grading."),
	UPDATE_SUBMITTED_MARKS("Update Submitted Marks", Module.EXAMINATION,
			"You don't have access to update submitted marks"),
	UPDATE_REPORT_CARD_DETAILS("Update report card variables", Module.EXAMINATION,
			"You don't have access to update exam report card variables data"),
	GENERATE_EXAM_REPORT_CARD("Generate Exam Report", Module.EXAMINATION,
			"You don't have access to generate exam report card."),
	GENERATE_CLASS_TEACHER_EXAM_REPORT_CARD("Generate Class Teacher Exam Report Card", Module.EXAMINATION,
			"You don't have access to generate exam report card."),
	GENERATE_EXAM_BULK_REPORT_CARD("Generate Bulk Exam Report Cards", Module.EXAMINATION,
			"You don't have access to generate exam bulk report cards."),
	SEND_EXAMINATION_SMS("Send Exam SMS", Module.EXAMINATION,
			"You don't have access to send exam sms."),
	EXAM_EXCEL_REPORTS("Exam Excel Reports", Module.EXAMINATION,
			"You don't have access to view exam excel reports."),
	EXAM_PDF_REPORTS("Exam Pdf Reports", Module.EXAMINATION,
			"You don't have access to view exam pdf reports."),
	ADD_DATESHEET("Add Datesheet", Module.EXAMINATION,
			"You don't have access to add datesheet."),
	UPDATE_DATESHEET("Update Datesheet", Module.EXAMINATION,
			"You don't have access to update datesheet."),
	DELETE_DATESHEET("Delete Datesheet", Module.EXAMINATION,
			"You don't have access to delete datesheet."),
	PUBLISH_EXAM_MARKS("Publish Exam Marks", Module.EXAMINATION,
			"You don't have access to publish exam marks."),
	GENERATE_TEACHER_WISE_RESULT_ANALYSIS_REPORT("Teacher Wise Result Analysis Report", Module.EXAMINATION,
			"You don't have access to view Teacher Wise Result Analysis Report."),
	GENERATE_DIVISION_WISE_CLASS_RESULT_ANALYSIS_REPORT("Division Wise Class Result Analysis Report", Module.EXAMINATION,
			"You don't have access to view Division Wise Class Result Analysis Report."),
	GENERATE_GRADE_WISE_CLASS_RESULT_ANALYSIS_REPORT("Grade Wise Class Result Analysis Report", Module.EXAMINATION,
			"You don't have access to view Grade Wise Class Result Analysis Report."),
	GENERATE_SUBJECT_WISE_RANK_REPORT("Subjectwise Rank Report", Module.EXAMINATION,
			"You don't have access to view Subjectwise Rank Report."),
	GENERATE_OVERALL_RANK_REPORT("Overall Rank Report", Module.EXAMINATION,
			"You don't have access to view Overall Rank Report."),
	GENERATE_RESULT_SUMMARY_REPORT("Result Summary Report", Module.EXAMINATION,
			"You don't have access to view Result Summary Report."),
	GENERATE_MULTIPLE_EXAM_MARKS_REPORT("Multiple Exam Marks Report", Module.EXAMINATION,
			"You don't have access to view Multiple Exam Marks Report."),
	GENERATE_EXAM_CONSOLIDATED_REPORT("Generate Exam Consolidated Report", Module.EXAMINATION,
			"You don't have access to generate exam consolidated report."),
	GENERATE_GROWTH_REPORT("Generate Growth Report", Module.EXAMINATION,
			"You don't have access to view Exam Growth Report."),
	BULK_UPDATE_MARKS_STATUS("Bulk Update Marks Status", Module.EXAMINATION,
			"You don't have access to update bulk exam marks status."),
	ADD_EXAM("Add Exam", Module.EXAMINATION,
			"You don't have access to add exams."),
	UPDATE_EXAM_DIMENSIONS("Update Exam Dimensions", Module.EXAMINATION,
			"You don't have access to update exams dimensions."),
	UPDATE_EXAM_BASIC_INFO("Update ExamBasic Info", Module.EXAMINATION,
			"You don't have access to update exam basic info."),
	DELETE_EXAM("Delete Exam", Module.EXAMINATION,
			"You don't have access to delete exams."),
	UPDATE_EXAM_COURSES("Update Exam Courses", Module.EXAMINATION,
			"You don't have access to update exam courses."),
	CLEAR_STUDENT_MARKS("Clear Student Marks", Module.EXAMINATION,
			"You don't have access to clear student marks."),
	EDIT_PERSONALITY_TRAITS("Edit Personality Traits", Module.EXAMINATION,
			"You don't have access to edit Personality Traits"),
	EDIT_STUDENT_PERSONALITY_TRAITS("Edit Student Personality Traits", Module.EXAMINATION,
			"You don't have access to edit Student Personality Traits"),
	ACCESS_ALL_CLASSES_EXAMINATION_MARKS_DATA("Access/Update All Classes Examination Marks Data", Module.EXAMINATION,
			"You are not allowed to access/update this class and course exam marks data."),
	UPDATE_STUDENT_REPORT_CARD_STATUS("Update Student Report Card Status", Module.EXAMINATION,
			"You don't have access to update student report card display status!"),
	RENAME_GRADES("Update Grade Name Details", Module.EXAMINATION,
			"You don't have access to update grade name details"),
	UPDATE_GRADE("Update Grade Entire Details", Module.EXAMINATION,
			"You don't have access to update grade entire details"),
	ADD_GRADE("Add Grade Details", Module.EXAMINATION,
			"You don't have access to add grade details"),
	DELETE_GRADE("Delete Grade Details", Module.EXAMINATION,
			"You don't have access to Delete grade details"),
	GENERATE_STUDENT_HPC_REPORT_CARD("Generate Student HPC Report Card", Module.EXAMINATION,
			"You don't have access to download student HPC Report Card!"),
	GENERATE_BULK_HPC_REPORT_CARD("Generate Bulk HPC Report Card", Module.EXAMINATION,
			"You don't have access to download bulk HPC Report Card!"),
	GENERATE_CLASS_HPC_REPORT_CARD("Generate Class HPC Report Card", Module.EXAMINATION,
			"You don't have access to download class HPC Report Card!"),
	UPDATE_STUDENT_HPC_REPORT_CARD_PUBLISH_STATUS("Publish Status Student HPC Report Card", Module.EXAMINATION,
	"You don't have access to update publish status of HPC Report Card!"),
	UPDATE_SUBMITTED_HPC_DATA("Update Submitted HPC Data", Module.EXAMINATION,
			"You don't have access to update submitted HPC Data!"),
	EDIT_EXAM_MASTER_CONFIG("Edit Exam Master Config", Module.EXAMINATION,
			"You don't have access to edit exam master config!"),
	/**
	 *
	 */
	EDIT_CLASS_COURSES("Edit Class Courses", Module.COURSES,
			"You don't have access to edit class courses"),
	ADD_CLASS_COURSES("Add Class Course", Module.COURSES,
			"You don't have access to add class courses"),
	OPTIONAL_COURSE_ASSIGNMENT("Assign Optional Courses", Module.COURSES,
			"You don't have permission to assign optional courses"),
	/**
	 * Courses Report Actions
	 */
	COURSE_EXCEL_REPORTS("Course Excel Reports", Module.COURSES, 
			"You don't have access to view course excel reports."),
	COURSE_PDF_REPORTS("Course PDF Reports", Module.COURSES, 
			"You don't have access to view course pdf reports."),
	GENERATE_STUDENT_COURSE_DETAILS_REPORT("Student Course Details Reports", Module.COURSES, 
			"You don't have access to view student course details reports."),
	GENERATE_STUDENT_OPTIONAL_COURSE_DETAILS_REPORT("Student Optional Course Details Reports", Module.COURSES, 
			"You don't have access to view student optional course details reports."),
	/**
	 * Lecture Module Actions
	 */
	UPLOAD_LECTURE("Upload Lecture", Module.LECTURE_MANAGEMENT),
	UPDATE_LECTURE("Update Lecture", Module.LECTURE_MANAGEMENT),
	DELETE_LECTURE("Delete Lecture", Module.LECTURE_MANAGEMENT),

	/**
	 * Homework Module Actions
	 */
	BULK_STUDENT_HOMEWORK_SUBMISSION_UPDATE("Bulk Student Homework Submission Update",Module.HOMEWORK_MANAGEMENT),
	ACCESS_ALL_CLASSES_HOMEWORK_DATA("Access/Update All Classes Homework Data", Module.HOMEWORK_MANAGEMENT,
			"You are not allowed to access/update this class and course homework data."),
	HOMEWORK_EXCEL_REPORTS("Homework Excel Reports", Module.HOMEWORK_MANAGEMENT,
			"You don't have access to view homework excel reports."),
	HOMEWORK_PDF_REPORTS("Homework Pdf Reports", Module.HOMEWORK_MANAGEMENT,
			"You don't have access to view homework pdf reports."),
	GENERATE_HOMEWORK_CONSOLIDATED_REPORT("Generate Homework Consolidated Report", Module.HOMEWORK_MANAGEMENT,
			"You don't have access to generate homework consolidated report."),

	/**
	 * Default Actions
	 */
	RESTRICT_WORKING_HOURS("Restrict Working Hours", Module.DEFAULT),


	/**
	 * Salary Module Actions
	 */
	PAY_HEAD_CONFIGURATION("Pay Head Configurations", Module.SALARY_MANAGEMENT),
	SALARY_STRUCTURE_CONFIGURATION("Salary Structure Configurations", Module.SALARY_MANAGEMENT),
	GENERATE_PAYSLIP("Generate Payslip", Module.SALARY_MANAGEMENT),
	PROVIDE_ADVANCE("Provide Advance", Module.SALARY_MANAGEMENT),
	CANCEL_PAYSLIP("Cancel Payslip", Module.SALARY_MANAGEMENT),
	CANCEL_ADVANCE("Cancel Advance", Module.SALARY_MANAGEMENT),
	SALARY_REPORTS("Salary Reports", Module.SALARY_MANAGEMENT),
	SALARY_CYCLE("Salary Cycle", Module.SALARY_MANAGEMENT),

	/**
	 * Timetable Module Actions
	 */
	ADD_SHIFT_CONFIGURATION("Add Shift Configurations", Module.TIMETABLE_MANAGEMENT),
	UPDATE_SHIFT_CONFIGURATION("Update Shift Configurations", Module.TIMETABLE_MANAGEMENT),
	DELETE_SHIFT_CONFIGURATION("Delete Shift Configurations", Module.TIMETABLE_MANAGEMENT),
	ADD_SUBJECT_GROUP_CONFIGURATION("Add Subject Group Configurations", Module.TIMETABLE_MANAGEMENT, false),
	ADD_BATCH_DETAILS("Add Batch Configurations", Module.TIMETABLE_MANAGEMENT, false),
	ASSIGN_STUDENT_BATCHES("Assign Student Batches", Module.TIMETABLE_MANAGEMENT, false),
	ADD_TIMETABLE_CONFIGURATION("Add timetable Configurations", Module.TIMETABLE_MANAGEMENT),
	UPDATE_TIMETABLE_CONFIGURATION("Update timetable Configurations", Module.TIMETABLE_MANAGEMENT),
	DELETE_TIMETABLE_CONFIGURATION("Delete timetable Configurations", Module.TIMETABLE_MANAGEMENT),
	ASSIGN_SUBSTITUTION("Assign Substitution", Module.TIMETABLE_MANAGEMENT, false),
	UPDATE_SUBSTITUTION("Update Substitution", Module.TIMETABLE_MANAGEMENT, false),
	ACTIVITY_CONFIGURATIONS("Activity Configurations", Module.TIMETABLE_MANAGEMENT),
	FACULTY_ASSIGNMENT_CONFIGURATIONS("Faculty Assignment Configurations", Module.TIMETABLE_MANAGEMENT),
	TIMETABLE_EXCEL_REPORTS("Timetable Excel Reports", Module.TIMETABLE_MANAGEMENT),
	TIMETABLE_PDF_REPORTS("Timetable Pdf Reports", Module.TIMETABLE_MANAGEMENT),
	DAY_WISE_TIMETABLE_REPORT("Day wise Timetable Report", Module.TIMETABLE_MANAGEMENT),

	/**
	 * Student management Module Actions
	 */
	UPDATE_STUDENT_SESSION_DETAILS("Update Student Session Details", Module.STUDENT_MANAGEMENT),
	PROMOTE_STUDENTS("Promote Students", Module.STUDENT_MANAGEMENT),
	STUDENT_CLASS_CHANGE("Student Class Change", Module.STUDENT_MANAGEMENT),
	GENERATE_STUDENT_IDENTITY_CARD("Generate Student I-Card", Module.STUDENT_MANAGEMENT),
	GENERATE_BULK_STUDENT_IDENTITY_CARD("Generate Bulk Student I-Cards", Module.STUDENT_MANAGEMENT),
	GENERATE_CLASS_IDENTITY_CARD("Generate Class I-Cards", Module.STUDENT_MANAGEMENT),
	GENERATE_INDIVIDUAL_STUDENT_DYNAMIC_DOCUMENT("Generate Individual Student Dynamic Document", Module.STUDENT_MANAGEMENT, false),
	GENERATE_BULK_STUDENT_DYNAMIC_DOCUMENT("Generate Bulk Student Dynamic Document", Module.STUDENT_MANAGEMENT, false),
	GENERATE_CLASS_DYNAMIC_DOCUMENT("Generate Class Dynamic Document", Module.STUDENT_MANAGEMENT, false),
	BULK_UPDATE_STUDENT("Bulk Update Student Details", Module.STUDENT_MANAGEMENT),
	GENERATE_CLASS_REPEATERS_REPORT("Class Repeaters Report",Module.STUDENT_MANAGEMENT),
	STUDENT_MANAGEMENT_EXCEL_REPORT("Student Management Excel Report",Module.STUDENT_MANAGEMENT),
	STUDENT_MANAGEMENT_PDF_REPORT("Student Management Pdf Report",Module.STUDENT_MANAGEMENT),
	UPDATE_STUDENT_TAG_DETAILS("Update Student Tag Details" ,Module.STUDENT_MANAGEMENT, "You don't have access to update student tags"),



	/**
	 * Staff Attendance Module Actions
	 */
	SAVE_STAFF_ATTENDANCE("Save Staff Attendance", Module.STAFF_ATTENDANCE),
	EDIT_STAFF_ATTENDANCE_TIME("Edit Staff Attendance Timing", Module.STAFF_ATTENDANCE),
	STAFF_LEAVE_APPLICATION_ADMIN_ACCESS("Staff Leave Admin Access", Module.STAFF_ATTENDANCE,
			"You don't have staff leave admin access."),
	GENERATE_STAFF_ATTENDANCE_EXCEL_REPORT("Generate Staff Attendance Excel Report", Module.STAFF_ATTENDANCE,
			"You don't have access to download Staff Attendance Excel Reports"),
	GENERATE_STAFF_ATTENDANCE_PDF_REPORT("Edit Staff Attendance Timing", Module.STAFF_ATTENDANCE,
			"You don't have access to download Staff Attendance Pdf Reports"),
	GENERATE_STAFF_ATTENDANCE_DETAILS_IN_A_DAY_REPORT("Generate Staff Attendance Details in a day Report", Module.STAFF_ATTENDANCE,
			"You don't have access to view Staff Attendance Details in a day Report"),
	GENERATE_STAFF_MONTHLY_ATTENDANCE_SUMMARY_REPORT("Staff Monthly Attendance Summary Report", Module.STAFF_ATTENDANCE,
			"You don't have access to view Staff Monthly Attendance Summary Report"),
	GENERATE_STAFF_ATTENDANCE_DETAILS_REPORT("Staff Attendance Details Report", Module.STAFF_ATTENDANCE,
			"You don't have access to view Staff Attendance Details Report"),

	/**
	 * Student Attendance Module Actions
	 */
	ADD_STUDENT_ATTENDANCE_TYPE("Add Student Attendance Type", Module.ATTENDANCE),
	DELETE_STUDENT_ATTENDANCE_TYPE("Delete Student Attendance Type", Module.ATTENDANCE),
	UPDATE_STUDENT_ATTENDANCE_TYPE("Update Student Attendance Type", Module.ATTENDANCE),
	UPDATE_BACKDATED_ATTENDANCE("Update Backdated Attendance", Module.ATTENDANCE,
			"You are not allowed to update back date attendance."),
	REVIEW_STUDENT_LEAVES("Review Student Leaves", Module.ATTENDANCE,
			"You are not allowed to review student leaves."),
	ACCESS_ALL_CLASSES_ATTENDANCE_DATA("Student Attendance Data Admin Access", Module.ATTENDANCE,
			"You don't have student attendance data admin access."),
	ACCESS_ALL_CLASSES_STUDENT_LEAVE_DATA("Student Leave Data Admin Access", Module.ATTENDANCE,
			"You don't have student leave data admin access."),

//	UPDATE_ATTENDANCE_AS_PER_STUDENT_LEAVES("Update Attendance As Per Leaves", Module.ATTENDANCE,
//			"You are not allowed to update student attendances as per leaves."),
	/**
	 * Attendance Report Permissions
	 */
	ATTENDANCE_EXCEL_REPORTS("Attendance Reports in Excel", Module.ATTENDANCE),
	ATTENDANCE_PDF_REPORTS("Attendance Reports in Pdf", Module.ATTENDANCE),


	/**
	 * Communication Module Actions
	 */
	SEND_EXCEL_SMS("Send Excel SMS", Module.COMMUNICATION),
	SEND_MOBILE_NOTIFICATIONS("Send Mobile Notifications", Module.COMMUNICATION),
	SET_STAFF_DEFAULT_PASSWORD("Set Default Password of Staffs", Module.COMMUNICATION,
			"You don't have access to set default password for staffs!"),

	/**
	 * Staff Management Module Actions
	 */
	GENERATE_STAFF_IDENTITY_CARD("Generate Staff I-Card", Module.STAFF_MANAGEMENT),
	STAFF_EXCEL_REPORTS("Staff Excel Reports", Module.STAFF_MANAGEMENT),
	STAFF_PDF_REPORTS("Staff Pdf Reports", Module.STAFF_MANAGEMENT),
	STAFF_BASIC_REPORTS("Staff Basic Reports", Module.STAFF_MANAGEMENT),
	BULK_UPDATE_STAFF("Bulk Update Staff Details",Module.STAFF_MANAGEMENT),
	ADD_PRIMARY_STAFF_EMAIL("Add Primary Staff Email",Module.STAFF_MANAGEMENT,
			"You don't have access to add primary staff email address."),
	UPDATE_PRIMARY_STAFF_EMAIL("Update Primary Staff Email",Module.STAFF_MANAGEMENT,
			"You don't have access to update primary staff email address."),

	/**
	 * Inventory Module Actions
	 */
	EDIT_PRODUCTS("Edit Products", Module.STORE),
	ADD_PURCHASE_TRANSACTIONS("Add and return Purchase Transactions", Module.STORE),
	ADD_TRADE_TRANSACTIONS("Add sale and sale return Transactions", Module.STORE),
	ADD_ISSUE_TRANSACTIONS("Add Issue Transactions", Module.STORE),
	DELETE_TRANSACTIONS("Delete Transactions", Module.STORE),
	INVENTORY_REPORTS("Inventory Excel Reports", Module.STORE),
	INVENTORY_PDF_REPORTS("Inventory Pdf reports", Module.STORE),
	ADD_BACK_DATE_TRANSACTIONS("Add Backdated transactions", Module.STORE),
	PURCHASE_WISE_INVENTORY_MOVEMENT_REPORT("Product Wise Inventory Movement Report", Module.STORE, "You don't have access to view product wise inventory movement report."),

	/**
	 * Front Desk Module Actions
	 */
	ADD_GATE_PASS_DETAILS("Add Gate Pass Details", Module.FRONT_DESK),
	GENERATE_GATE_PASS_PDF("Generate Gate Pass Pdf", Module.FRONT_DESK),
	UPDATE_GATE_PASS_STATUS("Update Gate Pass Status", Module.FRONT_DESK),

	/**
	 * Income & Expense Module Actions
	 */
	EDIT_CATEGORY("Edit Category", Module.INCOME_EXPENSE),
	EDIT_ENTITY("Edit Entity", Module.INCOME_EXPENSE),
	ADD_TRANSACTIONS("Add Transactions", Module.INCOME_EXPENSE),
	ADD_INCOME_EXPENSE_BACK_DATE_TRANSACTIONS("Add Income-Expense Backdated Transactions", Module.INCOME_EXPENSE),
	UPDATE_TRANSACTION_STATUS("Cancel Transaction", Module.INCOME_EXPENSE),
	INCOME_EXPENSE_ADVANCE_REPORTS("Income Expense Advance Reports", Module.INCOME_EXPENSE),
	INCOME_EXPENSE_PDF_REPORTS("Income Expense PDF Reports", Module.INCOME_EXPENSE),
	INCOME_EXPENSE_EXCEL_REPORTS("Income Expense Excel reports", Module.INCOME_EXPENSE),

	/**
	 * Mobile App Management Module Actions
	 */
	EDIT_USER_MODULE_PERMISSIONS("Edit User Module Permissions", Module.MOBILE_APPLICATION_MANAGEMENT),
	STUDENT_SESSION_RESTRICTION("Student academic session restriction", Module.MOBILE_APPLICATION_MANAGEMENT,
			"You do not have access to restrict student's sessions!"),


	/**
	 * Staff Diary Module Actions
	 */
	EDIT_STAFF_DIARY_REMARK_CATEGORY("Edit Staff Diary Remark Category", Module.STAFF_DIARY,
			"You are not allowed to edit staff diary remark category."),
	EDIT_STAFF_DIARY_STANDARD_REMARKS("Edit Staff Diary Standard Remarks", Module.STAFF_DIARY,
			"You are not allowed to edit staff diary standard remarks."),
	EDIT_STAFF_DIARY_REMARKS("Edit Staff Diary Remarks", Module.STAFF_DIARY,
			"You are not allowed to edit staff diary remarks."),
	STAFF_DIARY_ADMIN_ACCESS("Staff Diary Admin Access", Module.STAFF_DIARY,
			"You don't have admin access to staff diary."),


	/**
	 *  User Management Module Action
	 */
	GENERATE_USER_CREDENTIALS_PDF("Generate User Credentials Pdf", Module.USER_MANAGEMENT,
			"You don't have access to generate user credentials pdf"),
	UPDATE_BULK_USER_DETAILS("Update Bulk User Details", Module.USER_MANAGEMENT,
			"You don't have access to update bulk user details"),


	/**
	 *  Parents Appointment Module Action
	 */
	PARENT_APPOINTMENT_ADMIN_ACCESS("Parent Appointment Admin Access", Module.PARENTS_APPOINTMENT,
			"You do not have admin access to appointment details."),


	/**
	 *  Visitors Module Action
	 */
	VISITORS_DESK_ADMIN_ACCESS("View All Visitor Details", Module.VISITORS_DESK,
			"You do not have access to view all visitor details."),
	GENERATE_VISITOR_IDENTITY_CARD("Generate Visitor I-Card", Module.VISITORS_DESK),
	ALL_NOTIFICATION_PERMISSION("All Notification Permission", Module.VISITORS_DESK),

	/**
	 * Library Management Module Action
	 */
	UPDATE_BOOK_DETAILS_ACCESS("Update Book Details Access", Module.LIBRARY_MANAGEMENT, "You don't have access to update book details"),
	BOOK_ISSUE_ACCESS("Book Issue Access", Module.LIBRARY_MANAGEMENT,
	"You do not have book issue access. "),

	/** 
	 * Library Reports Permissions
	 */

	GENERATE_LIBRARY_BOOKS_REPORT("Generate Library Books Report", Module.LIBRARY_MANAGEMENT, "You don't have access to generate library books report"),
	DOWNLOAD_LIBRARY_EXCEL("Download Library Excel Report", Module.LIBRARY_MANAGEMENT, "You don't have access to download Library Excel Reports"),
	DOWNLOAD_LIBRARY_PDF("Download Library PDF Report", Module.LIBRARY_MANAGEMENT, "You don't have access to download Library Pdf Reports"),

	/**
	 * Online Assessment
	*/
	ASSESSMENT_MODIFICATION_ACCESS("Assessment Modification Access", Module.ONLINE_ASSESSMENT,"You do not have permission to add, update, or delete assessments"),
	QUESTION_MODIFICATION_ACCESS("Question Modification Access", Module.ONLINE_ASSESSMENT,"You do not have permission to add, update, or delete questions"),
	/**
	 * Hostel Management Module Actions
	 */
	ADD_HOSTEL_GATE_PASS_DETAILS("Add Hostel Gate Pass Details", Module.HOSTEL_MANAGEMENT),
	UPDATE_HOSTEL_GATE_PASS_STATUS("Update Hostel Gate Pass Status", Module.HOSTEL_MANAGEMENT),
  EDIT_HOSTEL_DETAILS("Edit Hostel Details", Module.HOSTEL_MANAGEMENT, "You do not have permission to edit hostel details"),
	/*
	 * Study Tracker
	 */
	ADD_STUDY_TRACKER_TYPE("Add Study Tracker Type details", Module.STUDY_TRACKER, "You do not have permission To add Study Tracker Type"),
	EDIT_STUDY_TRACKER_STUDENT_DATA(" Edit Study Tracker Student Data", Module.STUDY_TRACKER, "You do not have permission To edit Study Tracker Student Data"),
	DOWNLOAD_STUDY_TRACKER_EXCEL("Download Study Tracker Excel Report", Module.STUDY_TRACKER,
			"You don't have access to download Study Tracker Excel Reports"),
	DOWNLOAD_STUDY_TRACKER_PDF("Download Study Tracker PDF Report", Module.STUDY_TRACKER,
			"You don't have access to download Study Tracker Pdf Reports"),
	GENERATE_STUDY_TRACKER_DAILY_REPORT("Generate Study Tracker Daily Report", Module.STUDY_TRACKER,
			"You don't have access to generate Study Tracker Daily Report"),
	GENERATE_STUDY_TRACKER_MONTHLY_AVG_REPORT("Generate Study Tracker Monthly Avg Report", Module.STUDY_TRACKER,
			"You don't have access to generate Study Tracker Monthly Avg Report"),
	/**
	 * Student Finance Module Actions
	 */
   	ADD_SERVICE_BACK_DATE_TRANSACTIONS("Add Backdated transactions", Module.STUDENT_FINANCE),
	GENERATE_STUDENT_FINANCE_EXCEL_REPORT("Student Finance Excel Reports", Module.STUDENT_FINANCE,
			"You are not allowed to access student finance excel reports."),
	GENERATE_STUDENT_FINANCE_PDF_REPORT("Student Finance Pdf Reports", Module.STUDENT_FINANCE,
			"You are not allowed to access student finance pdf reports."),
	STUDENT_FINANCE_DETAILS_REPORT("Student Finance Details Reports", Module.STUDENT_FINANCE,
			"You are not allowed to access student finance details reports."),
	STUDENT_FINANCE_SUMMARY_REPORT("Student Finance Summary Reports", Module.STUDENT_FINANCE,
			"You are not allowed to access student finance summary reports."),
	CANCEL_STUDENT_FINANCE_TRANSACTION("Cancel Student Finance Transaction", Module.STUDENT_FINANCE,
			"You are not allowed to cancel student finance transaction."),

	/**
	 * Calender Module Actions
	 */
	//Need to change holiday calender to calender in module enum.
	EDIT_EVENT_DETAILS("Edit Event Details", Module.HOLIDAY_CALENDAR, "You do not have permission to edit event details"),

	/*
	 * Lesson Plan
	 */
	LESSON_PLAN_ADMIN_ACCESS("Add, Update, Delete and Clone access for all the classes", Module.LESSON_PLAN, "You do not have permission to add, update, or delete lesson plan"),
	LESSON_PLAN_STAFF_ACCESS("Add, Update and Delete access for all the classes taught by the staff", Module.LESSON_PLAN, "You do not have permission to add, update, or delete lesson plan"),
	STAFF_RESPONSE_ACCESS("Access TO Add Response To Lesson Plan", Module.LESSON_PLAN, "You do not have Permission to add Response to the Lesson Plan");



	private final String displayName;

	private final Module module;

	private final boolean visibleInUI;

	private final String errorMessage;

	private AuthorisationRequiredAction(String displayName, Module module) {
		this.displayName = displayName;
		this.module = module;
		this.visibleInUI = true;
		this.errorMessage = null;
	}

	AuthorisationRequiredAction(String displayName, Module module, boolean visibleInUI) {
		this.displayName = displayName;
		this.module = module;
		this.visibleInUI = visibleInUI;
		this.errorMessage = null;
	}

	AuthorisationRequiredAction(String displayName, Module module, boolean visibleInUI, String errorMessage) {
		this.displayName = displayName;
		this.module = module;
		this.visibleInUI = visibleInUI;
		this.errorMessage = errorMessage;
	}

	AuthorisationRequiredAction(String displayName, Module module, String errorMessage) {
		this.displayName = displayName;
		this.module = module;
		this.visibleInUI = true;
		this.errorMessage = errorMessage;
	}

	public String getDisplayName() {
		return displayName;
	}

	public Module getModule() {
		return module;
	}

	public String getPermissionId() {
		return this.name();
	}

	public boolean isVisibleInUI() {
		return visibleInUI;
	}

	public String getErrorMessage() {
		return errorMessage;
	}
}
