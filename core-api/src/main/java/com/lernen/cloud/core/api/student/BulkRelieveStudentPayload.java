package com.lernen.cloud.core.api.student;

import java.util.Set;

/**
 * Payload for bulk student relieve/NSO
 */
public class BulkRelieveStudentPayload {

    private StudentStatus updatedStudentStatus;
    private StudentRelievePayload relievePayload;
    private Set<String> studentAdmissionNumbers;
    private String date;  // date input in DD/MM/YYYY format

    public BulkRelieveStudentPayload() {
    }

    public BulkRelieveStudentPayload(StudentStatus updatedStudentStatus, StudentRelievePayload relievePayload, Set<String> studentAdmissionNumbers) {
        this.updatedStudentStatus = updatedStudentStatus;
        this.relievePayload = relievePayload;
        this.studentAdmissionNumbers = studentAdmissionNumbers;
    }

    public StudentStatus getUpdatedStudentStatus() {
        return updatedStudentStatus;
    }

    public void setUpdatedStudentStatus(StudentStatus updatedStudentStatus) {
        this.updatedStudentStatus = updatedStudentStatus;
    }

    public StudentRelievePayload getRelievePayload() {
        return relievePayload;
    }

    public void setRelievePayload(StudentRelievePayload relievePayload) {
        this.relievePayload = relievePayload;
    }

    public Set<String> getStudentAdmissionNumbers() {
        return studentAdmissionNumbers;
    }

    public void setStudentAdmissionNumbers(Set<String> studentAdmissionNumbers) {
        this.studentAdmissionNumbers = studentAdmissionNumbers;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Override
    public String toString() {
        return "BulkRelieveStudentPayload{" +
                "updatedStudentStatus=" + updatedStudentStatus +
                ", relievePayload=" + relievePayload +
                ", studentAdmissionNumbers=" + studentAdmissionNumbers +
                ", date='" + date + '\'' +
                '}';
    }
}
