package com.lernen.cloud.core.api.library.report;

import java.util.List;
import java.util.UUID;

import com.lernen.cloud.core.api.library.IndividualBookDetails;

/**
 * Represents detailed book data for library reports (with accession details)
 */
public class LibraryReportDetailedData {
    
    private final UUID bookId;
    private final String bookTitle;
    private final String isbn;
    private final String author;
    private final String genre;
    private final String publication;
    private final String publisher;
    private final String edition;
    private final int publishYear;
    private final String language;
    private final int totalCopies;
    private final Integer addedAt;
    private final List<IndividualBookDetails> individualBookDetails;

    public LibraryReportDetailedData(UUID bookId, String bookTitle, String isbn, String author,
                                    String genre, String publication, String publisher,
                                    String edition, int publishYear, String language,
                                    int totalCopies, Integer addedAt,
                                    List<IndividualBookDetails> individualBookDetails) {
        this.bookId = bookId;
        this.bookTitle = bookTitle;
        this.isbn = isbn;
        this.author = author;
        this.genre = genre;
        this.publication = publication;
        this.publisher = publisher;
        this.edition = edition;
        this.publishYear = publishYear;
        this.language = language;
        this.totalCopies = totalCopies;
        this.addedAt = addedAt;
        this.individualBookDetails = individualBookDetails;
    }

    public UUID getBookId() {
        return bookId;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public String getIsbn() {
        return isbn;
    }

    public String getAuthor() {
        return author;
    }

    public String getGenre() {
        return genre;
    }

    public String getPublication() {
        return publication;
    }

    public String getPublisher() {
        return publisher;
    }

    public String getEdition() {
        return edition;
    }

    public int getPublishYear() {
        return publishYear;
    }

    public String getLanguage() {
        return language;
    }

    public int getTotalCopies() {
        return totalCopies;
    }

    public Integer getAddedAt() {
        return addedAt;
    }

    public List<IndividualBookDetails> getIndividualBookDetails() {
        return individualBookDetails;
    }

    @Override
    public String toString() {
        return "LibraryReportDetailedData [bookId=" + bookId + ", bookTitle=" + bookTitle + 
               ", isbn=" + isbn + ", author=" + author + ", genre=" + genre + 
               ", publication=" + publication + ", publisher=" + publisher + 
               ", edition=" + edition + ", publishYear=" + publishYear + 
               ", language=" + language + ", totalCopies=" + totalCopies + 
               ", addedAt=" + addedAt + ", individualBookDetails=" + individualBookDetails + "]";
    }
}
