package com.lernen.cloud.core.api.course;

import java.util.List;


public class StudentCoursesDetailsLiteRow {

    private final int academicSessionId;
    private final StudentInfoDetails studentInfoDetails;
    private final Course course;

    // Basic constructor (single course)
    public StudentCoursesDetailsLiteRow(int academicSessionId, StudentInfoDetails studentInfoDetails, Course course) {
        this.academicSessionId = academicSessionId;
        this.studentInfoDetails = studentInfoDetails;
        this.course = course;
    }

    public StudentCoursesDetailsLiteRow(int academicSessionId, StudentInfoDetails studentInfoDetails, Course course, List<Course> courses, List<Course> scholasticCourses, List<Course> coScholasticCourses) {
        this.academicSessionId = academicSessionId;
        this.studentInfoDetails = studentInfoDetails;
        this.course = course;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public StudentInfoDetails getStudentInfoDetails() {
        return studentInfoDetails;
    }

    public Course getCourse() {
        return course;
    }


    @Override
    public String toString() {
        return "StudentCoursesDetailsLiteRow{" +
                "academicSessionId=" + academicSessionId +
                ", studentInfoDetails=" + studentInfoDetails +
                ", course=" + course +
                '}';
    }
}
