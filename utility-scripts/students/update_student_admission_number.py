import requests
import json

headers =  {"Content-Type": "application/json"}


# BASE_URL = "http://127.0.0.1:8080/data-server"
# ACCESS_TOKEN = "b8475b44-2334-4717-9504-8deeef55616c"

# BASE_URL = "https://api.embrate.com"
# ACCESS_TOKEN = ""

GET_STUDENT_API_URL = BASE_URL + "/2.0/student/institute-students/{institute_id}/{academic_session_id}?access_token={access_token}"
UPDATE_STUDENT_DATA_API_URL = BASE_URL + "/2.0/student/management/{standard_id}/field-data?institute_id={institute_id}&academic_session_id={academic_session_id}&user_id={user_id}&access_token={access_token}"
UPDATE_STUDENT_DATA_WITHOUT_STANDARD_ID_API_URL = BASE_URL + "/2.0/student/management/field-data?institute_id={institute_id}&academic_session_id={academic_session_id}&user_id={user_id}&access_token={access_token}"

def get_students(institute_id, academic_session_id):
    response = requests.get(GET_STUDENT_API_URL.format(institute_id = institute_id,  academic_session_id = academic_session_id, access_token = ACCESS_TOKEN), headers=headers)
    data = json.loads(response.content)
    standard_student_map = {}
    for student in data:
        standard_id = student['studentAcademicSessionInfoResponse']['standard']['standardId']
        admission_number = student['studentBasicInfo']['admissionNumber'].lower()
        if standard_id in standard_student_map:
            standard_student_map[standard_id][admission_number] = student
        else:
            standard_student_map[standard_id] = {admission_number : student}

    return standard_student_map

def get_students_without_standard_id(institute_id, academic_session_id):
    response = requests.get(GET_STUDENT_API_URL.format(institute_id = institute_id,  academic_session_id = academic_session_id, access_token = ACCESS_TOKEN), headers=headers)
    data = json.loads(response.content)
    standard_student_map = {}
    for student in data:
        admission_number = student['studentBasicInfo']['admissionNumber'].lower()
        standard_student_map[admission_number] = student
       

    return standard_student_map

def get_multi_field_update_payload_without_standard(old_new_mapping, student_map):
    student_payload_map = {}
    processed_admission_numbers = set()
    
    # Define the field order to ensure consistency
    FIELD_ORDER = ['AADHAR_NUMBER', 'PRESENT_ADDRESS', 'CATEGORY', 'CASTE', 'FATHER_CONTACT_NUMBER', 'MOTHER_CONTACT_NUMBER', 'WHATSAPP_NUMBER']
    
    for admission_number in student_map:
        student = student_map[admission_number]
        
        if admission_number in old_new_mapping:
            field_updates = old_new_mapping[admission_number]
            
            # Get all fields that have values
            available_fields = [field for field, value in field_updates.items() if value]
            
            # Sort fields according to predefined order
            ordered_fields = [field for field in FIELD_ORDER if field in available_fields]
            
            # Build field value list in the correct order
            field_value_list = []
            for field_name in ordered_fields:
                field_value_list.append({
                    'field': field_name, 
                    'value': field_updates[field_name]
                })
            
            if field_value_list:  # Only add student if there are fields to update
                student_list = [{
                    'studentId': student['studentId'], 
                    'fieldValueList': field_value_list
                }]

                student_payload_map[admission_number] = {
                    'dataType': 'BASIC_DATA', 
                    'requiredFields': ordered_fields,  # Use the same ordered fields
                    'studentDataList': student_list
                }
                
            # Mark this admission number as processed
            processed_admission_numbers.add(admission_number)

    # Find admission numbers in old_new_mapping but not processed
    not_found_admission_numbers = []
    for admission_number in old_new_mapping:
        if admission_number not in processed_admission_numbers:
            not_found_admission_numbers.append(admission_number)
    
    return student_payload_map, not_found_admission_numbers

def get_data_update_payload(old_new_mapping, standard_student_map):
    standard_payload_map = {}
    for standard_id in standard_student_map:
        student_map = standard_student_map[standard_id]
        student_list = []
        for admission_number in student_map:
            student = student_map[admission_number]
            new_admission_number = None
            if admission_number in old_new_mapping:
                new_admission_number = old_new_mapping[admission_number]

            if not (new_admission_number is None):
                student_list.append({'studentId' : student['studentId'], 'fieldValueList' : [{'field' : 'ADMISSION_NUMBER', 'value' : new_admission_number}]})

        standard_payload_map[standard_id] = {'dataType' : 'BASIC_DATA', 'requiredFields' : ['ADMISSION_NUMBER'] , 'studentDataList' : student_list}

    return standard_payload_map

def update_student_data_without_standard_id(institute_id, academic_session_id, user_id, payload):
    try:
        print("\n UPDATING STUDENT DATA = " + str(payload) + "\n")
        response = requests.post(UPDATE_STUDENT_DATA_WITHOUT_STANDARD_ID_API_URL.format(institute_id = institute_id, academic_session_id = academic_session_id, user_id = user_id, access_token = ACCESS_TOKEN), data=json.dumps(payload), headers=headers)
        if response.status_code == 200:
            print("Success for payload ")
            return True
        else:
            print("Error " + str(response.status_code) + " for " + str(payload) + " error = " + str(response.text))
            return False
    except Exception as e:
        print("Error for payload " + str(payload))
        return False

def update_student_admission_number(institute_id, academic_session_id, user_id, standard_id, payload):
    try:
        print("\n UPDATING STUDENT DATA = " + str(payload) + "\n")
        response = requests.post(UPDATE_STUDENT_DATA_API_URL.format(standard_id = standard_id, institute_id = institute_id, academic_session_id = academic_session_id, user_id = user_id, access_token = ACCESS_TOKEN), data=json.dumps(payload), headers=headers)
        if response.status_code == 200:
            print("Success for payload ")
            return True
        else:
            print("Error " + str(response.status_code) + " for " + str(payload) + " error = " + str(response.text))
            return False
    except Exception as e:
        print("Error for payload " + str(payload))
        return False


def read_mapping(file_path):
    f = open(file_path, 'r')
    old_new_mapping = {}
    for line in f:
        tokens = line.split(",")
        old_admission_number = tokens[0]
        new_admission_number = tokens[1]
        old_new_mapping[old_admission_number.strip().lower()] = new_admission_number.strip()

    return old_new_mapping


def read_multi_field_mapping(file_path):
    f = open(file_path, 'r')
    old_new_mapping = {}
    for line in f:
        # tokens = line.split(",")
        tokens = line.split("|")
        admission_number = tokens[0].strip().lower()
        # father_contact = tokens[1].strip() if len(tokens) > 1 else ""
        # mother_contact = tokens[2].strip() if len(tokens) > 2 else ""
        present_address = tokens[1].strip() if len(tokens) > 1 else ""
        category = tokens[2].strip() if len(tokens) > 2 else ""
        caste = tokens[3].strip() if len(tokens) > 3 else ""
        aadhar_number = tokens[4].strip() if len(tokens) > 4 else ""
        # whatsapp_number = tokens[3].strip() if len(tokens) > 3 else ""

        # **** WARNING :- if you add new fields, add them here and also in FIELD_ORDER ******

        field_updates = {}
        # if father_contact:
        #     field_updates["FATHER_CONTACT_NUMBER"] = father_contact
        # if mother_contact:
        #     field_updates["MOTHER_CONTACT_NUMBER"] = mother_contact
        if present_address:
            field_updates["PRESENT_ADDRESS"] = present_address
        if category:
            field_updates["CATEGORY"] = category
        if caste:
            field_updates["CASTE"] = caste
        if aadhar_number:
            field_updates["AADHAR_NUMBER"] = aadhar_number
        # if whatsapp_number:
        #     field_updates["WHATSAPP_NUMBER"] = whatsapp_number
            
        if field_updates:  # Only add if there are fields to update
            old_new_mapping[admission_number] = field_updates
    
    f.close()
    return old_new_mapping


def run(file_path, institute_id, academic_session_id, user_id):
    #old_new_mapping = read_mapping(file_path)
    old_new_mapping = read_multi_field_mapping(file_path)
    #standard_student_map = get_students(institute_id, academic_session_id)
    student_map = get_students_without_standard_id(institute_id, academic_session_id)
    #standard_payload_map = get_data_update_payload(old_new_mapping, standard_student_map)
    standard_payload_map, not_found_admission_numbers = get_multi_field_update_payload_without_standard(old_new_mapping, student_map)
    count = 0
    success_count = 0
    failure_count = 0
    for standard_id in standard_payload_map:
        print("\n ------ executing update for " + standard_id +" ------------ \n ")
        standard_payload = standard_payload_map[standard_id]
        print(standard_payload)
        #success = update_student_admission_number(institute_id, academic_session_id, user_id, standard_id, standard_payload)
        success = update_student_data_without_standard_id(institute_id, academic_session_id, user_id, standard_payload)
        #success = True
        count += 1
        if success:
            success_count += 1
        else:
            failure_count += 1

    print("total {count}, success {success_count}, failure {failure_count}".format(count = count, success_count = success_count, failure_count = failure_count))
    count_standard_student_data(standard_payload_map)
    print("Mapping count : " + str(len(old_new_mapping)))
    
    # Display not found admission numbers at the end
    if not_found_admission_numbers:
        print(f"\n⚠️ Admission numbers in mapping but not found in student database:")
        for admission_number in not_found_admission_numbers:
            print(f"   - {admission_number}")
        print(f"Total not found: {len(not_found_admission_numbers)}")

def count_standard_student_data(standard_payload_map):
    total = 0
    for standard_id in standard_payload_map:
        standard_payload = standard_payload_map[standard_id]
        size = len(standard_payload['studentDataList'])
        total += size
        print(" standard_id {standard_id}, size = {size}".format(standard_id = standard_id, size = size))

    print("total = {total}".format(total = total))

run('C:/Users/<USER>/Downloads/10356_data_change.csv', 10356, 307, "27fcd651-cf72-41aa-bff8-ca481058750b")
