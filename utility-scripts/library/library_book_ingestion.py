import pandas as pd
import uuid
from datetime import datetime
import logging
import json
from typing import List, Dict, Tuple, Optional, Any
import sys
import os
import requests

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LibraryBookIngestionScript:
    def __init__(self, institute_id: int, user_id: str = None, api_config: Dict = None):
        self.institute_id = institute_id
        self.user_id = user_id
        self.api_config = api_config
        
        # Initialize API headers if config provided
        if self.api_config:
            self.headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': f"Bearer {self.api_config['access_token']}"
            }
            
            # Processing stats
            self.processing_stats = {
                'api_calls_made': 0,
                'entities_created': 0,
                'books_created': 0
            }
        
        self.errors = []
        self.warnings = []
        self.success_count = 0
        
        # Carry-forward variables
        self.last_bill_number = None
        self.last_date = None
        self.last_source = None
        
        # Deduplication caches
        self.existing_books = {}  # {(isbn, normalized_title): (book_id, copies)}
        self.existing_authors = {}  # {author_name: author_id}
        self.existing_publications = {}  # {publication_name: publication_id}
        self.existing_vendors = {}  # {vendor_name: vendor_id}
        self.existing_library_types = {}  # {library_type_name: library_type_id}
        
        # Validation caches
        self.isbn_to_title = {}  # {isbn: {'normalized': title, 'original': title}} for validation
        self.used_accession_numbers = set()  # {accession_number} for uniqueness
        
        # Entity collections for separate files
        self.all_authors = []
        self.all_publications = []
        self.all_vendors = []
        
        # Mock DAO - Replace with actual DAO implementation
        self.library_dao = None  # Initialize with actual LibraryDao instance
        
        # ISBN statistics for validation
        self.isbn_stats = {}

    def parse_voucher_date(self, voucher_data: str) -> Tuple[Optional[str], Optional[int]]:
        """
        Parse voucher data with carry-forward logic for empty/quote patterns
        Handles:
        - "694 /31-5-2023" → bill_number="694", date="31-5-2023" converted to timestamp
        - " ''            ''" → use last_bill_number, last_date
        - Empty/None → bill_number=None, date=None
        
        Returns: (bill_number, date_of_purchase_timestamp)
        """
        if not voucher_data or voucher_data.strip() == "":
            return None, None
        
        # Check for quote pattern indicating "same as above"
        if "''" in voucher_data or voucher_data.strip() in ["''", "' '", '"', '""']:
            return self.last_bill_number, self.last_date
        
        # Parse format: "694 /31-5-2023"
        if '/' in voucher_data:
            parts = voucher_data.split('/')
            if len(parts) == 2:
                bill_number = parts[0].strip()
                date_str = parts[1].strip()
                try:
                    # Convert DD-MM-YYYY to timestamp
                    date_obj = datetime.strptime(date_str, "%d-%m-%Y")
                    timestamp = int(date_obj.timestamp())
                    
                    # Update carry-forward variables
                    self.last_bill_number = bill_number
                    self.last_date = timestamp
                    
                    return bill_number, timestamp
                except ValueError:
                    logger.warning(f"Invalid date format: {date_str}")
                    return None, None
        
        # If no valid pattern found, keep previous values
        return self.last_bill_number, self.last_date
    
    def parse_source_field(self, source_data: str) -> Optional[str]:
        """
        Parse SOURCE field with carry-forward logic
        """
        if not source_data or source_data.strip() == "":
            return None
        
        # Check for quote pattern indicating "same as above"
        if "''" in source_data or source_data.strip() in ["''", "' '", '"', '""']:
            return self.last_source
        
        # Valid source data
        vendor_name = source_data.strip()
        self.last_source = vendor_name
        return vendor_name
    
    def parse_price(self, price_data: str) -> Optional[float]:
        """
        Parse price data like "50/-", "160/-", "UK 6.99", etc.
        Returns: decimal value or None
        """
        if not price_data or price_data.strip() == "":
            return None
        
        # Remove "/-" suffix and any spaces
        price_str = price_data.replace("/-", "").replace(" ", "").strip()
        
        # Handle cases with currency codes like "UK 6.99"
        if any(char.isalpha() for char in price_str):
            # Extract numeric part after removing alphabetic characters
            import re
            numeric_part = re.sub(r'[A-Za-z]', '', price_str)
            if numeric_part:
                price_str = numeric_part
            else:
                logger.warning(f"Invalid price format: {price_data}")
                return None
        
        try:
            return float(price_str)
        except ValueError:
            logger.warning(f"Invalid price format: {price_data}")
            return None
    
    def parse_author_name(self, author_data: str) -> Optional[str]:
        """
        Handle special cases in author field
        - "none" → return None (don't create author)
        - Valid name → return author_name
        """
        if not author_data or author_data.strip().lower() in ["none", "", "null"]:
            return None
        
        return author_data.strip()
    
    def validate_required_fields(self, row_data: Dict, row_index: int) -> List[str]:
        """
        Validate mandatory fields and return error list
        Required fields:
        - book_details: institute_id, book_title, isbn_number, no_of_copies
        - individual_book_details: institute_id, book_id, accession_number
        """
        errors = []
        
        # Enhanced ISBN validation
        isbn_number = row_data.get('isbn_number', '').strip()
        if not isbn_number or isbn_number.upper() in ['NAN', 'NULL', 'NONE', '', 'N/A', 'NA'] or pd.isna(isbn_number):
            errors.append(f"Row {row_index}, Accession: {row_data.get('accession_number', 'N/A')} - Missing or invalid ISBN")
        
        if not row_data.get('book_title'):
            errors.append(f"Row {row_index}, Accession: {row_data.get('accession_number', 'N/A')} - Missing Book Title")
        
        if not row_data.get('accession_number'):
            errors.append(f"Row {row_index} - Missing Accession Number")
        
        # Additional validations
        if isbn_number and row_data.get('book_title'):
            if not self.validate_isbn_and_title(isbn_number, row_data['book_title'], row_index):
                errors.append(f"Row {row_index} - ISBN validation failed")
        
        if row_data.get('accession_number'):
            if not self.validate_accession_number(row_data['accession_number'], row_index):
                errors.append(f"Row {row_index} - Accession number validation failed")
        
        return errors
    
    def validate_isbn_and_title(self, isbn_number: str, book_title: str, row_index: int) -> bool:
        """
        Validate that ISBN is unique for each book title
        Uses normalized titles for comparison but shows original titles in error messages
        Returns: True if valid, False if error
        """
        normalized_title = self.normalize_title(book_title)
        
        if isbn_number in self.isbn_to_title:
            existing_data = self.isbn_to_title[isbn_number]
            existing_normalized_title = existing_data['normalized']
            existing_original_title = existing_data['original']
            
            if existing_normalized_title != normalized_title:
                self.errors.append(f"Row {row_index} - ISBN {isbn_number} already exists with different title: '{existing_original_title}' vs '{book_title}' (normalized comparison)")
                return False
        else:
            # Store both normalized and original titles
            self.isbn_to_title[isbn_number] = {
                'normalized': normalized_title,
                'original': book_title
            }
        
        return True

    def validate_accession_number(self, accession_number: str, row_index: int) -> bool:
        """
        Validate that accession number is unique
        Returns: True if valid, False if error
        """
        if accession_number in self.used_accession_numbers:
            self.errors.append(f"Row {row_index} - Duplicate accession number: {accession_number}")
            return False
        
        self.used_accession_numbers.add(accession_number)
        return True

    def create_or_get_author(self, author_name: str) -> Optional[str]:
        """
        Create or get author ID with deduplication
        """
        if not author_name:
            return None
        
        # Check if author already exists in cache
        if author_name in self.existing_authors:
            return self.existing_authors[author_name]
        
        # Generate new author ID and cache it
        author_id = str(uuid.uuid4())
        self.existing_authors[author_name] = author_id
        
        # Store for separate file
        author_payload = {
            "authorId": author_id,
            "authorName": author_name,
            "nationality": None,
            "dateOfBirth": None,
            "associatedGeners": None,
            "shortBiography": None
        }
        
        self.all_authors.append(author_payload)
        print(f"Creating Author: {author_name} -> {author_id}")
        
        return author_id
    
    def parse_publication_data(self, publication_data: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Parse publication data to extract publication name and address
        Handles:
        - "Maple Press.Pvt.Ltd,Noida -u.p." → name="Maple Press.Pvt.Ltd", address="Noida -u.p."
        - "MANNU GRAPHICS NEW DELHI" → name="MANNU GRAPHICS", address="NEW DELHI"
        - "TINY- TOT PUBLICATION NOIDA UP" → name="TINY- TOT PUBLICATION", address="NOIDA UP"
        - "SIMPLE NAME" → name="SIMPLE NAME", address=None
        - Empty/None → name=None, address=None
        
        Returns: (publication_name, address)
        """
        if not publication_data or publication_data.strip() == "":
            return None, None
        
        publication_data = publication_data.strip()
        
        # Check if there's a comma separator
        if ',' in publication_data:
            parts = publication_data.split(',', 1)  # Split only on first comma
            publication_name = parts[0].strip()
            address = parts[1].strip() if len(parts) > 1 and parts[1].strip() else None
            return publication_name, address
        
        # No comma found, check if last words look like city/address
        words = publication_data.split()
        
        if len(words) <= 1:
            # Single word, treat as publication name only
            return publication_data, None
        
        # Common city/state patterns (case insensitive) - ordered by specificity
        multi_word_city_patterns = [
            'NEW DELHI', 'NAVI MUMBAI', 'GREATER NOIDA', 'NOIDA UP', 'DELHI NCR',
            'MUMBAI MAHARASHTRA', 'CHENNAI TAMIL NADU', 'BANGALORE KARNATAKA',
            'HYDERABAD TELANGANA', 'PUNE MAHARASHTRA', 'KOLKATA WEST BENGAL'
        ]
        
        single_word_city_patterns = [
            'DELHI', 'MUMBAI', 'KOLKATA', 'CHENNAI', 'BANGALORE', 'HYDERABAD',
            'PUNE', 'AHMEDABAD', 'JAIPUR', 'LUCKNOW', 'KANPUR', 'NAGPUR', 'INDORE',
            'BHOPAL', 'VISAKHAPATNAM', 'PATNA', 'VADODARA', 'GHAZIABAD', 'LUDHIANA',
            'AGRA', 'NASHIK', 'FARIDABAD', 'MEERUT', 'RAJKOT', 'KALYAN', 'VASAI',
            'VARANASI', 'SRINAGAR', 'AURANGABAD', 'DHANBAD', 'AMRITSAR',
            'ALLAHABAD', 'RANCHI', 'HOWRAH', 'COIMBATORE', 'JABALPUR', 'GWALIOR',
            'VIJAYAWADA', 'JODHPUR', 'MADURAI', 'RAIPUR', 'KOTA', 'GUWAHATI',
            'CHANDIGARH', 'SOLAPUR', 'HUBLI', 'TIRUCHIRAPPALLI', 'BAREILLY', 'MYSORE',
            'TIRUPPUR', 'GURGAON', 'ALIGARH', 'JALANDHAR', 'BHUBANESWAR', 'SALEM',
            'NOIDA', 'UP', 'MAHARASHTRA', 'KARNATAKA', 'TELANGANA', 'GUJARAT',
            'RAJASTHAN', 'HARYANA', 'PUNJAB', 'BIHAR', 'ODISHA', 'KERALA',
            'ASSAM', 'JHARKHAND', 'UTTARAKHAND', 'HIMACHAL', 'PRADESH'
        ]
        
        # First check for multi-word city patterns (more specific)
        for pattern in multi_word_city_patterns:
            pattern_words = pattern.split()
            pattern_length = len(pattern_words)
            
            if len(words) >= pattern_length:
                # Check if the last N words match this pattern
                last_words = ' '.join(words[-pattern_length:]).upper()
                if last_words == pattern:
                    publication_name = ' '.join(words[:-pattern_length]).strip()
                    address = ' '.join(words[-pattern_length:]).strip()
                    return publication_name if publication_name else publication_data, address
        
        # Then check for single-word city patterns
        for i in range(1, min(3, len(words) + 1)):  # Check last 1-2 words only
            potential_address = ' '.join(words[-i:]).upper()
            if any(city.upper() == potential_address for city in single_word_city_patterns):
                publication_name = ' '.join(words[:-i]).strip()
                address = ' '.join(words[-i:]).strip()
                return publication_name if publication_name else publication_data, address
        
        # No city pattern found, treat entire string as publication name
        return publication_data, None

    def create_or_get_publication(self, publication_data: str) -> Optional[str]:
        """
        Create or get publication ID with deduplication
        """
        if not publication_data:
            return None
        
        # Parse publication name and address
        publication_name, address = self.parse_publication_data(publication_data)
        
        if not publication_name:
            return None
        
        # Check if publication already exists in cache
        if publication_name in self.existing_publications:
            return self.existing_publications[publication_name]
        
        # Generate new publication ID and cache it
        publication_id = str(uuid.uuid4())
        self.existing_publications[publication_name] = publication_id
        
        # Store for separate file
        publication_payload = {
            "publicationId": publication_id,
            "publicationName": publication_name,
            "address": address,
            "phoneNumber": None,
            "email": None,
            "website": None
        }
        
        self.all_publications.append(publication_payload)
        print(f"Creating Publication: {publication_name} -> {publication_id}")
        
        return publication_id
    
    def create_or_get_vendor(self, vendor_name: str) -> Optional[str]:
        """
        Create or get vendor ID with deduplication
        """
        if not vendor_name:
            return None
        
        # Check if vendor already exists in cache
        if vendor_name in self.existing_vendors:
            return self.existing_vendors[vendor_name]
        
        # Generate new vendor ID and cache it
        vendor_id = str(uuid.uuid4())
        self.existing_vendors[vendor_name] = vendor_id
        
        # Store for separate file
        vendor_payload = {
            "vendorId": vendor_id,
            "vendorName": vendor_name,
            "address": None,
            "phoneNumber": None,
            "email": None,
            "gstNumber": None,
            "accountType": None,
            "bankName": None,
            "accountHolderName": None,
            "accountNumber": None,
            "ifscCode": None
        }
        
        self.all_vendors.append(vendor_payload)
        print(f"Creating Vendor: {vendor_name} -> {vendor_id}")
        
        return vendor_id
    
    def create_or_get_library_type(self, library_type_name: str = None) -> Optional[str]:
        """
        Create or get library type ID with deduplication
        If no library_type_name provided, return None (skip library type)
        """
        # If no library type name provided, skip library type creation
        if not library_type_name or library_type_name.strip() == "":
            return None
        
        library_type_name = library_type_name.strip()
        
        # Check if library type already exists in cache
        if library_type_name in self.existing_library_types:
            return self.existing_library_types[library_type_name]
        
        # Mock DAO call to check if library type exists in database
        # existing_library_type_id = self.library_dao.getLibraryTypeByName(self.institute_id, library_type_name)
        # if existing_library_type_id:
        #     self.existing_library_types[library_type_name] = existing_library_type_id
        #     return existing_library_type_id
        
        # Generate new library type ID and cache it
        library_type_id = str(uuid.uuid4())
        self.existing_library_types[library_type_name] = library_type_id
        
        library_type_payload = {
            "libraryTypeName": library_type_name
        }
        
        print(f"Creating Library Type: {library_type_name} -> {library_type_id}")
        print(f"Creating Library Type Payload: {json.dumps(library_type_payload, indent=2)}")
        
        # Mock DAO call
        # success = self.library_dao.addLibraryType(self.institute_id, library_type_payload)
        
        return library_type_id

    def check_existing_book(self, isbn_number: str, book_title: str) -> Tuple[bool, Optional[str], int]:
        """
        Check if book exists by ISBN or title with caching
        Returns: (exists, book_id, current_copies)
        """
        book_key = (isbn_number, book_title)
        
        # Check cache first
        if book_key in self.existing_books:
            book_id, current_copies = self.existing_books[book_key]
            return True, book_id, current_copies
        
        # Mock implementation - replace with actual DAO query
        return False, None, 0

    def update_book_copies(self, book_key: Tuple[str, str], book_id: str, additional_copies: int):
        """
        Update book copies count in cache
        """
        if book_key in self.existing_books:
            current_book_id, current_copies = self.existing_books[book_key]
            self.existing_books[book_key] = (current_book_id, current_copies + additional_copies)
        else:
            self.existing_books[book_key] = (book_id, additional_copies)
    
    def create_book_payload(self, processed_row: Dict, entity_ids: Dict) -> Dict:
        """
        Create BookPayloadWrapper for new book
        """
        book_details_payload = {
            "bookId": None,  # Will be generated by DAO
            "instituteId": self.institute_id,
            "genreId": entity_ids.get('genre_id'),
            "bookTitle": processed_row['book_title'],
            "bookNo": processed_row.get('book_no'),
            "authorId": entity_ids.get('author_id'),
            "isbn": processed_row['isbn_number'],
            "publicationId": entity_ids.get('publication_id'),
            "publisherId": entity_ids.get('publisher_id'),
            "edition": processed_row.get('edition'),
            "noOfCopies": processed_row['no_of_copies'],
            "publishYear": processed_row.get('publish_year'),
            "language": processed_row.get('language'),
            "typeOfBinding": processed_row.get('type_of_binding'),
            "numberOfPages": processed_row.get('number_of_pages')
        }
        
        individual_book_details = {
            "accessionId": None,  # Will be generated by DAO
            "instituteId": self.institute_id,
            "bookId": None,  # Will be set by DAO
            "accessionNumber": processed_row['accession_number'],
            "rack": processed_row.get('rack'),
            "purchasePrice": processed_row.get('price'),  # Changed from 'price' to 'purchasePrice'
            "mrp": None,  # Not available in Excel
            "billNumber": processed_row.get('bill_number'),
            "dateOfPurchase": processed_row.get('date_of_purchase'),
            "addedOn": int(datetime.now().timestamp()),  # Current timestamp
            "vendorId": entity_ids.get('vendor_id'),
            "status": "ACTIVE",
            "volume": processed_row.get('volume'),
            "libraryTypeId": entity_ids.get('library_type_id'),
            "remarks": processed_row.get('remarks')
        }
        
        book_payload_wrapper = {
            "bookDetailPayload": book_details_payload,
            "individualBookDetailsPayloads": [individual_book_details],
            "tags": []  # Empty list instead of set
        }
        
        return book_payload_wrapper
    
    def create_individual_book_payload(self, processed_row: Dict, book_id: str, entity_ids: Dict) -> Dict:
        """
        Create individual book details payload for existing book
        """
        individual_book_details = {
            "accessionId": None,  # Will be generated by DAO
            "instituteId": self.institute_id,
            "bookId": book_id,
            "accessionNumber": processed_row['accession_number'],
            "rack": processed_row.get('rack'),
            "purchasePrice": processed_row.get('price'),  # Changed from 'price' to 'purchasePrice'
            "mrp": None,  # Not available in Excel
            "billNumber": processed_row.get('bill_number'),
            "dateOfPurchase": processed_row.get('date_of_purchase'),
            "addedOn": int(datetime.now().timestamp()),  # Current timestamp
            "vendorId": entity_ids.get('vendor_id'),
            "status": "ACTIVE",
            "volume": processed_row.get('volume'),
            "libraryTypeId": entity_ids.get('library_type_id'),
            "remarks": processed_row.get('remarks')
        }
        
        return individual_book_details
    
    def safe_int_parse(self, value: str, field_name: str) -> Optional[int]:
        """
        Safely parse integer values, handling invalid cases
        """
        if not value or pd.isna(value):
            return None
        
        value_str = str(value).strip().upper()
        
        # Handle common invalid values
        if value_str in ['NONE', 'NULL', '', '-', 'N/A', 'NA']:
            return None
        
        # Remove "/-" suffix for number_of_pages (similar to price handling)
        if field_name == 'number_of_pages' and '/-' in value_str:
            value_str = value_str.replace("/-", "").strip()
        
        # Handle price-like values (e.g., "200/-", "UK 6.99") but allow decimals for years
        if '/-' in value_str or (any(char.isalpha() for char in value_str) and field_name != 'publish_year'):
            logger.warning(f"Invalid {field_name} format: {value} - skipping")
            return None
        
        try:
            parsed_value = int(float(value_str))  # Convert via float first to handle decimals
            
            # Handle 2-digit years for publish_year field
            if field_name == 'publish_year' and 0 <= parsed_value <= 99:
                # Assume years 0-30 are 2000s, 31-99 are 1900s
                if parsed_value <= 30:
                    parsed_value += 2000
                else:
                    parsed_value += 1900
            
            return parsed_value
        except (ValueError, TypeError):
            logger.warning(f"Invalid {field_name} format: {value} - skipping")
            return None
    
    def process_book_entry(self, processed_row: Dict) -> bool:
        """
        Process a single book entry
        """
        try:
            # Create optional entities
            entity_ids = {
                'author_id': self.create_or_get_author(processed_row.get('author_name')),
                'publication_id': self.create_or_get_publication(processed_row.get('publication_data')),
                'vendor_id': self.create_or_get_vendor(processed_row.get('vendor_name')),
                'library_type_id': self.create_or_get_library_type(),
                'genre_id': None,  # Not provided in Excel
                'publisher_id': None  # Not provided in Excel
            }
            
            # Check if book exists
            exists, book_id, current_copies = self.check_existing_book(
                processed_row['isbn_number'], 
                processed_row['book_title']
            )
            
            if exists:
                # Update existing book copies and add individual book details
                new_copies = current_copies + processed_row['no_of_copies']
                
                # Create individual book payload
                individual_book_payload = self.create_individual_book_payload(
                    processed_row, book_id, entity_ids
                )
                
                print(f"\n=== UPDATING EXISTING BOOK ===")
                print(f"Book ID: {book_id}")
                print(f"New Total Copies: {new_copies}")
                print(f"Individual Book Details Payload: {json.dumps(individual_book_payload, indent=2)}")
                
                # Mock DAO call for updating book copies
                # success = self.library_dao.updateNumberOfBook(
                #     self.institute_id, book_id, new_copies, 
                #     [individual_book_payload], [], "ADD"
                # )
                
                print(f"DAO Call: library_dao.updateNumberOfBook() - SUCCESS")
                
            else:
                # Create new book
                book_payload_wrapper = self.create_book_payload(processed_row, entity_ids)
                
                print(f"\n=== CREATING NEW BOOK ===")
                print(f"Full BookPayloadWrapper: {json.dumps(book_payload_wrapper, indent=2)}")
                
                # Mock DAO call for adding new book
                # book_id = self.library_dao.addBook(book_payload_wrapper)
                
                print(f"DAO Call: library_dao.addBook() - SUCCESS")
                book_id = str(uuid.uuid4())  # Mock book_id
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing book entry: {str(e)}")
            return False
    
    def create_book_entry_payload(self, processed_row: Dict) -> Optional[Dict]:
        """
        Create a single book entry payload with deduplication logic
        """
        try:
            # Normalize title ONLY for comparison, keep original for payload
            normalized_title = self.normalize_title(processed_row['book_title'])
            
            # Create optional entities (with deduplication)
            entity_ids = {
                'author_id': self.create_or_get_author(processed_row.get('author_name')),
                'publication_id': self.create_or_get_publication(processed_row.get('publication_data')),
                'vendor_id': self.create_or_get_vendor(processed_row.get('vendor_name')),
                'library_type_id': self.create_or_get_library_type(),
                'genre_id': None,  # Not provided in Excel
                'publisher_id': None  # Not provided in Excel
            }
            
            # Check if book exists using normalized title for comparison
            book_key = (processed_row['isbn_number'], normalized_title)  # ✅ Fixed: Include both ISBN and title
            exists, book_id, current_copies = self.check_existing_book_by_normalized_title(
                processed_row['isbn_number'], 
                normalized_title
            )
            
            # Create individual book payload
            individual_book_payload = self.create_individual_book_payload(
                processed_row, book_id if exists else None, entity_ids
            )
            
            if exists:
                # Update existing book copies
                self.update_book_copies(book_key, book_id, processed_row['no_of_copies'])
                
                return {
                    "type": "UPDATE_EXISTING",
                    "bookId": book_id,
                    "individualBookDetailsPayload": individual_book_payload,
                    "additionalCopies": processed_row['no_of_copies'],
                    "aggregation_key": book_key  # ✅ Fixed: Use proper book_key
                }
            else:
                # Create new book with ORIGINAL title from Excel
                book_id = str(uuid.uuid4())
                self.update_book_copies(book_key, book_id, processed_row['no_of_copies'])
                
                # Use original processed_row (with original title) for payload
                book_payload_wrapper = self.create_book_payload(processed_row, entity_ids)
                book_payload_wrapper["bookDetailPayload"]["bookId"] = book_id
                book_payload_wrapper["individualBookDetailsPayloads"][0]["bookId"] = book_id
                
                return {
                    "type": "CREATE_NEW",
                    "bookPayloadWrapper": book_payload_wrapper,
                    "aggregation_key": book_key  # ✅ Fixed: Use proper book_key
                }
            
        except Exception as e:
            logger.error(f"Error creating book payload: {str(e)}")
            return None

    def check_existing_book_by_normalized_title(self, isbn_number: str, normalized_title: str) -> Tuple[bool, Optional[str], int]:
        """
        Check if book exists by ISBN and normalized title with caching
        Returns: (exists, book_id, current_copies)
        """
        book_key = (isbn_number, normalized_title)  # ✅ Fixed: Use both ISBN and title
        
        # Check cache first
        if book_key in self.existing_books:
            book_id, current_copies = self.existing_books[book_key]
            return True, book_id, current_copies
        
        # Mock implementation - replace with actual DAO query
        return False, None, 0
    
    def process_excel_file(self, file_path: str) -> Tuple[int, List[str], List[Dict]]:
        """
        Main processing function for Excel file with proper aggregation
        Returns: (success_count, errors, aggregated_book_payloads)
        """
        all_book_payloads = []
        
        try:
            # Read Excel file
            df = pd.read_excel(file_path)
            logger.info(f"Loaded Excel file with {len(df)} rows")

            for row_index, row in df.iterrows():
                try:
                    # Parse voucher data with carry-forward logic
                    bill_number, date_purchase = self.parse_voucher_date(
                        str(row.get('VOUCHER NO.& DATE', ''))
                    )
                    
                    # Parse source with carry-forward logic
                    vendor_name = self.parse_source_field(
                        str(row.get('SOURCE', ''))
                    )
                    
                    # Parse price
                    price = self.parse_price(str(row.get('PRICE', '')))
                    
                    # Parse author (handle "none")
                    author_name = self.parse_author_name(str(row.get('writer name', '')))
                    
                    # Process row data
                    processed_row = {
                        'isbn_number': str(row.get('ISBN  NO.', '')).strip(),
                        'accession_number': str(row.get('assesion no.', '')).strip(),
                        'book_title': str(row.get('Book Name', '')).strip(),
                        'author_name': author_name,
                        'publication_data': str(row.get('Publication', '')).strip() if str(row.get('Publication', '')).strip() else None,
                        'vendor_name': vendor_name,
                        'language': str(row.get('Medium', '')).strip() if str(row.get('Medium', '')).strip() else None,
                        'publish_year': self.safe_int_parse(row.get('year'), 'publish_year'),
                        'number_of_pages': self.safe_int_parse(row.get('pages'), 'number_of_pages'),
                        'price': price,
                        'no_of_copies': int(row.get('COPY', 1)) if pd.notna(row.get('COPY')) and str(row['COPY']).strip() and str(row['COPY']).strip().upper() not in ['NONE', 'NULL', '', '-'] else 1,
                        'bill_number': bill_number,
                        'date_of_purchase': date_purchase
                    }
                    
                    # Track ISBN statistics for PRE-aggregation validation only
                    self.track_isbn_stats(
                        processed_row['isbn_number'],
                        processed_row['book_title'],
                        processed_row['accession_number'],
                        row_index
                    )
                    
                    # Validate required fields
                    row_errors = self.validate_required_fields(processed_row, row_index)
                    if row_errors:
                        self.errors.extend(row_errors)
                        continue
                    
                    # Create book payload (CREATE_NEW or UPDATE_EXISTING)
                    book_payload = self.create_book_entry_payload(processed_row)
                    if book_payload:
                        all_book_payloads.append(book_payload)
                        self.success_count += 1
                        logger.info(f"Successfully created payload for row {row_index}: {processed_row['book_title']}")
                
                except Exception as e:
                    error_msg = f"Row {row_index}, Accession: {row.get('assesion no.', 'N/A')} - {str(e)}"
                    self.errors.append(error_msg)
                    logger.error(error_msg)
            
            # Aggregate mixed CREATE_NEW and UPDATE_EXISTING payloads
            aggregated_payloads = self.aggregate_mixed_payloads(all_book_payloads)
            
            # Validate ISBN consistency AFTER aggregation
            isbn_warnings = self.validate_isbn_consistency_post_aggregation(aggregated_payloads)
            self.warnings.extend(isbn_warnings)
            
            return self.success_count, self.errors, aggregated_payloads
        
        except Exception as e:
            logger.error(f"Error reading Excel file: {str(e)}")
            return 0, [f"Failed to read Excel file: {str(e)}"], []
    
    def generate_report(self) -> Dict:
        """
        Generate processing report
        """
        return {
            'total_processed': self.success_count,
            'total_errors': len(self.errors),
            'error_details': self.errors,
            'success_rate': (self.success_count / (self.success_count + len(self.errors))) * 100 if (self.success_count + len(self.errors)) > 0 else 0
        }

    def save_json_to_file(self, data: List[Dict], filename: str) -> str:
        """
        Save JSON data to file
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Successfully saved JSON to {filename}")
            return filename
        except Exception as e:
            logger.error(f"Error saving JSON to file {filename}: {str(e)}")
            return None

    def create_direct_table_payloads(self, aggregated_book_payloads: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        Create separate payloads for direct table insertion from aggregated BookPayloadWrapper format
        Returns: (book_details_list, individual_book_details_list)
        """
        book_details_list = []
        individual_book_details_list = []
        
        for book_payload_wrapper in aggregated_book_payloads:
            # Extract book details
            book_detail = book_payload_wrapper.get('bookDetailPayload', {})
            if book_detail:
                book_details_list.append(book_detail)
            
            # Extract individual book details
            individual_details = book_payload_wrapper.get('individualBookDetailsPayloads', [])
            individual_book_details_list.extend(individual_details)
        
        return book_details_list, individual_book_details_list

    def save_errors_and_warnings(self, filename: str) -> str:
        """
        Save errors and warnings to file
        """
        try:
            error_data = {
                "total_errors": len(self.errors),
                "total_warnings": len(self.warnings),
                "errors": self.errors,
                "warnings": self.warnings,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(error_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Successfully saved errors and warnings to {filename}")
            return filename
        except Exception as e:
            logger.error(f"Error saving errors to file {filename}: {str(e)}")
            return None

    def save_errors_and_warnings(self, filename: str) -> str:
        """
        Save errors and warnings to file
        """
        try:
            error_data = {
                "total_errors": len(self.errors),
                "total_warnings": len(self.warnings),
                "errors": self.errors,
                "warnings": self.warnings,
                "timestamp": datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(error_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Successfully saved errors and warnings to {filename}")
            return filename
        except Exception as e:
            logger.error(f"Error saving errors to file {filename}: {str(e)}")
            return None

    def normalize_title(self, title: str) -> str:
        """
        Normalize book title for consistent comparison
        - Convert to uppercase
        - Remove extra spaces
        - Standardize spacing around hyphens and special characters
        """
        if not title:
            return ""
        
        # Convert to uppercase and strip
        normalized = title.strip().upper()
        
        # Replace multiple spaces with single space
        import re
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Standardize spacing around hyphens, colons, and other punctuation
        normalized = re.sub(r'\s*-\s*', '-', normalized)  # Remove spaces around hyphens
        normalized = re.sub(r'\s*:\s*', ':', normalized)  # Remove spaces around colons
        normalized = re.sub(r'\s*\.\s*', '.', normalized)  # Remove spaces around periods
        normalized = re.sub(r'\s*,\s*', ',', normalized)  # Remove spaces around commas
        
        # Remove leading/trailing spaces again after normalization
        return normalized.strip()

    def track_isbn_stats(self, isbn_number: str, book_title: str, accession_number: str, row_index: int):
        """
        Track ISBN statistics for validation and reporting
        """
        if not isbn_number:
            return
        
        if isbn_number not in self.isbn_stats:
            self.isbn_stats[isbn_number] = {
                'count': 0,
                'titles': set(),
                'accession_numbers': [],
                'rows': []
            }
        
        stats = self.isbn_stats[isbn_number]
        stats['count'] += 1
        stats['titles'].add(book_title)
        stats['accession_numbers'].append(accession_number)
        stats['rows'].append(row_index + 2)  # Excel row number (1-indexed + header)

    def validate_isbn_consistency(self) -> List[str]:
        """
        Validate ISBN consistency and generate warnings for potential issues
        """
        validation_warnings = []
        
        for isbn, stats in self.isbn_stats.items():
            # Check for multiple titles with same ISBN
            if len(stats['titles']) > 1:
                titles_list = list(stats['titles'])
                validation_warnings.append(
                    f"ISBN {isbn} has multiple titles: {', '.join(titles_list)} "
                    f"(Rows: {', '.join(map(str, stats['rows']))})"
                )
            
            # Check for potential copy count mismatches
            if stats['count'] > 1:
                validation_warnings.append(
                    f"ISBN {isbn} appears {stats['count']} times - verify if book should have {stats['count']} copies "
                    f"(Accessions: {', '.join(stats['accession_numbers'])})"
                )
        
        return validation_warnings

    def validate_isbn_consistency_post_aggregation(self, aggregated_payloads: List[Dict]) -> List[str]:
        """
        Validate ISBN consistency AFTER aggregation to avoid false warnings
        """
        validation_warnings = []
        
        # Build post-aggregation ISBN stats
        post_agg_isbn_stats = {}
        
        for payload in aggregated_payloads:
            # Extract ISBN from the payload
            book_detail = payload.get('bookDetailPayload', {})
            isbn = book_detail.get('isbnNumber', '')
            title = book_detail.get('bookTitle', '')
            
            # Get total copies from individual book details
            individual_books = payload.get('individualBookDetailsPayloads', [])
            total_copies = len(individual_books)
            accession_numbers = [book.get('accessionNumber', '') for book in individual_books]
            
            if isbn:
                if isbn not in post_agg_isbn_stats:
                    post_agg_isbn_stats[isbn] = {
                        'titles': set(),
                        'total_copies': 0,
                        'accession_numbers': [],
                        'book_count': 0
                    }
                
                stats = post_agg_isbn_stats[isbn]
                stats['titles'].add(title)
                stats['total_copies'] += total_copies
                stats['accession_numbers'].extend(accession_numbers)
                stats['book_count'] += 1
        
        # Generate warnings based on post-aggregation data
        for isbn, stats in post_agg_isbn_stats.items():
            # Check for multiple titles with same ISBN (real error)
            if len(stats['titles']) > 1:
                titles_list = list(stats['titles'])
                validation_warnings.append(
                    f"ISBN {isbn} has multiple titles: {', '.join(titles_list)}"
                )
            
            # Only warn if same book appears in multiple aggregated entries
            if stats['book_count'] > 1:
                validation_warnings.append(
                    f"ISBN {isbn} appears in {stats['book_count']} separate book entries - "
                    f"total {stats['total_copies']} copies "
                    f"(Accessions: {', '.join(stats['accession_numbers'])})"
                )
            
            # Info for high copy counts (not a warning, just info)
            elif stats['total_copies'] > 10:
                validation_warnings.append(
                    f"INFO: ISBN {isbn} has {stats['total_copies']} copies - "
                    f"verify if this is expected (Accessions: {', '.join(stats['accession_numbers'])})"
                )
        
        return validation_warnings

    def aggregate_mixed_payloads(self, all_book_payloads: List[Dict]) -> List[Dict]:
        """
        Aggregate mixed CREATE_NEW and UPDATE_EXISTING payloads into final BookPayloadWrapper format
        """
        print(f"\n🔄 Aggregating {len(all_book_payloads)} mixed payloads...")
        
        # Group by aggregation key
        book_groups = {}
        
        for payload in all_book_payloads:
            key = payload.get("aggregation_key")
            if not key:
                continue
                
            if key not in book_groups:
                book_groups[key] = {
                    "base_payload": None,
                    "additional_individuals": [],
                    "total_additional_copies": 0
                }
            
            if payload["type"] == "CREATE_NEW":
                # Use this as base payload
                book_groups[key]["base_payload"] = payload["bookPayloadWrapper"]
                
            elif payload["type"] == "UPDATE_EXISTING":
                # Add individual book details to be merged
                book_groups[key]["additional_individuals"].append(
                    payload["individualBookDetailsPayload"]
                )
                book_groups[key]["total_additional_copies"] += payload["additionalCopies"]
        
        # Create final aggregated payloads
        final_payloads = []
        
        for key, group_data in book_groups.items():
            base_payload = group_data["base_payload"]
            if not base_payload:
                continue
                
            # ✅ Fixed: Properly calculate total copies
            base_copies = base_payload["bookDetailPayload"].get("noOfCopies", 0)
            additional_copies = group_data["total_additional_copies"]
            total_copies = base_copies + additional_copies
            
            # Update number of copies in book detail
            base_payload["bookDetailPayload"]["noOfCopies"] = total_copies
            
            # ✅ Fixed: Add additional individual book details
            if group_data["additional_individuals"]:
                base_payload["individualBookDetailsPayloads"].extend(
                    group_data["additional_individuals"]
                )
            
            # ✅ Verify: Individual book count should match total copies
            individual_count = len(base_payload["individualBookDetailsPayloads"])
            if individual_count != total_copies:
                print(f"⚠️  Warning: Book {key} has {total_copies} copies but {individual_count} individual records")
            
            final_payloads.append(base_payload)
            print(f"📚 Aggregated book {key}: {total_copies} copies, {individual_count} individual records")
        
        print(f"✅ Aggregation complete: {len(final_payloads)} unique books")
        return final_payloads

    def make_api_call(self, method: str, endpoint: str, data: Any = None, params: Dict = None) -> Optional[Any]:
        """Make API call with retry logic"""
        if not self.api_config:
            return None
            
        url = f"{self.api_config['base_url']}{endpoint}"
        
        try:
            if method == 'POST':
                if 'save-book-details' in endpoint:
                    return self.insert_book_multipart(data)
                else:
                    response = requests.post(url, json=data, headers=self.headers, params=params)
            else:
                response = requests.get(url, headers=self.headers, params=params)
            
            self.processing_stats['api_calls_made'] += 1
            
            if response.status_code in [200, 201]:
                return response.json() if response.content else True
            else:
                print(f"❌ API call failed: {response.status_code} - {response.text}")
                return None
            
        except Exception as e:
            print(f"❌ API call exception: {str(e)}")
            return None

    def insert_book_multipart(self, book_payload: Dict) -> bool:
        """Insert book using multipart form data"""
        try:
            files = {
                'add_book_details': (None, json.dumps(book_payload)),
                'documentName': (None, '')
            }
            
            params = {
                "user_id": self.user_id,
                "access_token": self.api_config['access_token']
            }
            
            url = f"{self.api_config['base_url']}/2.0/library/save-book-details/{self.institute_id}"
            
            headers = {
                'Accept': 'application/json',
                'Authorization': f"Bearer {self.api_config['access_token']}"
            }
            
            response = requests.post(
                url,
                files=files,
                params=params,
                headers=headers
            )
            
            self.processing_stats['api_calls_made'] += 1
            
            if response.status_code in [200, 201]:
                return True
            else:
                print(f"❌ Book insertion failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Book insertion exception: {str(e)}")
            return False

    def batch_insert_entities(self) -> bool:
        """Insert all entities using batch APIs"""
        if not self.api_config:
            return False
            
        print("🚀 Batch entity insertion...")
        success = True
        
        # Insert authors
        if self.all_authors:
            print(f"  👤 Inserting {len(self.all_authors)} authors...")
            if not self.insert_authors_batch(self.all_authors):
                success = False
        
        # Insert vendors
        if self.all_vendors:
            print(f"  🏪 Inserting {len(self.all_vendors)} vendors...")
            if not self.insert_vendors_batch(self.all_vendors):
                success = False
        
        # Insert publications
        if self.all_publications:
            print(f"  📖 Inserting {len(self.all_publications)} publications...")
            if not self.insert_publications_batch(self.all_publications):
                success = False
        
        return success

    def insert_authors_batch(self, authors: List[Dict]) -> bool:
        """Insert authors in batch"""
        try:
            params = {"user_id": self.user_id, "access_token": self.api_config['access_token']}
            response = self.make_api_call(
                'POST', 
                f"/2.0/library/save-author-details-batch/{self.institute_id}", 
                authors, 
                params
            )
            return response is not None
        except Exception as e:
            print(f"❌ Authors batch failed: {str(e)}")
            return False

    def insert_vendors_batch(self, vendors: List[Dict]) -> bool:
        """Insert vendors in batch"""
        try:
            params = {"user_id": self.user_id, "access_token": self.api_config['access_token']}
            response = self.make_api_call(
                'POST', 
                f"/2.0/library/save-vendor-details-batch/{self.institute_id}", 
                vendors, 
                params
            )
            return response is not None
        except Exception as e:
            print(f"❌ Vendors batch failed: {str(e)}")
            return False

    def insert_publications_batch(self, publications: List[Dict]) -> bool:
        """Insert publications in batch"""
        try:
            params = {"user_id": self.user_id, "access_token": self.api_config['access_token']}
            response = self.make_api_call(
                'POST', 
                f"/2.0/library/save-publication-details-batch/{self.institute_id}", 
                publications, 
                params
            )
            return response is not None
        except Exception as e:
            print(f"❌ Publications batch failed: {str(e)}")
            return False

    def batch_insert_books(self, aggregated_payloads: List[Dict]) -> bool:
        """Insert all books using batch processing"""
        if not self.api_config:
            return False
            
        print(f"📚 Batch book insertion ({len(aggregated_payloads)} books)...")
        
        success_count = 0
        
        for i, book_payload in enumerate(aggregated_payloads, 1):
            try:
                print(f"  Processing book {i}/{len(aggregated_payloads)}...")
                
                if self.insert_book_multipart(book_payload):
                    success_count += 1
                    self.processing_stats['books_created'] += 1
                
                if success_count % 10 == 0:
                    print(f"  📈 Inserted {success_count}/{len(aggregated_payloads)} books")
                    
            except Exception as e:
                self.errors.append(f"Failed to insert book: {str(e)}")
                print(f"❌ Book insertion failed: {str(e)}")
        
        print(f"✅ Successfully inserted {success_count}/{len(aggregated_payloads)} books")
        return success_count == len(aggregated_payloads)

    def insert_data_to_api(self, aggregated_payloads: List[Dict]) -> bool:
        """Main API insertion function"""
        if not self.api_config:
            print("❌ No API configuration provided")
            return False
            
        print(f"\n🚀 Starting API insertion for {len(aggregated_payloads)} books...")
        
        try:
            # Step 1: Insert entities first
            if not self.batch_insert_entities():
                print("❌ Entity insertion failed")
                return False
            
            print("✅ Entities inserted successfully")
            
            # Step 2: Insert books
            if not self.batch_insert_books(aggregated_payloads):
                print("❌ Some books failed to insert")
                return False
            
            print("🎉 ALL DATA INSERTED SUCCESSFULLY!")
            return True
            
        except Exception as e:
            print(f"❌ API insertion failed: {str(e)}")
            return False

    def save_errors_and_warnings(self, filename: str):
        """Save errors and warnings to JSON file"""
        error_data = {
            "timestamp": datetime.now().isoformat(),
            "institute_id": self.institute_id,
            "errors": self.errors,
            "warnings": self.warnings,
            "total_errors": len(self.errors),
            "total_warnings": len(self.warnings)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(error_data, f, indent=2, ensure_ascii=False)

    def save_json_to_file(self, data: Any, filename: str):
        """Save data to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    def generate_report(self) -> Dict:
        """Generate processing report"""
        total_processed = self.success_count + len(self.errors)
        success_rate = (self.success_count / total_processed * 100) if total_processed > 0 else 0
        
        return {
            "total_processed": total_processed,
            "successful": self.success_count,
            "total_errors": len(self.errors),
            "success_rate": success_rate
        }
    def check_accession_number_access(self) -> bool:
        """Check if accession number access is enabled for the institute"""
        try:
            url = f"{self.api_config['base_url']}/2.0/user-preferences/library"
            
            params = {
                "institute_id": self.institute_id,
                "access_token": self.api_config['access_token']
            }
            
            response = requests.get(
                url,
                params=params,
                headers=self.headers
            )
            
            if response.status_code == 200:
                preferences = response.json()
                # Check if accession number access is enabled
                accession_enabled = preferences.get('enableAccessionNumber', False)
                
                print(f"📋 Library Preferences Retrieved:")
                print(f"   - Enable Accession Number: {accession_enabled}")
                print(f"   - Auto Increment: {preferences.get('accessionNumberCounter', False)}")
                
                return accession_enabled
            else:
                print(f"⚠️  Failed to retrieve library preferences: {response.status_code}")
                print(f"   Response: {response.text}")
                # Default to True if we can't check (to avoid blocking)
                return True
                
        except Exception as e:
            print(f"⚠️  Error checking accession number access: {str(e)}")
            # Default to True if we can't check (to avoid blocking)
            return True

def main():
    """
    Main execution function
    """
    # Configuration
    INSTITUTE_ID = 10356  # Replace with actual institute ID
    EXCEL_FILE_PATH = r"C:\Users\<USER>\Downloads\LIBARARY BOOK WITH ISBN NUMBER WISE  (FINAL LIST).xlsx"  # Replace with actual file path
    user_id = "27fcd651-cf72-41aa-bff8-ca481058750b"
    
    # API Configuration (optional)
    API_CONFIG = {
        'base_url': 'https://api.embrate.com',
        # 'base_url': 'http://127.0.0.1:8080/data-server',
        'access_token': 'aa0eb118-9747-49f7-9256-2b99b51ba1d4'
    }
    
    if len(sys.argv) >= 3:
        excel_file_path = sys.argv[1]
        institute_id = int(sys.argv[2])
    else:
        excel_file_path = EXCEL_FILE_PATH
        institute_id = INSTITUTE_ID
    
    if not os.path.exists(excel_file_path):
        print(f"Error: Excel file not found: {excel_file_path}")
        sys.exit(1)
    
    # Initialize ingestion script with API config
    ingestion_script = LibraryBookIngestionScript(institute_id, user_id, API_CONFIG)

     # Check accession number access for institute
    print(f"\n🔍 Checking accession number access for institute {institute_id}...")
    accession_access_enabled = ingestion_script.check_accession_number_access()
    
    if not accession_access_enabled:
        print("❌ Accession number access is disabled for this institute.")
        print("📄 Cannot proceed with book insertion. Please enable accession number access first.")
        sys.exit(1)
    
    print("✅ Accession number access is enabled. Proceeding with insertion...")
    
    # Confirm before proceeding
    print(f"\n🤔 Ready for book ingestion books. Continue? (y/n): ", end="")
    user_input = input().strip().lower()
    
    if user_input != 'y':
        print("📄 Operation cancelled by user.")
        sys.exit(0)
    # Process Excel file
    logger.info(f"Starting book ingestion for institute {institute_id}")
    success_count, errors, all_book_payloads = ingestion_script.process_excel_file(excel_file_path)
    
    # Generate timestamp for files
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Always save errors and warnings
    error_filename = f"errors_warnings_{institute_id}_{timestamp}.json"
    ingestion_script.save_errors_and_warnings(error_filename)
    
    # Generate and display report
    report = ingestion_script.generate_report()
    
    print("\n" + "="*50)
    print("LIBRARY BOOK INGESTION REPORT")
    print("="*50)
    print(f"Total Books Processed: {report['total_processed']}")
    print(f"Total Errors: {report['total_errors']}")
    print(f"Total Warnings: {len(ingestion_script.warnings)}")
    print(f"Success Rate: {report['success_rate']:.2f}%")
    
    # If there are errors, don't create data files
    if errors:
        print(f"\n❌ ERRORS FOUND - Data files not created")
        print(f"📄 Error details saved to: {error_filename}")
        print("\nERRORS:")
        for error in errors[:10]:  # Show first 10 errors
            print(f"  - {error}")
        if len(errors) > 10:
            print(f"  ... and {len(errors) - 10} more errors")
        return
    
    # No errors - create all data files
    print(f"\n✅ NO ERRORS - Creating data files")
    
    # Save complete book payloads
    complete_filename = f"library_books_complete_{institute_id}_{timestamp}.json"
    ingestion_script.save_json_to_file(all_book_payloads, complete_filename)
    
    # Create and save direct table payloads
    book_details_list, individual_book_details_list = ingestion_script.create_direct_table_payloads(all_book_payloads)
    
    book_details_filename = f"book_details_table_{institute_id}_{timestamp}.json"
    individual_details_filename = f"individual_book_details_table_{institute_id}_{timestamp}.json"
    authors_filename = f"authors_table_{institute_id}_{timestamp}.json"
    publications_filename = f"publications_table_{institute_id}_{timestamp}.json"
    vendors_filename = f"vendors_table_{institute_id}_{timestamp}.json"
    
    ingestion_script.save_json_to_file(book_details_list, book_details_filename)
    ingestion_script.save_json_to_file(individual_book_details_list, individual_details_filename)
    ingestion_script.save_json_to_file(ingestion_script.all_authors, authors_filename)
    ingestion_script.save_json_to_file(ingestion_script.all_publications, publications_filename)
    ingestion_script.save_json_to_file(ingestion_script.all_vendors, vendors_filename)
    
    print(f"\n=== FILES CREATED ===")
    print(f"📚 Complete Book Payloads: {complete_filename}")
    print(f"📖 Book Details Table: {book_details_filename}")
    print(f"📋 Individual Book Details: {individual_details_filename}")
    print(f"👤 Authors Table: {authors_filename}")
    print(f"🏢 Publications Table: {publications_filename}")
    print(f"🏪 Vendors Table: {vendors_filename}")
    print(f"⚠️  Errors & Warnings: {error_filename}")
    
    # API insertion option
    if not errors and all_book_payloads:
        print(f"\n🤔 Do you want to insert {len(all_book_payloads)} books to the API? (y/n): ", end="")
        user_input = input().strip().lower()
        
        if user_input == 'y':
            print("\n🚀 Starting API insertion...")
            
            if ingestion_script.insert_data_to_api(all_book_payloads):
                print("🎉 ALL DATA INSERTED SUCCESSFULLY!")
            else:
                print("❌ API insertion failed")
        else:
            print("📄 Data files generated. API insertion skipped.")
    
    print("="*50)

if __name__ == "__main__":
    main()
