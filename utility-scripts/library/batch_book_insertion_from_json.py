import json
import requests
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchBookInsertionFromJSON:
    def __init__(self, institute_id: int, user_id: str, api_config: Dict):
        self.institute_id = institute_id
        self.user_id = user_id
        self.api_config = api_config
        
        # Tracking
        self.successful_insertions = []
        self.failed_insertions = []
        self.processing_stats = {
            'total_books': 0,
            'successful_count': 0,
            'failed_count': 0,
            'api_calls_made': 0
        }
        
        # Headers for API calls
        self.headers = {
            'Accept': 'application/json',
            'Authorization': f"Bearer {self.api_config['access_token']}"
        }

    def load_aggregated_payloads(self, json_file_path: str) -> List[Dict]:
        """Load aggregated book payloads from JSON file"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"Successfully loaded {len(data)} book payloads from {json_file_path}")
            return data
            
        except FileNotFoundError:
            logger.error(f"JSON file not found: {json_file_path}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON format: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Error loading JSON file: {str(e)}")
            return []

    def insert_single_book(self, book_payload: Dict) -> Dict:
        """Insert single book and return detailed result"""
        book_id = book_payload.get('bookDetailPayload', {}).get('bookId', 'Unknown')
        book_title = book_payload.get('bookDetailPayload', {}).get('bookTitle', 'Unknown')
        
        try:
            # Prepare multipart form data
            files = {
                'add_book_details': (None, json.dumps(book_payload)),
                'documentName': (None, '')
            }
            
            params = {
                "user_id": self.user_id,
                "access_token": self.api_config['access_token']
            }
            
            url = f"{self.api_config['base_url']}/2.0/library/save-book-details/{self.institute_id}"
            
            response = requests.post(
                url,
                files=files,
                params=params,
                headers=self.headers
            )
            
            self.processing_stats['api_calls_made'] += 1
            
            if response.status_code in [200, 201]:
                return {
                    'success': True,
                    'book_id': book_id,
                    'book_title': book_title,
                    'status_code': response.status_code,
                    'response_data': response.json() if response.content else None
                }
            else:
                error_message = f"HTTP {response.status_code}: {response.text}"
                return {
                    'success': False,
                    'book_id': book_id,
                    'book_title': book_title,
                    'status_code': response.status_code,
                    'error_message': error_message,
                    'response_text': response.text
                }
                
        except requests.exceptions.ConnectionError:
            error_message = "Connection error - unable to reach API"
            return {
                'success': False,
                'book_id': book_id,
                'book_title': book_title,
                'error_message': error_message,
                'error_type': 'CONNECTION_ERROR'
            }
        except Exception as e:
            error_message = f"Unexpected error: {str(e)}"
            return {
                'success': False,
                'book_id': book_id,
                'book_title': book_title,
                'error_message': error_message,
                'error_type': 'EXCEPTION'
            }

    def batch_insert_books(self, book_payloads: List[Dict]) -> bool:
        """Insert all books with detailed tracking"""
        self.processing_stats['total_books'] = len(book_payloads)
        
        print(f"🚀 Starting batch insertion of {len(book_payloads)} books...")
        print(f"📡 API Endpoint: {self.api_config['base_url']}")
        print(f"🏫 Institute ID: {self.institute_id}")
        print("-" * 60)
        
        for i, book_payload in enumerate(book_payloads, 1):
            print(f"📚 Processing book {i}/{len(book_payloads)}...", end=" ")
            
            result = self.insert_single_book(book_payload)
            
            if result['success']:
                self.successful_insertions.append(result)
                self.processing_stats['successful_count'] += 1
                print(f"✅ SUCCESS - {result['book_title'][:50]}...")
            else:
                self.failed_insertions.append(result)
                self.processing_stats['failed_count'] += 1
                print(f"❌ FAILED - {result['book_title'][:50]}... | Error: {result['error_message'][:100]}...")
            
            # Progress update every 10 books
            if i % 10 == 0:
                success_rate = (self.processing_stats['successful_count'] / i) * 100
                print(f"📊 Progress: {i}/{len(book_payloads)} | Success Rate: {success_rate:.1f}%")
        
        print("-" * 60)
        print(f"🎯 BATCH INSERTION COMPLETE!")
        print(f"✅ Successful: {self.processing_stats['successful_count']}")
        print(f"❌ Failed: {self.processing_stats['failed_count']}")
        print(f"📡 API Calls Made: {self.processing_stats['api_calls_made']}")
        
        return self.processing_stats['failed_count'] == 0

    def save_results(self, output_dir: str = ".") -> Dict[str, str]:
        """Save detailed results to JSON files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        files_created = {}
        
        # Save successful insertions
        if self.successful_insertions:
            success_file = os.path.join(output_dir, f"successful_book_insertions_{self.institute_id}_{timestamp}.json")
            with open(success_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'institute_id': self.institute_id,
                    'total_successful': len(self.successful_insertions),
                    'successful_books': self.successful_insertions
                }, f, indent=2, ensure_ascii=False)
            files_created['success_file'] = success_file
        
        # Save failed insertions with detailed error info
        if self.failed_insertions:
            failed_file = os.path.join(output_dir, f"failed_book_insertions_{self.institute_id}_{timestamp}.json")
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'institute_id': self.institute_id,
                    'total_failed': len(self.failed_insertions),
                    'failed_books': self.failed_insertions,
                    'error_summary': self.get_error_summary()
                }, f, indent=2, ensure_ascii=False)
            files_created['failed_file'] = failed_file
        
        # Save processing summary
        summary_file = os.path.join(output_dir, f"batch_insertion_summary_{self.institute_id}_{timestamp}.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'institute_id': self.institute_id,
                'processing_stats': self.processing_stats,
                'success_rate': (self.processing_stats['successful_count'] / self.processing_stats['total_books'] * 100) if self.processing_stats['total_books'] > 0 else 0,
                'error_summary': self.get_error_summary()
            }, f, indent=2, ensure_ascii=False)
        files_created['summary_file'] = summary_file
        
        return files_created

    def get_error_summary(self) -> Dict:
        """Generate error summary statistics"""
        error_types = {}
        status_codes = {}
        
        for failed_book in self.failed_insertions:
            # Count error types
            error_type = failed_book.get('error_type', 'API_ERROR')
            error_types[error_type] = error_types.get(error_type, 0) + 1
            
            # Count status codes
            status_code = failed_book.get('status_code', 'N/A')
            status_codes[str(status_code)] = status_codes.get(str(status_code), 0) + 1
        
        return {
            'error_types': error_types,
            'status_codes': status_codes,
            'total_errors': len(self.failed_insertions)
        }

    def get_failed_book_ids(self) -> List[str]:
        """Get list of failed book IDs for easy reference"""
        return [book['book_id'] for book in self.failed_insertions]

    def check_accession_number_access(self) -> bool:
        """Check if accession number access is enabled for the institute"""
        try:
            url = f"{self.api_config['base_url']}/2.0/user-preferences/library"
            
            params = {
                "institute_id": self.institute_id,
                "access_token": self.api_config['access_token']
            }
            
            response = requests.get(
                url,
                params=params,
                headers=self.headers
            )
            
            if response.status_code == 200:
                preferences = response.json()
                # Check if accession number access is enabled
                accession_enabled = preferences.get('enableAccessionNumber', False)
                
                print(f"📋 Library Preferences Retrieved:")
                print(f"   - Enable Accession Number: {accession_enabled}")
                print(f"   - Auto Increment: {preferences.get('accessionNumberCounter', False)}")
                
                return accession_enabled
            else:
                print(f"⚠️  Failed to retrieve library preferences: {response.status_code}")
                print(f"   Response: {response.text}")
                # Default to True if we can't check (to avoid blocking)
                return True
                
        except Exception as e:
            print(f"⚠️  Error checking accession number access: {str(e)}")
            # Default to True if we can't check (to avoid blocking)
            return True

def main():
    """Main execution function"""
    
    # Configuration
    INSTITUTE_ID = 10356  # Replace with your institute ID
    JSON_FILE_PATH = r"C:/Users/<USER>/OneDrive/Desktop/<EMAIL>"  # Replace with your JSON file path
    USER_ID = "27fcd651-cf72-41aa-bff8-ca481058750b"  # Replace with your user ID
    
    # API Configuration
    API_CONFIG = {
        'base_url': 'https://api.embrate.com',
        'access_token': '389404b1-bc65-4d21-b378-ee65e90861d1'  # Replace with your token
    }
    
    # Command line arguments support
    if len(sys.argv) >= 3:
        json_file_path = sys.argv[1]
        institute_id = int(sys.argv[2])
    else:
        json_file_path = JSON_FILE_PATH
        institute_id = INSTITUTE_ID
    
    # Validate file exists
    if not os.path.exists(json_file_path):
        print(f"❌ Error: JSON file not found: {json_file_path}")
        sys.exit(1)
    
    print("=" * 60)
    print("📚 BATCH BOOK INSERTION FROM JSON")
    print("=" * 60)
    print(f"📁 JSON File: {json_file_path}")
    print(f"🏫 Institute ID: {institute_id}")
    print(f"👤 User ID: {USER_ID}")
    print("=" * 60)
    
    # Initialize batch inserter
    batch_inserter = BatchBookInsertionFromJSON(institute_id, USER_ID, API_CONFIG)
    
    # Load book payloads
    book_payloads = batch_inserter.load_aggregated_payloads(json_file_path)
    
    if not book_payloads:
        print("❌ No book payloads found or failed to load JSON file")
        sys.exit(1)
    
    # Check accession number access for institute
    print(f"\n🔍 Checking accession number access for institute {institute_id}...")
    accession_access_enabled = batch_inserter.check_accession_number_access()
    
    if not accession_access_enabled:
        print("❌ Accession number access is disabled for this institute.")
        print("📄 Cannot proceed with book insertion. Please enable accession number access first.")
        sys.exit(1)
    
    print("✅ Accession number access is enabled. Proceeding with insertion...")
    
    # Confirm before proceeding
    print(f"\n🤔 Ready to insert {len(book_payloads)} books. Continue? (y/n): ", end="")
    user_input = input().strip().lower()
    
    if user_input != 'y':
        print("📄 Operation cancelled by user.")
        sys.exit(0)
    
    # Perform batch insertion
    success = batch_inserter.batch_insert_books(book_payloads)
    
    # Save results
    files_created = batch_inserter.save_results()
    
    # Display final results
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    print(f"📚 Total Books: {batch_inserter.processing_stats['total_books']}")
    print(f"✅ Successful: {batch_inserter.processing_stats['successful_count']}")
    print(f"❌ Failed: {batch_inserter.processing_stats['failed_count']}")
    
    if batch_inserter.processing_stats['total_books'] > 0:
        success_rate = (batch_inserter.processing_stats['successful_count'] / batch_inserter.processing_stats['total_books']) * 100
        print(f"📈 Success Rate: {success_rate:.2f}%")
    
    print("\n📁 FILES CREATED:")
    for file_type, file_path in files_created.items():
        print(f"  📄 {file_type}: {file_path}")
    
    # Show failed book IDs if any
    if batch_inserter.failed_insertions:
        failed_ids = batch_inserter.get_failed_book_ids()
        print(f"\n❌ FAILED BOOK IDs ({len(failed_ids)}):")
        for book_id in failed_ids[:10]:  # Show first 10
            print(f"  - {book_id}")
        if len(failed_ids) > 10:
            print(f"  ... and {len(failed_ids) - 10} more (see failed_book_insertions file)")
    
    print("=" * 60)
    
    if success:
        print("🎉 ALL BOOKS INSERTED SUCCESSFULLY!")
    else:
        print("⚠️  Some books failed to insert. Check the failed_book_insertions file for details.")

if __name__ == "__main__":
    main()




