{"timestamp": "2025-08-26T23:22:46.635669", "institute_id": 10356, "total_failed": 2, "failed_books": [{"success": false, "book_id": "d9a29182-f320-44a8-8908-0f93d54b1a3d", "book_title": "Maple Illustrated-Krishna Stories", "status_code": 500, "error_message": "HTTP 500: <!doctype html><html lang=\"en\"><head><title>HTTP Status 500 – Internal Server Error</title><style type=\"text/css\">h1 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:22px;} h2 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:16px;} h3 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:14px;} body {font-family:Tahoma,Arial,sans-serif;color:black;background-color:white;} b {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;} p {font-family:Tahoma,Arial,sans-serif;background:white;color:black;font-size:12px;} a {color:black;} a.name {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 500 – Internal Server Error</h1><hr class=\"line\" /><p><b>Type</b> Exception Report</p><p><b>Message</b> PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter</p><p><b>Description</b> The server encountered an unexpected condition that prevented it from fulfilling the request.</p><p><b>Exception</b></p><pre>org.springframework.jdbc.UncategorizedSQLException: PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:83)\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:603)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Root Cause</b></p><pre>java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:127)\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)\n\tcom.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:622)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:397)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1116)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1066)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1396)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:1051)\n\tcom.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)\n\tcom.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:818)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:1)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:587)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Note</b> The full stack trace of the root cause is available in the server logs.</p><hr class=\"line\" /><h3>Apache Tomcat/8.5.39 (Ubuntu)</h3></body></html>", "response_text": "<!doctype html><html lang=\"en\"><head><title>HTTP Status 500 – Internal Server Error</title><style type=\"text/css\">h1 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:22px;} h2 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:16px;} h3 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:14px;} body {font-family:Tahoma,Arial,sans-serif;color:black;background-color:white;} b {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;} p {font-family:Tahoma,Arial,sans-serif;background:white;color:black;font-size:12px;} a {color:black;} a.name {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 500 – Internal Server Error</h1><hr class=\"line\" /><p><b>Type</b> Exception Report</p><p><b>Message</b> PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter</p><p><b>Description</b> The server encountered an unexpected condition that prevented it from fulfilling the request.</p><p><b>Exception</b></p><pre>org.springframework.jdbc.UncategorizedSQLException: PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:83)\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:603)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Root Cause</b></p><pre>java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:127)\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)\n\tcom.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:622)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:397)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1116)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1066)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1396)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:1051)\n\tcom.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)\n\tcom.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:818)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:1)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:587)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Note</b> The full stack trace of the root cause is available in the server logs.</p><hr class=\"line\" /><h3>Apache Tomcat/8.5.39 (Ubuntu)</h3></body></html>"}, {"success": false, "book_id": "45ee6c31-ca1e-4992-88fc-c9bcbc4e9401", "book_title": "Bible Ki Kathay", "status_code": 500, "error_message": "HTTP 500: <!doctype html><html lang=\"en\"><head><title>HTTP Status 500 – Internal Server Error</title><style type=\"text/css\">h1 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:22px;} h2 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:16px;} h3 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:14px;} body {font-family:Tahoma,Arial,sans-serif;color:black;background-color:white;} b {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;} p {font-family:Tahoma,Arial,sans-serif;background:white;color:black;font-size:12px;} a {color:black;} a.name {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 500 – Internal Server Error</h1><hr class=\"line\" /><p><b>Type</b> Exception Report</p><p><b>Message</b> PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter</p><p><b>Description</b> The server encountered an unexpected condition that prevented it from fulfilling the request.</p><p><b>Exception</b></p><pre>org.springframework.jdbc.UncategorizedSQLException: PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:83)\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:603)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Root Cause</b></p><pre>java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:127)\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)\n\tcom.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:622)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:397)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1116)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1066)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1396)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:1051)\n\tcom.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)\n\tcom.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:818)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:1)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:587)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Note</b> The full stack trace of the root cause is available in the server logs.</p><hr class=\"line\" /><h3>Apache Tomcat/8.5.39 (Ubuntu)</h3></body></html>", "response_text": "<!doctype html><html lang=\"en\"><head><title>HTTP Status 500 – Internal Server Error</title><style type=\"text/css\">h1 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:22px;} h2 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:16px;} h3 {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;font-size:14px;} body {font-family:Tahoma,Arial,sans-serif;color:black;background-color:white;} b {font-family:Tahoma,Arial,sans-serif;color:white;background-color:#525D76;} p {font-family:Tahoma,Arial,sans-serif;background:white;color:black;font-size:12px;} a {color:black;} a.name {color:black;} .line {height:1px;background-color:#525D76;border:none;}</style></head><body><h1>HTTP Status 500 – Internal Server Error</h1><hr class=\"line\" /><p><b>Type</b> Exception Report</p><p><b>Message</b> PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter</p><p><b>Description</b> The server encountered an unexpected condition that prevented it from fulfilling the request.</p><p><b>Exception</b></p><pre>org.springframework.jdbc.UncategorizedSQLException: PreparedStatementCallback; uncategorized SQLException for SQL [INSERT INTO book_details (book_id, institute_id, genre_id, book_title, book_no, book_tags, author_id, isbn_number, publication_id, publisher_id, edition, no_of_copies, publish_year, language, type_of_binding, number_of_pages) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]; SQL state [HY000]; error code [3988]; Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter; nested exception is java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:83)\n\torg.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:603)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Root Cause</b></p><pre>java.sql.SQLException: Conversion from collation utf8mb4_0900_ai_ci into latin1_swedish_ci impossible for parameter\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:127)\n\tcom.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:95)\n\tcom.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:622)\n\tcom.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:397)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1116)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1066)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1396)\n\tcom.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:1051)\n\tcom.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)\n\tcom.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:818)\n\torg.springframework.jdbc.core.JdbcTemplate$2.doInPreparedStatement(JdbcTemplate.java:1)\n\torg.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:587)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:812)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:868)\n\torg.springframework.jdbc.core.JdbcTemplate.update(JdbcTemplate.java:876)\n\tcom.lernen.cloud.dao.tier.library.LibraryDao.addBook(LibraryDao.java:921)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:199)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager$1.doInTransaction(LibraryManager.java:196)\n\torg.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:130)\n\tcom.lernen.cloud.core.lib.managers.LibraryManager.addBook(LibraryManager.java:196)\n\tcom.lernen.cloud.data.server.library.LibraryEndpoint.addBook(LibraryEndpoint.java:98)\n\tsun.reflect.GeneratedMethodAccessor1136.invoke(Unknown Source)\n\tsun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\n\tjava.lang.reflect.Method.invoke(Method.java:498)\n\tcom.sun.jersey.spi.container.JavaMethodInvokerFactory$1.invoke(JavaMethodInvokerFactory.java:60)\n\tcom.sun.jersey.server.impl.model.method.dispatch.AbstractResourceMethodDispatchProvider$ResponseOutInvoker._dispatch(AbstractResourceMethodDispatchProvider.java:205)\n\tcom.sun.jersey.server.impl.model.method.dispatch.ResourceJavaMethodDispatcher.dispatch(ResourceJavaMethodDispatcher.java:75)\n\tcom.sun.jersey.server.impl.uri.rules.HttpMethodRule.accept(HttpMethodRule.java:302)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.ResourceClassRule.accept(ResourceClassRule.java:108)\n\tcom.sun.jersey.server.impl.uri.rules.RightHandPathRule.accept(RightHandPathRule.java:147)\n\tcom.sun.jersey.server.impl.uri.rules.RootResourceClassesRule.accept(RootResourceClassesRule.java:84)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1542)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl._handleRequest(WebApplicationImpl.java:1473)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1419)\n\tcom.sun.jersey.server.impl.application.WebApplicationImpl.handleRequest(WebApplicationImpl.java:1409)\n\tcom.sun.jersey.spi.container.servlet.WebComponent.service(WebComponent.java:409)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:558)\n\tcom.sun.jersey.spi.container.servlet.ServletContainer.service(ServletContainer.java:733)\n\tjavax.servlet.http.HttpServlet.service(HttpServlet.java:742)\n\torg.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:52)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:330)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:118)\n\torg.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:84)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:103)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:113)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:54)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:45)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.ReadOnlyScopeFilter.doFilter(ReadOnlyScopeFilter.java:110)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:131)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:87)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\tcom.lernen.cloud.core.utils.oauth2.SecurityHeaderFilter.doFilter(SecurityHeaderFilter.java:57)\n\torg.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:342)\n\torg.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:192)\n\torg.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:160)\n\torg.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:346)\n\torg.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:259)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.handleStandardMetrics(APIMetricsFilter.java:189)\n\tcom.embrate.cloud.core.utils.prometheus.APIMetricsFilter.doFilter(APIMetricsFilter.java:143)\n</pre><p><b>Note</b> The full stack trace of the root cause is available in the server logs.</p><hr class=\"line\" /><h3>Apache Tomcat/8.5.39 (Ubuntu)</h3></body></html>"}], "error_summary": {"error_types": {"API_ERROR": 2}, "status_codes": {"500": 2}, "total_errors": 2}}