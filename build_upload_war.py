#! /usr/bin/python
import os
import sys
import subprocess

def build_war():
	cmd = "mvn clean install -T 4C"
	subprocess.call(cmd,shell=True)

def copy_war(ip,env):
	ip = ip.strip()
	env = env.strip().lower();
	key = "lernen-qa-2022.cer"
	if(env == "prod"):
		key = "lernen-be-2023.cer"
	elif(env == "preprod"):
		key = "L-preprod.pem"
	cmd = "scp -i ~/.ssh/"+key+ " " + os.getcwd() + "/data-server/target/data-server.war ubuntu@"+ip+":/tmp/."
	print("cmd : " + cmd)
	subprocess.call(cmd,shell=True)
	print("Copied war file to "+ip+":/tmp/.")
	print("ssh -i ~/.ssh/"+key+" ubuntu@"+ip)

if __name__ == '__main__':
	env = input("env : ")
	if env == "prod":
		ip = "api.embrate.com"
	elif env == "preprod":
		ip = "demo.embrate.com"
	else:
		ip = "dev.embrate.com"
	#ip = raw_input("IP : ")
	build_war()
	copy_war(ip,env)
