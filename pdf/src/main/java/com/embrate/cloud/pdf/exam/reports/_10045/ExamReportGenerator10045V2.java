package com.embrate.cloud.pdf.exam.reports._10045;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridHeaderConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.core.utils.images.WatermarkProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.*;


/**
 *
 * <AUTHOR>
 *
 */
public class ExamReportGenerator10045V2 extends ExamReportGenerator10045 {

	public ExamReportGenerator10045V2(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10045V2.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;
	public static final float STUDENT_IMAGE_WIDTH = 75f;
	public static final float STUDENT_IMAGE_HEIGHT = 75f;

	protected static final String I_TERMINAL_EXAM = "I_TERMINAL";
	protected static final String II_TERMINAL_EXAM = "II_TERMINAL";
	protected static final String III_TERMINAL_EXAM = "III_TERMINAL";
	
	

	@Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
                    examReportData.getStandardMetaData().getStandardId());

            PdfFont regularFont = getCambriaFont();
            PdfFont boldFont = getCambriaBoldFont();

			generateFirstPageLayout(examReportCardLayoutData, examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), institute, boldFont, regularFont,
			examReportData.getStudentLite(), studentManager, reportType, 1);
			
			examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
           
            generateSecondPageLayout(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
                    2, boldFont, regularFont);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

	private void generateSecondPageLayout(ExamReportCardLayoutData examReportCardLayoutData,
                                           Institute institute, ExamReportData examReportData,
                                           StudentManager studentManager, String reportType,
                                           int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

        generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                examReportData, pageNumber, boldFont, regularFont);


        Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
        generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
                regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
                examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
                "Scholastic Subjects", getScholasticMarksGridSubjectWidth(),
                nonAdditionalSubjects, "MM", "MO", "#ff0000",
                "-");

        if (!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
            addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
            generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forSkipTotalRow(),
                    "Additional Subjects", getScholasticMarksGridSubjectWidth(), boldFont, regularFont);
        }

//        generateCoScholasticExamDescription(examReportCardLayoutData.getDocument(),
//                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
//                examReportData, boldFont, regularFont, SCHOLASTIC_DESCRIPTION);
        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

		generateResultSummary(examReportCardLayoutData.getDocument(),
                examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                examReportData, reportType, boldFont, regularFont, studentManager);
		
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);

        if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            generateCoScholasticMarksGridSection(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
                    examReportCardLayoutData.getDefaultBorderWidth(),
                    examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC), false, examReportData,
                    regularFont, boldFont);
        }

        addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
        
        generateRemarksSection(examReportCardLayoutData, examReportData, examReportCardLayoutData.getContentFontSize() - 1,
                boldFont, regularFont);
		
		generateSignatureBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(), boldFont, examReportData.getStudentLite());

        generateBorderLayout(examReportCardLayoutData, pageNumber);
    }

	protected float getScholasticMarksGridSubjectWidth() {
        return 0.2f;
    }

	 private void generateCoScholasticMarksGridSection(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                      float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid, boolean showTotal,
                                                      ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {

        float subjectColumnWidth = 0.30f;
        CourseType courseType = CourseType.COSCHOLASTIC;

        float[] columnWidths = new float[] { subjectColumnWidth, 0.20f, 0.02f, 0.28f, 0.20f };
        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

        CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 1)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                .setTextAlignment(TextAlignment.LEFT);

        addBlankLine(document, false, 1);

        List<CellData> headerList = new ArrayList<>();
        headerList.addAll(getHeaderCells(examReportMarksGrid,
                examReportData, courseType, defaultBorderWidth, regularFont, boldFont, contentFontSize, contentFontSize,
                subjectColumnWidth, "Co-curricular Activity/Personal/Social trait", true, "MM", "MO", false));

        ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
                CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
                        examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);

        String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingBackgroundColor();
        String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingTextColor();

        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setBorder(
                new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        if(!StringUtils.isBlank(gridHeadingBackgroundColor)) {
            headerCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
        }

        List<Integer> rgbGridHeadingColorCode = null;
        if(!StringUtils.isBlank(gridHeadingTextColor)) {
            rgbGridHeadingColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
        }

        headerList.add(new CellData("", emptyCellLayoutSetup));
        if(CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
            headerList.add(new CellData(" Characteristics", headerCellLayoutSetup));
            headerList.add(new CellData("Grade", headerCellLayoutSetup));
        } else {
            headerList.add(new CellData(getParagraph("Characteristics", rgbGridHeadingColorCode.get(0),
                    rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
            headerList.add(new CellData(getParagraph("Grade", rgbGridHeadingColorCode.get(0),
                    rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
        }

        addRow(headerTable, documentLayoutSetup, headerList);

        Map<String, String> personalityTraitKeyValueMap = getPersonalityTraitKeyValueMap(examReportData.getStudentPersonalityTraitsResponseList());

        List<Map.Entry<String, String>> gradesList = new ArrayList<>(personalityTraitKeyValueMap.entrySet());

        Collections.sort(examReportMarksGrid.getExamReportCourseMarksRows(),
                new Comparator<ExamReportCourseMarksRow>() {

                    @Override
                    public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

                        return o1.getCourse().compareTo(o2.getCourse());
                    }
                });
        int index = 0;
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
            if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
                continue;
            }
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName()), courseCellLayoutSetup));
            for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
                    .getExamReportCourseMarksColumns()) {
                /**
                 * Assuming grades only for coscholatic rows
                 */
                if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
                    continue;
                }
                ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
                        .getExamDimensionObtainedValuesList().get(0);
                String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
                        : examDimensionObtainedValues.getObtainedGrade().getGradeName();
                row.add(new CellData(getParagraph(value), marksCellLayoutSetup));
            }

            row.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
            if (index < gradesList.size()) {
                row.add(new CellData(getParagraph(gradesList.get(index).getKey()),
                        marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
                row.add(new CellData(getParagraph(gradesList.get(index++).getValue()),
                        marksCellLayoutSetup.copy().setPdfFont(regularFont)));
            } else {
                row.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
                row.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
            }

            addRow(headerTable, documentLayoutSetup, row);
        }

        if (index < gradesList.size()) {
            while (index < gradesList.size()) {
                List<CellData> reminingRows = new ArrayList<>();
                reminingRows.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
                reminingRows.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
                reminingRows.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
                reminingRows.add(new CellData(getParagraph(gradesList.get(index).getKey()),
                        marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
                reminingRows.add(new CellData(getParagraph(gradesList.get(index++).getValue()), marksCellLayoutSetup));
                addRow(headerTable, documentLayoutSetup, reminingRows);
            }
        }

        /**
         * only showing sum in last column
         */
        if (showTotal) {
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(getParagraph("Total"), courseCellLayoutSetup, 1,
                    columnWidths.length - examReportMarksGrid.getExamReportTotalMarksColumns()
                            .get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
                            .getExamDimensionObtainedValuesList().size()));

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksGrid
                    .getExamReportTotalMarksColumns()
                    .get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
                    .getExamDimensionObtainedValuesList()) {
                String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
                        : examDimensionObtainedValues.getObtainedGrade().getGradeName();
                row.add(new CellData(getParagraph(value), marksCellLayoutSetup));
            }

            addRow(headerTable, documentLayoutSetup, row);
        }

        // addRow(headerTable, documentLayoutSetup,
        // Arrays.asList(getParagraph(NEXT_LINE)), new CellLayoutSetup());

        document.add(headerTable);

    }

	private Map<String, String> getPersonalityTraitKeyValueMap(List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList) {
        Map<String, String> personalityTraitKeyValueMap = new HashMap<>();
        if(CollectionUtils.isEmpty(studentPersonalityTraitsResponseList)) {
            return personalityTraitKeyValueMap;
        }

        for(StudentPersonalityTraitsResponse studentPersonalityTraitsResponse : studentPersonalityTraitsResponseList) {
            if(studentPersonalityTraitsResponse.getPersonalityTraitsDetails() == null) {
                continue;
            }
            personalityTraitKeyValueMap.put(studentPersonalityTraitsResponse.getPersonalityTraitsDetails().getPersonalityTraitName(),
                    studentPersonalityTraitsResponse.getResponse());
        }
        return personalityTraitKeyValueMap;
    }

	protected void generateStudentAttributeTable(ExamReportCardLayoutData examReportCardLayoutData,
												 ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {
		CellLayoutSetup attributeCellLayoutSetup = new CellLayoutSetup();
		attributeCellLayoutSetup.setPdfFont(boldFont)
				.setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setTextAlignment(TextAlignment.CENTER)
				.setBorder(new SolidBorder(1f));

		Table attributesTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),
				new float[] { 0.20f, 0.13f, 0.20f, 0.13f, 0.21f, 0.13f });

		addRow(attributesTable, examReportCardLayoutData.getDocumentLayoutSetup(),
				Arrays.asList(getParagraph("Height (cm)", boldFont),
						getParagraph(examReportData.getHeight(), regularFont),
						getParagraph("Weight (kg)", boldFont),
						getParagraph(examReportData.getWeight(), regularFont),
						getParagraph("Blood Group", boldFont),
						getParagraph(examReportData.getStudentLite().getBloodGroup() == null ? ""
								: examReportData.getStudentLite().getBloodGroup().getDisplayName(), regularFont)), attributeCellLayoutSetup);

		examReportCardLayoutData.getDocument().add(attributesTable);
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
															   ExamReportData examReportData, GridConfigs gridConfigs,
															   String subjectColumnTitle, float subjectColumnWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				regularFont, boldFont, examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
				subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"MM", "MO", "#ff0000", "-");
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		return 0.5f;
	}

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput,
																	UUID standardId)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = standardId != null ?
				standardId.equals(UUID.fromString("06e0eb7f-eaf1-4975-9563-014b7edbfc35")) ? 11f : 12f : 12f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 60f;
		float logoHeight = 60f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
										  StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData, instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.89f,
				boldFont, regularFont, studentManager, examReportData);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
								  Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont, StudentManager studentManager, ExamReportData examReportData) throws IOException {

		/* generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY + 10f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

		generateLogo(examReportCardLayoutData, institute, ImageProvider.INSTANCE.getImage(ImageProvider._CBSE_LOGO_2),
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - (2.5f * offsetX), offsetY + 10f); */

		/* byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
		if (studentImage != null) {
			generateImage(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), studentImage, STUDENT_IMAGE_WIDTH, STUDENT_IMAGE_HEIGHT,
					examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - offsetX - STUDENT_IMAGE_WIDTH + 12f,
					offsetY - STUDENT_IMAGE_HEIGHT -2f);
		} */

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase())), cellLayoutSetup.copy().setFontSize(17f));

		/* addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1()).setMultipliedLeading(1f)
						.setFontColor(new DeviceRgb(EColorUtils.lightRed1R, EColorUtils.lightRed1G, EColorUtils.lightRed1B))),
				cellLayoutSetup.copy().setFontSize(9f));

		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2()).setMultipliedLeading(1f)
						.setFontColor(new DeviceRgb(EColorUtils.lightRed1R, EColorUtils.lightRed1G, EColorUtils.lightRed1B))),
				cellLayoutSetup.copy().setFontSize(9f)); */

		examReportCardLayoutData.getDocument().add(table);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String reportCardHeaderJsonText = examReportData.getExamReportStructure().getExamReportStructureMetaData().getReportCardHeaderName();
		String reportCardHeader = StringUtils.isBlank(reportCardHeaderJsonText) ? reportType.equalsIgnoreCase(I_TERMINAL_EXAM) ? "1st Terminal Exam" : reportType.equalsIgnoreCase(II_TERMINAL_EXAM) ? "2nd Terminal Exam" : reportType.equalsIgnoreCase(III_TERMINAL_EXAM) ? "3rd Terminal Exam" : "" : reportCardHeaderJsonText;
		String headerExamTitle = "Session: " + studentLite.getStudentSessionData().getAcademicSessionName() + "";
		if(!StringUtils.isBlank(reportCardHeader)){
			addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(reportCardHeader)),
				cellLayoutSetup.copy().setFontSize(14f).setPdfFont(boldFont));
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle)),
				cellLayoutSetup.copy().setFontSize(11f).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
											  StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.35f, 0.3f, 0.35f});

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup forthCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);

		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, regularFont).setMultipliedLeading(1f);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),
				boldFont, regularFont).setMultipliedLeading(1f);
		Paragraph rollnumber = getKeyValueParagraph("Roll No. : ", studentLite.getStudentSessionData().getRollNumber(),
				boldFont, regularFont).setMultipliedLeading(1f);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				studentLite.getStudentSessionData().getStandardNameWithSection(),
				boldFont, regularFont).setMultipliedLeading(1f);

		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup), new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData(EMPTY_TEXT, secondCellLayoutSetup),new CellData(rollnumber, thirdCellLayoutSetup)));
		document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
										 float contentFontSize, ExamReportData examReportData,
										 String reportType, PdfFont boldFont, PdfFont regularFont,
										 StudentManager studentManager) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.33f, 0.33f, 0.33f });

		String attendanceText = "-";
		if (examReportData.getTotalAttendedDays() != null && examReportData.getTotalWorkingDays() != null) {
			double attendancePercentage = (double) examReportData.getTotalAttendedDays() / examReportData.getTotalWorkingDays() * 100;
			attendanceText = String.format("%.2f%%", attendancePercentage);
		}		
		Paragraph totalMarks = getKeyValueParagraph("Total Marks: ", examReportData.getTotalMaxMarks() == null ? "-" : examReportData.getTotalMaxMarks().toString(),
		boldFont, regularFont).setMultipliedLeading(1f);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained Marks: ", examReportData.getTotalObtainedMarks() == null ? "-" : examReportData.getTotalObtainedMarks().toString(),
		boldFont, regularFont).setMultipliedLeading(1f);
		Paragraph attendancePercentage = getKeyValueParagraph("Attendance Percentage: ", attendanceText,
				boldFont, regularFont).setMultipliedLeading(1f);

		Paragraph percentage = getKeyValueParagraph("Percentage Of Marks: ", examReportData.getPercentage() == null ? "-"
						: Math.round(examReportData.getPercentage() * 100) / 100d + "%",
				boldFont, regularFont);
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(),
				boldFont, regularFont);

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(totalMarks, cellLayoutSetup),
				new CellData(percentage, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(attendancePercentage, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
										  ExamReportData examReportData, float contentFontSize, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(1f));
		
		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), new float[] { 0.23f, 0.77f});


		Paragraph remarksValue = getParagraph(StringUtils.isBlank(examReportData.getRemarks()) ? "-" : examReportData.getRemarks(),
				boldFont);

		Paragraph remarks = getParagraph("Remarks", boldFont);

		Paragraph result = getParagraph("Result : ", boldFont);
		Paragraph resultValue = getParagraph("-", regularFont);
		if(examReportData.getExamResultStatus() != null){
			resultValue = getParagraph(examReportData.getExamResultStatus().getDisplayName(),regularFont);
		}

		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(new CellData(remarks, cellLayoutSetup.copy().setBorderBottom(new SolidBorder(0f))),
		new CellData(remarksValue, cellLayoutSetup.copy().setBorderBottom(new SolidBorder(0f)))));
		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(new CellData(result, cellLayoutSetup.copy()),
		new CellData(resultValue, cellLayoutSetup.copy())));
		examReportCardLayoutData.getDocument().add(remarksTable);
		generateGradeBox(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportData, contentFontSize, 1f, regularFont, boldFont);
	}

	private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize, 
                                  float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {

		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);

		boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);

		if (!hasScholastic) {
			return;
		}

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth)).setBorderTop(new SolidBorder(0f)).setTextAlignment(TextAlignment.CENTER);
		

		if (hasScholastic) {
			generateGradeTable(document, documentLayoutSetup, scholasticGrades, marksCellLayoutSetup, 
							contentFontSize, boldFont, regularFont);
		}
	}

	private void generateGradeTable(Document document, DocumentLayoutSetup documentLayoutSetup, List<ExamGrade> grades, CellLayoutSetup marksCellLayoutSetup,
									float contentFontSize, PdfFont boldFont, PdfFont regularFont) throws IOException {

		Table headerTable = getPDFTable(documentLayoutSetup, grades.size());
		List<CellData> headerList = new ArrayList<>();

		for (ExamGrade grade : grades) {
			String remarks = "";
			if(!StringUtils.isBlank(grade.getRemarks()))
			{
				remarks = "\n("+grade.getRemarks()+")";
			}
			headerList.add(new CellData(grade.getRangeDisplayName()+" = "+grade.getGradeName()+remarks, marksCellLayoutSetup));
		}

		addRow(headerTable, documentLayoutSetup, headerList);
		document.add(headerTable);
	}

	protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
										float contentFontSize, float defaultBorderWidth, int pageNumber,
										PdfFont boldFont, PdfFont regularFont) throws IOException {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		canvas.moveTo(12, 100);
		canvas.lineTo(583, 100);
		canvas.setLineWidth(.5f);
		canvas.closePathStroke();
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph("Class Teacher").setFontColor(new DeviceRgb(0,0,139)),
						getParagraph("").setFontColor(new DeviceRgb(0,0,139)),
						getParagraph("Principal").setFontColor(new DeviceRgb(0,0,139))),
				signatureCellLayoutSetup);
		table.setFixedPosition(30f, 100f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);
	}


	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
											  List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput,
					CollectionUtils.isEmpty(examReportDataList) ? null : examReportDataList.get(0).getStandardMetaData().getStandardId());
			
			Document document = examReportCardLayoutData.getDocument();
			DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			
			for (ExamReportData examReportData : examReportDataList) {

				generateFirstPageLayout(examReportCardLayoutData, document, documentLayoutSetup, institute, boldFont, regularFont,
						examReportData.getStudentLite(), studentManager, reportType, pageNumber);

				document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				pageNumber++;

				generateSecondPageLayout(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size() * 2) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, "#ff0000");
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}