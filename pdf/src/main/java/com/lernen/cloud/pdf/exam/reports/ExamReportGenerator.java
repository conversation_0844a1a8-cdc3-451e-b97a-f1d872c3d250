package com.lernen.cloud.pdf.exam.reports;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamDimensionObtainedValues;
import com.lernen.cloud.core.api.examination.ExamDivision;
import com.lernen.cloud.core.api.examination.ExamEvaluationType;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.ExamGridAdditionalAttributeRow;
import com.lernen.cloud.core.api.examination.StudentPersonalityTraitsResponse;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridPersonalityTraitsConfigs;
import com.lernen.cloud.core.api.examination.report.configs.GridTotalMarksRowConfigs;
import com.lernen.cloud.core.api.examination.report.configs.PersonalityTraitsColumnStructure;
import com.lernen.cloud.core.api.examination.report.configs.PersonalityTraitsColumnType;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.StringHelper;
import com.lernen.cloud.pdf.base.PDFGenerator;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
public abstract class ExamReportGenerator extends PDFGenerator {
	public ExamReportGenerator(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator.class);
	public static final String TOTAL_HINDI = "योग";
	public static final String GRADE_HINDI = "ग्रेड";
	public static final String MAX_MARKS_HINDI_SHORT = "पू.";
	public static final String OBTAINED_MARKS_HINDI_SHORT = "प्रा.";
	public static final String MAX_MARKS_HINDI = "पूर्णांक";
	public static final String OBTAINED_MARKS_HINDI = "प्राप्तांक";
	public static final String SUBJECT_HINDI = "विषय";
	protected static final float DEFAULT_FONT_SIZE = 8.5f;
	protected static final float DEFAULT_BORDER_WIDTH = 0.5f;

	public static final String NULL_MAX_MARKS_VALUE = "-";
	public static final String MAX_PERCENTAGE = "100%";

	public float[] getMarksGridWidth(ExamReportMarksGrid examReportMarksGrid, CourseType courseType,
			boolean equalDistribution, ExamReportStructureMetaData examReportStructureMetaData,
			float subjectColumnWidth, boolean showGradeOnly, boolean rowWiseMaxMarksTitle,
			float maxMarksTitleColumnWidth) {
		int count = 0;

		for (ExamReportCourseMarksColumn examReportMarksColumn : examReportMarksGrid.getExamReportTotalMarksColumns()) {
			if(examReportMarksColumn.isHide()) {
				continue;
			}
			count += examReportMarksColumn.getExamDimensionObtainedValuesList().size()
					* getDimensionMultiplier(examReportStructureMetaData,
							examReportMarksColumn.getExamReportGridColumnType(), courseType, showGradeOnly);
		}
		int subjectColumns = rowWiseMaxMarksTitle ? 2 : 1;
		float[] widths = new float[count + subjectColumns];
		widths[0] = equalDistribution ? 1.0f / (count + subjectColumns) : subjectColumnWidth;
		if (rowWiseMaxMarksTitle) {
			widths[1] = equalDistribution ? 1.0f / (count + subjectColumns) : maxMarksTitleColumnWidth;
		}
		float usedWidth = widths[0] + (rowWiseMaxMarksTitle ? widths[1] : 0f);
		for (int i = 0; i < count; i++) {
			widths[i + subjectColumns] = (1 - usedWidth) / count;
		}
		return widths;
	}

	public int getDimensionMultiplier(ExamReportStructureMetaData examReportStructureMetaData,
			ExamReportGridColumnType examReportGridColumnType, CourseType courseType, boolean showGradeOnly) {
		boolean showCourseMaxMarks = courseType == CourseType.SCHOLASTIC
				? examReportStructureMetaData.isShowScholasticCourseMaxMarks()
				: examReportStructureMetaData.isShowCoScholasticCourseMaxMarks();

		int dimensionMultiplier = showCourseMaxMarks ? 2 : 1;
		if (showGradeOnly || examReportGridColumnType == ExamReportGridColumnType.EXAM_GRADE
				|| examReportGridColumnType == ExamReportGridColumnType.GRADE
				|| examReportGridColumnType == ExamReportGridColumnType.CREDIT_SCORE
				|| examReportGridColumnType == ExamReportGridColumnType.PERCENT
				|| examReportGridColumnType == ExamReportGridColumnType.STATIC
				|| examReportGridColumnType == ExamReportGridColumnType.MIN_MARKS
				|| examReportGridColumnType == ExamReportGridColumnType.COURSE_RANK
				|| examReportGridColumnType == ExamReportGridColumnType.COURSE_GAIN
				|| examReportGridColumnType == ExamReportGridColumnType.CLASS_COURSE_AVG
				|| examReportGridColumnType == ExamReportGridColumnType.CLASS_COURSE_HIGH) {
			dimensionMultiplier = 1;
		}
		return dimensionMultiplier;
	}

	public int getHeaderColumnMaxHeight(ExamReportMarksGrid examReportMarksGrid) {
		int columnMaxHeight = 0;
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : examReportMarksGrid
				.getExamReportMarksHeaderColumns()) {
			if(examReportMarksColumn.isHide()) {
				continue;
			}
			columnMaxHeight = Math.max(columnMaxHeight, getTreeHeight(examReportMarksColumn));
		}
		return columnMaxHeight;
	}

	/**
	 * Updates row and column span for all header columns
	 * 
	 * @param examReportMarksGrid
	 * @return all columns in header tree
	 */
	public List<ExamReportMarksColumn<ExamDimensionObtainedValues>> updateRowColumnSpan(
			ExamReportMarksGrid examReportMarksGrid, int maxHeight,
			ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, boolean showGrade) {

		if (CollectionUtils.isEmpty(examReportMarksGrid.getExamReportMarksHeaderColumns())) {
			return new ArrayList<>();
		}

		List<ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumns = new ArrayList<>();
		int xPosition = 0;
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : examReportMarksGrid
				.getExamReportMarksHeaderColumns()) {
			if(examReportMarksColumn.isHide()) {
				continue;
			}
			updateRowColumnSpan(examReportMarksColumn, xPosition, 0, maxHeight, examReportMarksColumns,
					examReportStructureMetaData, courseType, showGrade);
			xPosition = examReportMarksColumn.getX() + examReportMarksColumn.getColSpan();
		}
		return examReportMarksColumns;
	}

	/**
	 * Updates row and column span for all child nodes including given node
	 * 
	 * @param examReportMarksColumn
	 * @param maxHeight
	 */
	public void updateRowColumnSpan(ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn,
			int xPosition, int parentYPosition, int maxHeight,
			List<ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumns,
			ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, boolean showGradeOnly) {

		/**
		 * Leaf node column span will always be 1 or equal to dimension size and row
		 * span low end will stick to bottom and upper end to parent position
		 * 
		 */
		if (CollectionUtils.isEmpty(examReportMarksColumn.getChildExamReportMarksColumns())) {
			int rowSpan = maxHeight - parentYPosition;
			examReportMarksColumn.setRowSpan(rowSpan);
			int colSpan = examReportMarksColumn.getExamDimensionValues().size()
					* getDimensionMultiplier(examReportStructureMetaData,
							examReportMarksColumn.getExamReportGridColumnType(), courseType, showGradeOnly);
			examReportMarksColumn.setColSpan(colSpan);
			examReportMarksColumn.setX(xPosition);
			examReportMarksColumn.setY(parentYPosition);
			examReportMarksColumns.add(examReportMarksColumn);
			return;
		}

		/**
		 * Finding max height child. It determines the lowest end of parent
		 */
		int childrenMaxHeight = 0;
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> childExamReportMarksColumn : examReportMarksColumn
				.getChildExamReportMarksColumns()) {
			if(childExamReportMarksColumn.isHide()) {
				continue;
			}
			childrenMaxHeight = Math.max(childrenMaxHeight, getTreeHeight(childExamReportMarksColumn));
		}

		int colSpan = 0;
		int childXPosition = xPosition;
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> childExamReportMarksColumn : examReportMarksColumn
				.getChildExamReportMarksColumns()) {
			if(childExamReportMarksColumn.isHide()) {
				continue;
			}
			updateRowColumnSpan(childExamReportMarksColumn, childXPosition, maxHeight - childrenMaxHeight, maxHeight,
					examReportMarksColumns, examReportStructureMetaData, courseType, showGradeOnly);
			colSpan += childExamReportMarksColumn.getColSpan();
			childXPosition = childExamReportMarksColumn.getX() + childExamReportMarksColumn.getColSpan();
		}

		int rowSpan = maxHeight - childrenMaxHeight - parentYPosition;
		examReportMarksColumn.setRowSpan(rowSpan);
		examReportMarksColumn.setColSpan(colSpan);
		examReportMarksColumn.setX(xPosition);
		examReportMarksColumn.setY(parentYPosition);
		examReportMarksColumns.add(examReportMarksColumn);
		return;
	}

	public boolean displayDimensionRow(ExamReportMarksGrid examReportMarksGrid, GridConfigs gridConfigs) {
		boolean dimensionExists = false;
		if(gridConfigs != null && gridConfigs.getGridHeaderConfigs() != null && gridConfigs.getGridHeaderConfigs().isHideDimensionsRow()) {
			return dimensionExists;
		}
		for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportMarksGrid
				.getExamReportTotalMarksColumns()) {
			if (examReportCourseMarksColumn.getExamDimensionObtainedValuesList().size() > 1) {
				dimensionExists = true;
				break;
			}
		}
		return dimensionExists;
	}

	public boolean isGradeOnlyGrid(ExamReportMarksGrid examReportMarksGrid) {
		boolean allGradedExamColumns = true;
		for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportMarksGrid
				.getExamReportTotalMarksColumns()) {
			allGradedExamColumns &= gradedExamColumn(examReportCourseMarksColumn);

		}
		return allGradedExamColumns;
	}

	public Map<String, String> getPersonalityTraitKeyValueData(List<StudentPersonalityTraitsResponse> studentPersonalityTraitsResponseList) {
        Map<String, String> personalityTraitKeyValueMap = new HashMap<>();
        if(CollectionUtils.isEmpty(studentPersonalityTraitsResponseList)) {
            return personalityTraitKeyValueMap;
        }

        for(StudentPersonalityTraitsResponse studentPersonalityTraitsResponse : studentPersonalityTraitsResponseList) {
            if(studentPersonalityTraitsResponse.getPersonalityTraitsDetails() == null) {
                continue;
            }
            personalityTraitKeyValueMap.put(studentPersonalityTraitsResponse.getPersonalityTraitsDetails().getPersonalityTraitName(),
                    studentPersonalityTraitsResponse.getResponse());
        }
        return personalityTraitKeyValueMap;
    }

	public void generatePersonalityTraitSection(Document document, DocumentLayoutSetup documentLayoutSetup,
                                          float contentFontSize, float defaultBorderWidth, 
                                          Map<String, String> personalityTraitKeyValueMap, 
                                          GridConfigs gridConfigs, ExamReportData examReportData, 
                                          PdfFont regularFont, PdfFont boldFont, String personalityTraitsTitle) throws IOException {

		if (MapUtils.isEmpty(personalityTraitKeyValueMap) || gridConfigs == null) {
			return;
		}

		GridPersonalityTraitsConfigs personalityConfig = gridConfigs.getGridPersonalityTraitsConfigs();
		PersonalityTraitsColumnStructure columnStructure = PersonalityTraitsColumnStructure.DIVIDED_DATA_STRUCTURE;
		String subjectTitle = "";
		String marksTitle = "Grade";
		float subjectColumnWidth = 0.28f;
		float marksColumnWidth = 0.21f;
		if(personalityConfig != null) {
			columnStructure = personalityConfig.getPersonalityTraitsColumnStructure();
			subjectTitle = personalityConfig.getPersonalityTraitColumnSubjectTitle();
			marksTitle = personalityConfig.getPersonalityTraitColumnMarksTitle();
			subjectColumnWidth = personalityConfig.getPersonalityTraitColumnSubjectWidth();
			marksColumnWidth = personalityConfig.getPersonalityTraitColumnMarksWidth();
		}
		// if(!StringUtils.isBlank(personalityTraitsTitle)){
		// Table headerTitleTable=getPDFTable(documentLayoutSetup, 1).setMargin(0f);
		// CellLayoutSetup HeaderTitleCellLayoutSetup = new CellLayoutSetup();
		// HeaderTitleCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.CENTER);
		// List<CellData> titleList = new ArrayList<>();
		// titleList.add(new CellData(personalityTraitsTitle, HeaderTitleCellLayoutSetup));
		// addRow(headerTitleTable, documentLayoutSetup, titleList);
		// document.add(headerTitleTable);
		// }

		// Get color configurations from ExamReportCourseStructure
		CourseType courseType = CourseType.COSCHOLASTIC;
		ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
				CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
						examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
		
		String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingBackgroundColor();
		String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingTextColor();
		String gridCourseBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
		String gridCourseTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

		// Determine column widths based on structure
		float[] columnWidths;
		if (columnStructure == PersonalityTraitsColumnStructure.STANDARDIZED_COLUMN_STRUCTURE) {
			columnWidths = new float[] { 
				subjectColumnWidth, 
				marksColumnWidth 
			};
		} else { // DIVIDED_DATA_STRUCTURE
			columnWidths = new float[] { 
				subjectColumnWidth,
				marksColumnWidth,
				subjectColumnWidth,
				marksColumnWidth
			};
		}

		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

		if(!StringUtils.isBlank(personalityTraitsTitle)){
		// Table headerTitleTable=getPDFTable(documentLayoutSetup, 1).setMargin(0f);
		CellLayoutSetup HeaderTitleCellLayoutSetup = new CellLayoutSetup();
		HeaderTitleCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setTextAlignment(TextAlignment.CENTER);
		List<CellData> titleList = new ArrayList<>();
		CellData headerTitleCellData = new CellData(personalityTraitsTitle, HeaderTitleCellLayoutSetup);
		headerTitleCellData.setColSpan(columnWidths.length);
		titleList.add(headerTitleCellData);
		
		addRow(headerTable, documentLayoutSetup, titleList);
		// document.add(headerTitleTable);
		}else{
			addBlankLine(document, false, 1);
		}

		// Cell layout setups with color support
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 1)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
				.setTextAlignment(TextAlignment.LEFT);

		CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
		headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		// Apply background colors
		if (!StringUtils.isBlank(gridHeadingBackgroundColor)) {
			headerCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
		}
		if (!StringUtils.isBlank(gridCourseBackgroundColor)) {
			courseCellLayoutSetup.setBackgroundColor(gridCourseBackgroundColor);
			marksCellLayoutSetup.setBackgroundColor(gridCourseBackgroundColor);
		}

		// Get RGB color codes for text
		List<Integer> rgbGridHeadingColorCode = null;
		List<Integer> rgbGridCourseColorCode = null;
		if (!StringUtils.isBlank(gridHeadingTextColor)) {
			rgbGridHeadingColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
		}
		if (!StringUtils.isBlank(gridCourseTextColor)) {
			rgbGridCourseColorCode = EColorUtils.hex2Rgb(gridCourseTextColor);
		}

		// addBlankLine(document, false, 1);

		// Create headers with color support using stored variables
		List<CellData> headerList = new ArrayList<>();
		if (columnStructure == PersonalityTraitsColumnStructure.STANDARDIZED_COLUMN_STRUCTURE) {
			if (CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
				headerList.add(new CellData(subjectTitle, headerCellLayoutSetup));
				headerList.add(new CellData(marksTitle, headerCellLayoutSetup));
			} else {
				headerList.add(new CellData(getParagraph(subjectTitle, 
					rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
				headerList.add(new CellData(getParagraph(marksTitle, 
					rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
			}
		} else { // DIVIDED_DATA_STRUCTURE
			if (CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
				headerList.add(new CellData(subjectTitle, headerCellLayoutSetup));
				headerList.add(new CellData(marksTitle, headerCellLayoutSetup));
				headerList.add(new CellData(subjectTitle, headerCellLayoutSetup));
				headerList.add(new CellData(marksTitle, headerCellLayoutSetup));
			} else {
				headerList.add(new CellData(getParagraph(subjectTitle, 
					rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
				headerList.add(new CellData(getParagraph(marksTitle, 
					rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
				headerList.add(new CellData(getParagraph(subjectTitle, 
					rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
				headerList.add(new CellData(getParagraph(marksTitle, 
					rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
			}
		}

		addRow(headerTable, documentLayoutSetup, headerList);

		// Add personality trait data rows with color support
		List<Map.Entry<String, String>> personalityTraitsList = new ArrayList<>(personalityTraitKeyValueMap.entrySet());
		
		if (columnStructure == PersonalityTraitsColumnStructure.STANDARDIZED_COLUMN_STRUCTURE) {
			// Two column structure
			for (Map.Entry<String, String> entry : personalityTraitsList) {
				List<CellData> row = new ArrayList<>();
				if (CollectionUtils.isEmpty(rgbGridCourseColorCode)) {
					row.add(new CellData(getParagraph(entry.getKey()), courseCellLayoutSetup));
					row.add(new CellData(getParagraph(entry.getValue()), marksCellLayoutSetup));
				} else {
					row.add(new CellData(getParagraph(entry.getKey(), rgbGridCourseColorCode.get(0),
						rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), courseCellLayoutSetup));
					row.add(new CellData(getParagraph(entry.getValue(), rgbGridCourseColorCode.get(0),
						rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), marksCellLayoutSetup));
				}
				addRow(headerTable, documentLayoutSetup, row);
			}
		} else {
			// Four column structure - distribute equally
			int mid = (personalityTraitsList.size() + 1) / 2;
			for (int i = 0; i < mid; i++) {
				List<CellData> row = new ArrayList<>();
				
				// First pair
				Map.Entry<String, String> firstEntry = personalityTraitsList.get(i);
				if (CollectionUtils.isEmpty(rgbGridCourseColorCode)) {
					row.add(new CellData(getParagraph(firstEntry.getKey()), courseCellLayoutSetup));
					row.add(new CellData(getParagraph(firstEntry.getValue()), marksCellLayoutSetup));
				} else {
					row.add(new CellData(getParagraph(firstEntry.getKey(), rgbGridCourseColorCode.get(0),
						rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), courseCellLayoutSetup));
					row.add(new CellData(getParagraph(firstEntry.getValue(), rgbGridCourseColorCode.get(0),
						rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), marksCellLayoutSetup));
				}
				
				// Second pair (if exists)
				if (i + mid < personalityTraitsList.size()) {
					Map.Entry<String, String> secondEntry = personalityTraitsList.get(i + mid);
					if (CollectionUtils.isEmpty(rgbGridCourseColorCode)) {
						row.add(new CellData(getParagraph(secondEntry.getKey()), courseCellLayoutSetup));
						row.add(new CellData(getParagraph(secondEntry.getValue()), marksCellLayoutSetup));
					} else {
						row.add(new CellData(getParagraph(secondEntry.getKey(), rgbGridCourseColorCode.get(0),
							rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), courseCellLayoutSetup));
						row.add(new CellData(getParagraph(secondEntry.getValue(), rgbGridCourseColorCode.get(0),
							rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), marksCellLayoutSetup));
					}
				} else {
					row.add(new CellData("", marksCellLayoutSetup));
					row.add(new CellData("", marksCellLayoutSetup));
				}
				
				addRow(headerTable, documentLayoutSetup, row);
			}
		}

		document.add(headerTable);
	}

	public void generateCoScholasticMarksGridWithPersonalityTraitsSection(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                      float contentFontSize, float defaultBorderWidth, ExamReportMarksGrid examReportMarksGrid, boolean showTotal,
                                                      ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont, Map<String, String> personalityTraitKeyValueMap, boolean onlyPersonalityTraits, String personalityTraitColumnTitle) throws IOException {

        float subjectColumnWidth = 0.28f;
        CourseType courseType = CourseType.COSCHOLASTIC;
		
        float[] columnWidths = new float[] { subjectColumnWidth, 0.21f, 0.02f, 0.28f, 0.21f };
		if(onlyPersonalityTraits){
			columnWidths = new float[] { 0.5f, 0.5f};
		}
        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

        CellLayoutSetup emptyCellLayoutSetup = new CellLayoutSetup();
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize - 1)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                .setTextAlignment(TextAlignment.LEFT);

        addBlankLine(document, false, 1);

        List<CellData> headerList = new ArrayList<>();
		if(!onlyPersonalityTraits){
        headerList.addAll(getHeaderCells(examReportMarksGrid,
                examReportData, courseType, defaultBorderWidth, regularFont, boldFont, contentFontSize, contentFontSize,
                subjectColumnWidth, "Co-Scholastic Subject", true, "MM", "MO", false));
		}

        ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
                CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
                        examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
        ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure().getExamReportStructureMetaData();
        String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingBackgroundColor();
        String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingTextColor();

		String gridCourseBackgroundColor =  examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
		String gridCourseTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

        CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
        headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1).setBorder(
                new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);
        if(!StringUtils.isBlank(gridHeadingBackgroundColor)) {
            headerCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
        }
		if(!StringUtils.isBlank(gridCourseBackgroundColor)) {
			courseCellLayoutSetup.setBackgroundColor(gridCourseBackgroundColor);
		}
		if(!StringUtils.isBlank(gridCourseBackgroundColor)) {
			marksCellLayoutSetup.setBackgroundColor(gridCourseBackgroundColor);
		}

        List<Integer> rgbGridHeadingColorCode = null;
		List<Integer> rgbGridCourseColorCode = null;
		if(!StringUtils.isBlank(gridCourseTextColor)) {
			rgbGridCourseColorCode = EColorUtils.hex2Rgb(gridCourseTextColor);
		}
        if(!StringUtils.isBlank(gridHeadingTextColor)) {
            rgbGridHeadingColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
        }
		if(!onlyPersonalityTraits){
        	headerList.add(new CellData("", emptyCellLayoutSetup));
		}
        if(CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
            headerList.add(new CellData(personalityTraitColumnTitle, headerCellLayoutSetup));
            headerList.add(new CellData("Grade", headerCellLayoutSetup));
        } else {
            headerList.add(new CellData(getParagraph(personalityTraitColumnTitle, rgbGridHeadingColorCode.get(0),
                    rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
            headerList.add(new CellData(getParagraph("Grade", rgbGridHeadingColorCode.get(0),
                    rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerCellLayoutSetup));
        }

        addRow(headerTable, documentLayoutSetup, headerList);

        List<Map.Entry<String, String>> gradesList = new ArrayList<>(personalityTraitKeyValueMap.entrySet());
		if(!onlyPersonalityTraits){
			Collections.sort(examReportMarksGrid.getExamReportCourseMarksRows(),
					new Comparator<ExamReportCourseMarksRow>() {

						@Override
						public int compare(ExamReportCourseMarksRow o1, ExamReportCourseMarksRow o2) {

							return o1.getCourse().compareTo(o2.getCourse());
						}
					});
			int index = 0;
			for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {
				if (CollectionUtils.isEmpty(examReportCourseMarksRow.getExamReportCourseMarksColumns())) {
					continue;
				}
				List<CellData> row = new ArrayList<>();
				if(CollectionUtils.isEmpty(rgbGridCourseColorCode)){
					row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName()), courseCellLayoutSetup));
				}else{
					row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName(), rgbGridCourseColorCode.get(0),
							rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), courseCellLayoutSetup));
				}
				for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
						.getExamReportCourseMarksColumns()) {
					/**
					 * Assuming grades only for coscholatic rows
					 */
					if (CollectionUtils.isEmpty(examReportCourseMarksColumn.getExamDimensionObtainedValuesList())) {
						continue;
					}
					ExamDimensionObtainedValues examDimensionObtainedValues = examReportCourseMarksColumn
							.getExamDimensionObtainedValuesList().get(0);
					String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
							: examDimensionObtainedValues.getObtainedGrade().getGradeName();
					if(CollectionUtils.isEmpty(rgbGridCourseColorCode)){
						row.add(new CellData(getParagraph(value), marksCellLayoutSetup));
					}
					else{
						row.add(new CellData(getParagraph(value, rgbGridCourseColorCode.get(0),
								rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)), marksCellLayoutSetup));
					}		
				}

				row.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
				if (index < gradesList.size()) {
					if(CollectionUtils.isEmpty(rgbGridCourseColorCode)){
						row.add(new CellData(getParagraph(gradesList.get(index).getKey()),
								marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
						row.add(new CellData(getParagraph(gradesList.get(index++).getValue()),
								marksCellLayoutSetup.copy().setPdfFont(regularFont)));
					}else{
						row.add(new CellData(getParagraph(gradesList.get(index).getKey(), rgbGridCourseColorCode.get(0),
								rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)),
								marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
						row.add(new CellData(getParagraph(gradesList.get(index++).getValue(), rgbGridCourseColorCode.get(0),
								rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)),
								marksCellLayoutSetup.copy().setPdfFont(regularFont)));
					}
				} else {
					row.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
					row.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
				}

				addRow(headerTable, documentLayoutSetup, row);
			}

			if (index < gradesList.size()) {
				while (index < gradesList.size()) {
					List<CellData> reminingRows = new ArrayList<>();
					reminingRows.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
					reminingRows.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
					reminingRows.add(new CellData(getParagraph(""), emptyCellLayoutSetup));
					if(CollectionUtils.isEmpty(rgbGridCourseColorCode)){
						reminingRows.add(new CellData(getParagraph(gradesList.get(index).getKey()),
								marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
						reminingRows.add(new CellData(getParagraph(gradesList.get(index++).getValue()),
								marksCellLayoutSetup.copy().setPdfFont(regularFont)));
					}else{
						reminingRows.add(new CellData(getParagraph(gradesList.get(index).getKey(), rgbGridCourseColorCode.get(0),
								rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)),
								marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
						reminingRows.add(new CellData(getParagraph(gradesList.get(index++).getValue(), rgbGridCourseColorCode.get(0),
								rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)),
								marksCellLayoutSetup.copy().setPdfFont(regularFont)));
					}
					addRow(headerTable, documentLayoutSetup, reminingRows);
				}
			}
		}
		else{
			for (int index = 0; index < gradesList.size(); index++) {
				List<CellData> row = new ArrayList<>();
				if(CollectionUtils.isEmpty(rgbGridCourseColorCode)){
					row.add(new CellData(getParagraph(gradesList.get(index).getKey()),
							marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
					row.add(new CellData(getParagraph(gradesList.get(index++).getValue()),
							marksCellLayoutSetup.copy().setPdfFont(regularFont)));
				}else{
					row.add(new CellData(getParagraph(gradesList.get(index).getKey(), rgbGridCourseColorCode.get(0),
							rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)),
							marksCellLayoutSetup.copy().setPdfFont(boldFont).setTextAlignment(TextAlignment.LEFT)));
					row.add(new CellData(getParagraph(gradesList.get(index++).getValue(), rgbGridCourseColorCode.get(0),
							rgbGridCourseColorCode.get(1), rgbGridCourseColorCode.get(2)),
							marksCellLayoutSetup.copy().setPdfFont(regularFont)));
				}
				addRow(headerTable, documentLayoutSetup, row);
			}
		}

        /**
         * only showing sum in last column
         */
        if (showTotal) {
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(getParagraph("Total"), courseCellLayoutSetup, 1,
                    columnWidths.length - examReportMarksGrid.getExamReportTotalMarksColumns()
                            .get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
                            .getExamDimensionObtainedValuesList().size()));

            for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksGrid
                    .getExamReportTotalMarksColumns()
                    .get(examReportMarksGrid.getExamReportTotalMarksColumns().size() - 1)
                    .getExamDimensionObtainedValuesList()) {
                String value = ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade()) ? "-"
                        : examDimensionObtainedValues.getObtainedGrade().getGradeName();
                row.add(new CellData(getParagraph(value), marksCellLayoutSetup));
            }

            addRow(headerTable, documentLayoutSetup, row);
        }

        // addRow(headerTable, documentLayoutSetup,
        // Arrays.asList(getParagraph(NEXT_LINE)), new CellLayoutSetup());

        document.add(headerTable);

    }

	public List<CellData> getHeaderCells(ExamReportMarksGrid examReportMarksGrid,
										 ExamReportData examReportData, CourseType courseType, float defaultBorderWidth,
										 PdfFont regularFont, PdfFont boldFont, float contentFontSize, float subjectTitleFontSize,
										 float subjectColumnWidth, String subjectColumnTitle, boolean showGradeOnly, String maxMarksTitle,
										 String marksObtainedTitle, boolean rowWiseMaxMarksTitle) throws IOException {
		ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
				CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
						examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
		ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure().getExamReportStructureMetaData();
		return getHeaderCells(examReportMarksGrid, examReportStructureMetaData, courseType, defaultBorderWidth, regularFont,
				boldFont, contentFontSize, subjectTitleFontSize, subjectColumnWidth, subjectColumnTitle, showGradeOnly, maxMarksTitle,
				marksObtainedTitle, rowWiseMaxMarksTitle, null, examReportCourseStructure,
				null);
	}

	public List<CellData> getHeaderCells(ExamReportMarksGrid examReportMarksGrid,
			ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, float defaultBorderWidth,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float subjectTitleFontSize,
			float subjectColumnWidth, String subjectColumnTitle, boolean showGradeOnly, String maxMarksTitle,
			String marksObtainedTitle, boolean rowWiseMaxMarksTitle, List<Integer> rgbGridHeadingColorCode,
										 ExamReportCourseStructure examReportCourseStructure, GridConfigs gridConfigs) throws IOException {
		List<CellData> headerCells = new ArrayList<>();
		List<CellData> dimensionCells = new ArrayList<>();
		List<CellData> maxMarksTitleCells = new ArrayList<>();

		int maxHeight = getHeaderColumnMaxHeight(examReportMarksGrid);

		boolean displayDimension = displayDimensionRow(examReportMarksGrid, gridConfigs);
		boolean gradeOnlyGrid =   showGradeOnly || rowWiseMaxMarksTitle || isGradeOnlyGrid(examReportMarksGrid);

		List<ExamReportMarksColumn<ExamDimensionObtainedValues>> orderedExamReportMarksColumns = getOrderedHeaderColumn(
				examReportMarksGrid, courseType, maxHeight, examReportStructureMetaData, subjectColumnWidth,
				gradeOnlyGrid);

		CellLayoutSetup headerCellLayoutSetup = new CellLayoutSetup();
		headerCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setBorder(
				new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup headerDimensionCellLayoutSetup = new CellLayoutSetup();
		headerDimensionCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize).setBorder(
				new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingBackgroundColor();
		String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridHeadingTextColor();

		if(!StringUtils.isBlank(gridHeadingBackgroundColor)) {
			headerCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
			headerDimensionCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
		}

		if(!StringUtils.isBlank(gridHeadingTextColor)) {
			rgbGridHeadingColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
		}

		String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
		String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

		List<Integer> rgbCourseNameColorCode = null;
		if(!StringUtils.isBlank(gridCoursesTextColor)) {
			rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
		}

		boolean showCourseMaxMarks = examReportStructureMetaData.isShowMaxMarks(courseType);

		int dimensionColumnHeight = gradeOnlyGrid ? 0 : showCourseMaxMarks ? displayDimension ? 2 : 1 : 1;
		if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
			CellLayoutSetup cellLayoutSetup = headerCellLayoutSetup.copy().setFontSize(subjectTitleFontSize);
			if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
				cellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
			}
			headerCells.add(new CellData(getParagraph(subjectColumnTitle,
					rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)),
					cellLayoutSetup, maxHeight + dimensionColumnHeight, rowWiseMaxMarksTitle ? 2 : 1));
		} else {
			headerCells.add(new CellData(subjectColumnTitle, headerCellLayoutSetup.copy().setFontSize(subjectTitleFontSize),
					maxHeight + dimensionColumnHeight, rowWiseMaxMarksTitle ? 2 : 1));
		}

		List<ExamReportMarksColumn<ExamDimensionObtainedValues>> leafHeaderColumns = new ArrayList<>();
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : orderedExamReportMarksColumns) {
			if(examReportMarksColumn.isHide()) {
				continue;
			}
			headerCells.add(getHeaderCellData(examReportMarksColumn, headerCellLayoutSetup, rgbGridHeadingColorCode, gridConfigs));

			/**
			 * Skipping non leaf columns
			 */
			if (CollectionUtils.isEmpty(examReportMarksColumn.getChildExamReportMarksColumns())) {
				leafHeaderColumns.add(examReportMarksColumn);
			}
		}

		Collections.sort(leafHeaderColumns, new Comparator<ExamReportMarksColumn<ExamDimensionObtainedValues>>() {

			@Override
			public int compare(ExamReportMarksColumn<ExamDimensionObtainedValues> e1,
					ExamReportMarksColumn<ExamDimensionObtainedValues> e2) {
				return e1.getX() - e2.getX();
			}
		});

		for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : leafHeaderColumns) {

			if(examReportMarksColumn.isHide()) {
				continue;
			}
			/**
			 * All columns are graded only. No need for dimension and max marks columns
			 */
			if (gradeOnlyGrid) {
				continue;
			}

			int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
					examReportMarksColumn.getExamReportGridColumnType(), courseType, gradeOnlyGrid);

			/**
			 * No max marks/ marks obtained columns required. Simply add the dimensions.
			 */
			boolean isHideDimensionMaxMarks = gridConfigs != null && (gridConfigs.getGridHeaderConfigs() != null && gridConfigs.getGridHeaderConfigs().isHideDimensionMaxMarks());
			if (!showCourseMaxMarks) {
				addHeaderDimensionCells(examReportMarksColumn, dimensionCells, examReportStructureMetaData, courseType,
						headerDimensionCellLayoutSetup, displayDimension, showCourseMaxMarks || isHideDimensionMaxMarks, dimensionMultiplier, rgbGridHeadingColorCode);
				continue;
			}

			/**
			 * Case where course max marks are to be displayed
			 */
			if (displayDimension) {
				addHeaderDimensionCells(examReportMarksColumn, dimensionCells, examReportStructureMetaData, courseType,
						headerDimensionCellLayoutSetup, displayDimension, showCourseMaxMarks,
						dimensionMultiplier, rgbGridHeadingColorCode);
			}
			
			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksColumn
					.getExamDimensionValues()) {
				if (dimensionMultiplier == 1) {
					if(!CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
						CellData cellData = new CellData(getParagraph("", rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerDimensionCellLayoutSetup);
						maxMarksTitleCells.add(cellData);
					} else {
						CellData cellData = new CellData("", headerDimensionCellLayoutSetup);
						maxMarksTitleCells.add(cellData);
					}
				} else if (dimensionMultiplier == 2) {
					if(!CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
						CellData maxMarksCellData = new CellData(getParagraph(maxMarksTitle,
								rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerDimensionCellLayoutSetup);
						CellData obtainedMarksCellData = new CellData(getParagraph(marksObtainedTitle,
								rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), headerDimensionCellLayoutSetup);
						maxMarksTitleCells.add(maxMarksCellData);
						maxMarksTitleCells.add(obtainedMarksCellData);
					} else {
						CellData maxMarksCellData = new CellData(maxMarksTitle, headerDimensionCellLayoutSetup);
						CellData obtainedMarksCellData = new CellData(marksObtainedTitle, headerDimensionCellLayoutSetup);
						maxMarksTitleCells.add(maxMarksCellData);
						maxMarksTitleCells.add(obtainedMarksCellData);
					}
				}
			}
		}

		headerCells.addAll(dimensionCells);
		headerCells.addAll(maxMarksTitleCells);
		return headerCells;
	}

	private boolean gradedExamColumn(ExamReportCourseMarksColumn examReportCourseMarksColumn) {
		List<ExamDimensionObtainedValues> examDimensionObtainedValuesList = examReportCourseMarksColumn
				.getExamDimensionObtainedValuesList();
		if (examDimensionObtainedValuesList.size() != 1) {
			return false;
		}
		ExamDimensionObtainedValues examDimensionObtainedValues = examDimensionObtainedValuesList.get(0);
		if (examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE
				&& examDimensionObtainedValues.getExamDimension().isTotal()
				|| examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
				|| examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE) {
			return true;
		}
		return false;
	}

	public List<ExamReportMarksColumn<ExamDimensionObtainedValues>> getOrderedHeaderColumn(
			ExamReportMarksGrid examReportMarksGrid, CourseType courseType, int maxHeight,
			ExamReportStructureMetaData examReportStructureMetaData, float subjectColumnWidth, boolean showGradeOnly) {

		int maxWidth = getMarksGridWidth(examReportMarksGrid, courseType, false, examReportStructureMetaData,
				subjectColumnWidth, showGradeOnly, false, 0f).length - 1;

		List<ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumns = updateRowColumnSpan(
				examReportMarksGrid, maxHeight, examReportStructureMetaData, courseType, showGradeOnly);

		Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> headerColumnMap = new HashMap<>();
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : examReportMarksColumns) {
			if(examReportMarksColumn.isHide()) {
				continue;
			}
			headerColumnMap.put(examReportMarksColumn.getId(), examReportMarksColumn);
		}

		Set<String> orderedColumns = new LinkedHashSet<>();

		int i = 0;
		while (i < maxHeight) {
			int j = 0;
			while (j < maxWidth) {
				ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = getExamReportMarksColumnByCoordinates(
						j, i, examReportMarksColumns);

				if (examReportMarksColumn != null) {
					String columnId = examReportMarksColumn.getId();
					if (!orderedColumns.contains(columnId)) {
						orderedColumns.add(columnId);
					}
				}

				j++;
			}
			i++;
		}
		List<ExamReportMarksColumn<ExamDimensionObtainedValues>> orderedExamReportMarksColumns = new ArrayList<>();
		for (String columnId : orderedColumns) {
			orderedExamReportMarksColumns.add(headerColumnMap.get(columnId));
		}
		return orderedExamReportMarksColumns;
	}

	public ExamReportMarksColumn<ExamDimensionObtainedValues> getExamReportMarksColumnByCoordinates(int x, int y,
			List<ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumns) {

		for (ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn : examReportMarksColumns) {
			if(examReportMarksColumn.isHide()) {
				continue;
			}
			if (x >= examReportMarksColumn.getX()
					&& x < examReportMarksColumn.getX() + examReportMarksColumn.getColSpan()
					&& y >= examReportMarksColumn.getY()
					&& y < examReportMarksColumn.getY() + examReportMarksColumn.getRowSpan()) {
				return examReportMarksColumn;
			}
		}
		return null;
	}

	public CellData getHeaderCellData(ExamReportMarksColumn<ExamDimensionObtainedValues> examHeaderReportMarksColumn,
			CellLayoutSetup cellLayoutSetup, List<Integer> rgbGridHeadingColorCode, GridConfigs gridConfigs) {
		String title = examHeaderReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM
				|| examHeaderReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
				|| examHeaderReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE
				|| examHeaderReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID
						? StringUtils.isBlank(examHeaderReportMarksColumn.getTitle())
								? examHeaderReportMarksColumn.getExamMetaData().getExamName()
								: examHeaderReportMarksColumn.getTitle()
						: examHeaderReportMarksColumn.getTitle();
		if(gridConfigs != null && gridConfigs.getGridHeaderConfigs() != null){
			if(gridConfigs.getGridHeaderConfigs().getExamNamefontSize() != null) {
				cellLayoutSetup = cellLayoutSetup.copy().setFontSize(gridConfigs.getGridHeaderConfigs().getExamNamefontSize());
			}
			if(gridConfigs.getGridHeaderConfigs().getExamNameFont() != null) {
				cellLayoutSetup = cellLayoutSetup.copy().setPdfFont(gridConfigs.getGridHeaderConfigs().getExamNameFont());
			}
		}
		if(!CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
			return new CellData(getParagraph(title == null ? "" : title, rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)),
					cellLayoutSetup, examHeaderReportMarksColumn.getRowSpan(), examHeaderReportMarksColumn.getColSpan());
		} else {
			return new CellData(title == null ? "" : title, cellLayoutSetup, examHeaderReportMarksColumn.getRowSpan(),
					examHeaderReportMarksColumn.getColSpan());
		}
	}

	public int getTreeHeight(ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn) {

		if (CollectionUtils.isEmpty(examReportMarksColumn.getChildExamReportMarksColumns())) {
			return 1;
		}

		int childrenMaxHeight = 0;
		for (ExamReportMarksColumn<ExamDimensionObtainedValues> childExamReportMarksColumn : examReportMarksColumn
				.getChildExamReportMarksColumns()) {
			if(childExamReportMarksColumn.isHide()) {
				continue;
			}
			childrenMaxHeight = Math.max(childrenMaxHeight, getTreeHeight(childExamReportMarksColumn));
		}
		return childrenMaxHeight + 1;
	}

	public void addHeaderDimensionCells(ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn,
			List<CellData> dimensionCells, ExamReportStructureMetaData examReportStructureMetaData,
			CourseType courseType, CellLayoutSetup headerDimensionCellLayoutSetup, boolean displayDimension,
			boolean showCourseMaxMarks, int dimensionMultiplier, List<Integer> rgbGridHeadingColorCode) {
		for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksColumn.getExamDimensionValues()) {
			CellData cellData = getHeaderDimensionCellData(examReportMarksColumn,
					examDimensionObtainedValues, headerDimensionCellLayoutSetup, displayDimension, showCourseMaxMarks,
					dimensionMultiplier, rgbGridHeadingColorCode);
			dimensionCells.add(cellData);
		}
	}

	/**
	 * Creates dimension cell with colspan as dimensionMultiplier.
	 * 
	 * @param examReportMarksColumn
	 * @param examDimensionObtainedValues
	 * @param cellLayoutSetup
	 * @param displayDimension
	 * @param showCourseMaxMarks
	 * @param dimensionMultiplier
	 * @return
	 */
	public CellData getHeaderDimensionCellData(ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn,
			ExamDimensionObtainedValues examDimensionObtainedValues, CellLayoutSetup cellLayoutSetup,
			boolean displayDimension, boolean showCourseMaxMarks, int dimensionMultiplier, List<Integer> rgbGridHeadingColorCode) {
		String dimensionCellName = "";

		ExamReportGridColumnType examReportGridColumnType = examReportMarksColumn.getExamReportGridColumnType();
		boolean isTotal = examDimensionObtainedValues.getExamDimension().isTotal();
		boolean isHideDimensionMaxMarks = isTotal ? examReportMarksColumn.isHideTotalDimensionMaxMarks() : examReportMarksColumn.isHideRemainingDimensionMaxMarks();
		if (examReportGridColumnType != ExamReportGridColumnType.GRADE
				&& examReportGridColumnType != ExamReportGridColumnType.PERCENT
				&& examReportGridColumnType != ExamReportGridColumnType.EXAM_GRADE
				&& examReportGridColumnType != ExamReportGridColumnType.CREDIT_SCORE) {

			/**
			 * If showCourseMaxMarks then only show dimension name
			 */
			if (showCourseMaxMarks) {
				if(displayDimension) {
					dimensionCellName = examDimensionObtainedValues.getExamDimension().getDimensionName();
				}
			} else {
				if(!isHideDimensionMaxMarks) {
					dimensionCellName = examDimensionObtainedValues.getMaxMarks() == null ? ""
							: " (" + StringHelper.removeTrailingZero(String.valueOf(examDimensionObtainedValues.getMaxMarks())) + ")";
				}
				if (displayDimension) {
					dimensionCellName = examDimensionObtainedValues.getExamDimension().getDimensionName()
							+ dimensionCellName;
				}
			}
		}

		if(!CollectionUtils.isEmpty(rgbGridHeadingColorCode)) {
			return new CellData(getParagraph(dimensionCellName, rgbGridHeadingColorCode.get(0), rgbGridHeadingColorCode.get(1), rgbGridHeadingColorCode.get(2)), cellLayoutSetup, 1, dimensionMultiplier);
		} else {
			return new CellData(dimensionCellName, cellLayoutSetup, 1, dimensionMultiplier);
		}
	}

	public void fillCourseMarksInGrid(DocumentLayoutSetup documentLayoutSetup, ExamReportMarksGrid examReportMarksGrid,
			ExamReportStructure examReportStructure, CourseType courseType, Table headerTable,
			CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup, boolean showGrade,
			Set<UUID> coursesToDisplay, List<Integer> rgbCourseNameColorCode, String emptyColumnValue, ExamReportCourseStructure examReportCourseStructure, GridConfigs gridConfigs) {
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportMarksGrid.getExamReportCourseMarksRows()) {

			/**
			 * Filtering subjects if not required
			 */
			if (!CollectionUtils.isEmpty(coursesToDisplay)
					&& !coursesToDisplay.contains(examReportCourseMarksRow.getCourse().getCourseId())) {
				continue;
			}
			addExamCourseRow(documentLayoutSetup, examReportStructure.getExamReportStructureMetaData(), courseType,
					headerTable, marksCellLayoutSetup, courseCellLayoutSetup, examReportCourseMarksRow, showGrade, rgbCourseNameColorCode,
					emptyColumnValue, examReportCourseStructure, gridConfigs);
		}
	}

	public boolean isAdditionalSubject(Course course, ExamReportStructure examReportStructure) {
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(course.getCourseType()) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure()
						.get(course.getCourseType()).getAdditionalCourses())) {
			return false;
		}

		return examReportStructure.getExamReportCourseStructure().get(course.getCourseType()).getAdditionalCourses()
				.contains(course.getCourseId());
	}


	public void addExamCourseRow(DocumentLayoutSetup documentLayoutSetup,
								 ExamReportData examReportData, CourseType courseType, Table headerTable,
								 CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
								 ExamReportCourseMarksRow examReportCourseMarksRow, boolean showGrade) {
		addExamCourseRow(documentLayoutSetup, examReportData, courseType, headerTable, marksCellLayoutSetup, courseCellLayoutSetup,
				examReportCourseMarksRow, showGrade, "-");
	}

	public void addExamCourseRow(DocumentLayoutSetup documentLayoutSetup,
								 ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, Table headerTable,
								 CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
								 ExamReportCourseMarksRow examReportCourseMarksRow, boolean showGrade, List<Integer> rgbCourseNameColorCode,
								 String emptyColumnValue, ExamReportCourseStructure examReportCourseStructure, GridConfigs gridConfigs) {

		addExamCourseRow(documentLayoutSetup, examReportStructureMetaData, courseType, headerTable, marksCellLayoutSetup, courseCellLayoutSetup,
				examReportCourseMarksRow, showGrade, emptyColumnValue, rgbCourseNameColorCode, examReportCourseStructure, gridConfigs);
	}

	public void addExamCourseRow(DocumentLayoutSetup documentLayoutSetup,
								 ExamReportData examReportData, CourseType courseType, Table headerTable,
								 CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
								 ExamReportCourseMarksRow examReportCourseMarksRow, boolean showGrade, String emptyColumnValue) {
		ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure().getExamReportStructureMetaData();
		ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
				CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
						examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
		addExamCourseRow(documentLayoutSetup, examReportStructureMetaData, courseType, headerTable, marksCellLayoutSetup, courseCellLayoutSetup,
				examReportCourseMarksRow, showGrade, emptyColumnValue, null, examReportCourseStructure, null);
	}

	public void addExamCourseRow(DocumentLayoutSetup documentLayoutSetup,
			ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType, Table headerTable,
			CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
			ExamReportCourseMarksRow examReportCourseMarksRow, boolean showGrade, String emptyColumnValue,
			List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure, GridConfigs gridConfigs) {
		List<CellData> row = new ArrayList<>();

		String gridHeadingBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
		String gridHeadingTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();
		if(!StringUtils.isBlank(gridHeadingBackgroundColor)) {
			courseCellLayoutSetup.setBackgroundColor(gridHeadingBackgroundColor);
		}

		if(!StringUtils.isBlank(gridHeadingTextColor)) {
			rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridHeadingTextColor);
		}

		if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
			row.add(new CellData(getParagraph(examReportCourseMarksRow.getCourse().getCourseName(),
					rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
		} else {
			row.add(new CellData(examReportCourseMarksRow.getCourse().getCourseName(), courseCellLayoutSetup));
		}

		for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow
				.getExamReportCourseMarksColumns()) {

			if(examReportCourseMarksColumn.isHide()) {
				continue;
			}

			int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
					examReportCourseMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
					.getExamDimensionObtainedValuesList()) {

				if (dimensionMultiplier == 2) {
					String marks = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
							: String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));

					String hexColorCode = gridConfigs != null &&  !StringUtils.isBlank(gridConfigs.getMaxMarksColorHexCode()) ? gridConfigs.getMaxMarksColorHexCode() : examReportCourseMarksColumn.getColorHexCode();
					List<Integer> rgb = EColorUtils.hex2Rgb(hexColorCode);
					if (CollectionUtils.isEmpty(rgb)) {
						row.add(new CellData(marks, marksCellLayoutSetup));
					} else {
						row.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
								marksCellLayoutSetup));
					}
				}

				String marks = null;
				if (showGrade
						|| examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE
						|| examReportCourseMarksColumn
								.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
						|| examReportCourseMarksColumn
								.getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
					if (examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE) {
						String finalCreditScore =  ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
										? null : String.valueOf(examDimensionObtainedValues.getObtainedGrade().getCreditScore()),
								examDimensionObtainedValues.getAttendanceStatus());
						marks = finalCreditScore == null ? emptyColumnValue : finalCreditScore;
					} else {
						String finalGrade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
										? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
								examDimensionObtainedValues.getAttendanceStatus());
						marks = finalGrade == null ? emptyColumnValue : finalGrade;
					}
				}else if (examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE) {
					String finalCreditScore =  ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
									? null : String.valueOf(examDimensionObtainedValues.getObtainedGrade().getCreditScore()),
							examDimensionObtainedValues.getAttendanceStatus());
					marks = finalCreditScore == null ? emptyColumnValue : finalCreditScore;
				}else if (examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
					String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
								? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 1000) / 10d, examDimensionObtainedValues.getAttendanceStatus(), gridConfigs == null || gridConfigs.isShowPercentSymbolInGrid(), false);
					marks = finalPercentValue == null ? emptyColumnValue : finalPercentValue;
				} else if (examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.STATIC || examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.COURSE_RANK || examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.COURSE_GAIN || examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.CLASS_COURSE_AVG || examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.CLASS_COURSE_HIGH) {
					marks = examReportCourseMarksColumn.getColumnValue();
				} else if(examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.MIN_MARKS ){

					String finalMinValue = examDimensionObtainedValues.getMinMarks() == null
							? null : NumberUtils.formatDouble(Math.ceil(examDimensionObtainedValues.getMinMarks()));

					marks = finalMinValue == null ? emptyColumnValue : finalMinValue;

//					if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
//
//					} else {
//
//
//						marks = finalMinValue == null ? emptyColumnValue : finalMinValue;
//					}
				}
				else {
					/**
					 * By default, rounding off marks, only removing round if specified specially
					 */
					if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
						String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
										? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 1)),
								examDimensionObtainedValues.getAttendanceStatus());

						marks = finalObtainedValue == null ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE :
								emptyColumnValue : finalObtainedValue;
					} else {
						String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
										? null : String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks())),
								examDimensionObtainedValues.getAttendanceStatus());

						marks = finalObtainedValue == null ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE :
								emptyColumnValue : finalObtainedValue;
					}
				}

				if (marks == null) {
					marks = emptyColumnValue;
				}
				List<Integer> rgb = EColorUtils.hex2Rgb(examReportCourseMarksColumn.getColorHexCode());
				if (CollectionUtils.isEmpty(rgb)) {
					row.add(new CellData(marks, marksCellLayoutSetup));
				} else {
					row.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
							marksCellLayoutSetup));
				}
			}
		}
		addRow(headerTable, documentLayoutSetup, row);
	}

	public CourseTotalMarksRows generateCourseTotalMarksInGrid(ExamReportMarksGrid examReportMarksGrid,
			ExamReportData examReportData, CourseType courseType,
			CellLayoutSetup marksCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup, boolean showGrade, String hexColorCode, GridConfigs gridConfigs) throws IOException {
		ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure().getExamReportStructureMetaData();
		ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
				CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
						examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
		return  generateCourseTotalMarksInGrid(examReportMarksGrid, examReportStructureMetaData, courseType,
				marksCellLayoutSetup, courseCellLayoutSetup, showGrade, hexColorCode, "-", examReportCourseStructure, gridConfigs);
	}

	public CourseTotalMarksRows generateCourseTotalMarksInGrid(ExamReportMarksGrid examReportMarksGrid,
														 ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
														 CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup, boolean showGrade, String hexColorCode,
														 String emptyColumnValue, ExamReportCourseStructure examReportCourseStructure,
															   GridConfigs gridConfigs) throws IOException {
		return  generateCourseTotalMarksInGrid(examReportMarksGrid, examReportStructureMetaData, courseType,
				totalMarksRowCellLayoutSetup, courseCellLayoutSetup, showGrade, hexColorCode, emptyColumnValue,
				null, examReportCourseStructure, gridConfigs);
	}

//	public List<CellData> generateCourseTotalMarksInGrid(ExamReportMarksGrid examReportMarksGrid,
//			ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
//			CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
//			boolean showGrade, String totalRowValuesHexColor, String emptyColumnValue,
//			List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure) throws IOException {
//		List<CellData> row = new ArrayList<>();
//
//		String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
//		String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();
//
//		if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
//			courseCellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
//		}
//
//		if(!StringUtils.isBlank(gridCoursesTextColor)) {
//			rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
//		}
//
//		String totalRowBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowBackgroundColor();
//		String totalRowTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowTextColor();
//
//		if(!StringUtils.isBlank(totalRowBackgroundColor)) {
//			totalMarksRowCellLayoutSetup.setBackgroundColor(totalRowBackgroundColor);
//		}
//
//		if(!StringUtils.isBlank(totalRowTextColor)) {
//			totalRowValuesHexColor = totalRowTextColor;
//		}
//
//		if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
//			row.add(new CellData(getParagraph("Total", rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
//		} else {
//			row.add(new CellData("Total", courseCellLayoutSetup));
//		}
//		totalMarksRowCellLayoutSetup.setPdfFont(getRegularBoldFont());
//		for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportMarksGrid
//				.getExamReportTotalMarksColumns()) {
//			int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
//					examReportCourseMarksColumn.getExamReportGridColumnType(), courseType, showGrade);
//
//			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
//					.getExamDimensionObtainedValuesList()) {
//
//				if (dimensionMultiplier == 2) {
//
//					String marks = examDimensionObtainedValues.getMaxMarks() == null ? emptyColumnValue
//							: String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));
//
//					List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
//					if (CollectionUtils.isEmpty(rgb)) {
//						row.add(new CellData(marks, totalMarksRowCellLayoutSetup));
//					} else {
//						row.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
//								totalMarksRowCellLayoutSetup));
//					}
//				}
//
//				String value = "";
//				if (showGrade
//						|| examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE
//						|| examReportCourseMarksColumn
//								.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
//						|| examReportCourseMarksColumn
//								.getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
//					value = examDimensionObtainedValues.getObtainedGrade() == null ? emptyColumnValue
//							: examDimensionObtainedValues.getObtainedGrade().getGradeName();
//				} else if (examReportCourseMarksColumn
//						.getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
//					value = examDimensionObtainedValues.getObtainedMarksFraction() == null ? emptyColumnValue
//							: String.valueOf(
//									Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 1000) / 10d);
//				} else {
////					value = examDimensionObtainedValues.getObtainedMarks() == null ? emptyColumnValue
////							: String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks()));
//
//					/**
//					 * By default, rounding off marks, only removing round if specified specially
//					 */
//					if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
//						value = examDimensionObtainedValues.getObtainedMarks() == null ? emptyColumnValue
//								: String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 1));
//					} else {
//						value = examDimensionObtainedValues.getObtainedMarks() == null ? emptyColumnValue
//								: String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks()));
//					}
//				}
//
//				if (value == null) {
//					value = emptyColumnValue;
//				}
//
//
//				List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
//				if (CollectionUtils.isEmpty(rgb)) {
//					row.add(new CellData(value, totalMarksRowCellLayoutSetup));
//				} else {
//					row.add(new CellData(getParagraph(value, rgb.get(0), rgb.get(1), rgb.get(2)),
//							totalMarksRowCellLayoutSetup));
//				}
//			}
//		}
//		return row;
//	}

	public CourseTotalMarksRows generateCourseTotalMarksInGrid(ExamReportMarksGrid examReportMarksGrid,
														 ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
														 CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
														 boolean showGrade, String totalRowValuesHexColor, String emptyColumnValue,
														 List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure,
															   GridConfigs gridConfigs) throws IOException {
		List<CellData> maxMarksRow = new ArrayList<>();
		List<CellData> obtainedMarksRow = new ArrayList<>();
		List<CellData> percentMarksRow = new ArrayList<>();
		List<CellData> dimensionRow = new ArrayList<>();
		GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();

		String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
		String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

		if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
			courseCellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
		}

		if(!StringUtils.isBlank(gridCoursesTextColor)) {
			rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
		}

		String totalRowBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowBackgroundColor();
		String totalRowTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowTextColor();

		if(!StringUtils.isBlank(totalRowBackgroundColor)) {
			totalMarksRowCellLayoutSetup.setBackgroundColor(totalRowBackgroundColor);
		}

		if(!StringUtils.isBlank(totalRowTextColor)) {
			totalRowValuesHexColor = totalRowTextColor;
		}

		if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
			maxMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getMaxRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
			obtainedMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getObtainedRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
			percentMarksRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getPercentRowTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
			dimensionRow.add(new CellData(getParagraph(gridTotalMarksRowConfigs.getDivisionTitle(), rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
		} else {
			maxMarksRow.add(new CellData(gridTotalMarksRowConfigs.getMaxRowTitle(), courseCellLayoutSetup));
			obtainedMarksRow.add(new CellData(gridTotalMarksRowConfigs.getObtainedRowTitle(), courseCellLayoutSetup));
			percentMarksRow.add(new CellData(gridTotalMarksRowConfigs.getPercentRowTitle(), courseCellLayoutSetup));
			dimensionRow.add(new CellData(gridTotalMarksRowConfigs.getDivisionTitle(), courseCellLayoutSetup));
		}

		totalMarksRowCellLayoutSetup.setPdfFont(getRegularBoldFont());
		for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportMarksGrid
				.getExamReportTotalMarksColumns()) {
			if(examReportCourseMarksColumn.isHide()) {
				continue;
			}
			int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
					examReportCourseMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

			for (ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
					.getExamDimensionObtainedValuesList()) {

				if (dimensionMultiplier == 2) {

					String marks = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
							: String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));

					List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
					if (CollectionUtils.isEmpty(rgb)) {
						maxMarksRow.add(new CellData(marks, totalMarksRowCellLayoutSetup));
						obtainedMarksRow.add(new CellData(marks, totalMarksRowCellLayoutSetup));
						if (gridTotalMarksRowConfigs.isAddTotalPercentColumn()) {
							percentMarksRow.add(new CellData(MAX_PERCENTAGE, totalMarksRowCellLayoutSetup));
						}
// 						We're not adding the "Max Division" column to the grid.
// 						To merge two columns for "Obtained Division", we simply comment out the "Max Division" column.
//						dimensionRow.add(new CellData(EMPTY_TEXT, totalMarksRowCellLayoutSetup));

					} else {
						maxMarksRow.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
								totalMarksRowCellLayoutSetup));
						obtainedMarksRow.add(new CellData(getParagraph(marks, rgb.get(0), rgb.get(1), rgb.get(2)),
								totalMarksRowCellLayoutSetup));
						if (gridTotalMarksRowConfigs.isAddTotalPercentColumn()) {
							percentMarksRow.add(new CellData(getParagraph(MAX_PERCENTAGE, rgb.get(0), rgb.get(1), rgb.get(2)),
									totalMarksRowCellLayoutSetup));
						}
// 						We're not adding the "Max Division" column to the grid.
// 						To merge two columns for "Obtained Division", we simply comment out the "Max Division" column.
//						dimensionRow.add(new CellData(getParagraph(EMPTY_TEXT, rgb.get(0), rgb.get(1), rgb.get(2)),
//								totalMarksRowCellLayoutSetup));
					}
				}

				String maxValue = "";
				String obtainedValue = "";
				String percentValue = "";
				String divisionValue = "";
				if (showGrade
						|| examReportCourseMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE
						|| examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
						|| examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
					String finalGrade = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
									? null : examDimensionObtainedValues.getObtainedGrade().getGradeName(),
							examDimensionObtainedValues.getAttendanceStatus());
					obtainedValue =  finalGrade == null ? emptyColumnValue : finalGrade;
				} else if (examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE) {
					String finalCreditScore = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedGrade() == null
									? null : String.valueOf(examDimensionObtainedValues.getObtainedGrade().getCreditScore()),
							examDimensionObtainedValues.getAttendanceStatus());
					obtainedValue =  finalCreditScore == null ? emptyColumnValue : finalCreditScore;
				} else if (examReportCourseMarksColumn
						.getExamReportGridColumnType() == ExamReportGridColumnType.PERCENT) {
					maxValue = !gridTotalMarksRowConfigs.isAddTotalPercentColumn() ? EMPTY_TEXT : MAX_PERCENTAGE; // As percent max value should be 100?
					String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
									? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 1000) / 10d,
							examDimensionObtainedValues.getAttendanceStatus(), gridConfigs.getGridTotalMarksRowConfigs().isShowPercentSymbolColumn(), false);
					obtainedValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;
					percentValue = obtainedValue;
				} else {
//					value = examDimensionObtainedValues.getObtainedMarks() == null ? emptyColumnValue
//							: String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks()));

					/**
					 * By default, rounding off marks, only removing round if specified specially
					 */
					if(examReportCourseMarksColumn.getRoundOff() != null && !examReportCourseMarksColumn.getRoundOff()) {
						maxValue = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
								: String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getMaxMarks(), 1));

						String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
										? null : String.valueOf(NumberUtils.formatDouble(examDimensionObtainedValues.getObtainedMarks(), 1)),
								examDimensionObtainedValues.getAttendanceStatus());

						obtainedValue = finalObtainedValue == null ? emptyColumnValue : finalObtainedValue;

						String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
										? null : NumberUtils.formatDouble((examDimensionObtainedValues.getObtainedMarksFraction() * 100d), 1),
								examDimensionObtainedValues.getAttendanceStatus(), gridConfigs.getGridTotalMarksRowConfigs().isShowPercentSymbolColumn(), false);
						percentValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;

						divisionValue = ExamDimensionObtainedValues.getFinalDivisionValueDisplay(
								examDimensionObtainedValues.getObtainedMarksFraction() == null
										? null : (examDimensionObtainedValues.getObtainedMarksFraction() * 100d),
								examDimensionObtainedValues.getAttendanceStatus());


					} else {
						maxValue = examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE
								: String.valueOf(Math.round(examDimensionObtainedValues.getMaxMarks()));
						String finalObtainedValue = ExamDimensionObtainedValues.getFinalObtainedValueDisplay(examDimensionObtainedValues.getObtainedMarks() == null
										? null : String.valueOf(Math.round(examDimensionObtainedValues.getObtainedMarks())),
								examDimensionObtainedValues.getAttendanceStatus());

						obtainedValue = finalObtainedValue == null ? examDimensionObtainedValues.getMaxMarks() == null ? NULL_MAX_MARKS_VALUE :
								emptyColumnValue : finalObtainedValue;

						String finalPercentValue = ExamDimensionObtainedValues.getFinalPercentageValueDisplay(examDimensionObtainedValues.getObtainedMarksFraction() == null
										? null : Math.round(examDimensionObtainedValues.getObtainedMarksFraction() * 1000) / 10d,
								examDimensionObtainedValues.getAttendanceStatus(), gridConfigs.getGridTotalMarksRowConfigs().isShowPercentSymbolColumn(), false);
						percentValue = finalPercentValue == null ? emptyColumnValue : finalPercentValue;
						divisionValue = ExamDimensionObtainedValues.getFinalDivisionValueDisplay(
								examDimensionObtainedValues.getObtainedMarksFraction() == null
										? null : (examDimensionObtainedValues.getObtainedMarksFraction() * 100d),
								examDimensionObtainedValues.getAttendanceStatus());

					}
				}

				if (maxValue == null) {
					maxValue = emptyColumnValue;
				}
				if (obtainedValue == null) {
					obtainedValue = emptyColumnValue;
				}
				if (percentValue == null) {
					percentValue = emptyColumnValue;
				}
				if (divisionValue == null) {
					divisionValue = emptyColumnValue;
				}


				List<Integer> rgb = EColorUtils.hex2Rgb(totalRowValuesHexColor);
				if (CollectionUtils.isEmpty(rgb)) {
					maxMarksRow.add(new CellData(maxValue, totalMarksRowCellLayoutSetup));
					obtainedMarksRow.add(new CellData(obtainedValue, totalMarksRowCellLayoutSetup));
					if (gridTotalMarksRowConfigs.isAddTotalPercentColumn()) {
						percentMarksRow.add(new CellData(percentValue, totalMarksRowCellLayoutSetup));
					}
					else {
							percentMarksRow.add(new CellData(percentValue, totalMarksRowCellLayoutSetup, 1, dimensionMultiplier == 2? 2: 1));
					}
					if (dimensionMultiplier == 2) {
						dimensionRow.add(new CellData(divisionValue, totalMarksRowCellLayoutSetup,1, 2));
					}
					else{
						dimensionRow.add(new CellData(divisionValue, totalMarksRowCellLayoutSetup));
					}


				} else {
					maxMarksRow.add(new CellData(getParagraph(maxValue, rgb.get(0), rgb.get(1), rgb.get(2)),
							totalMarksRowCellLayoutSetup));
					obtainedMarksRow.add(new CellData(getParagraph(obtainedValue, rgb.get(0), rgb.get(1), rgb.get(2)),
							totalMarksRowCellLayoutSetup));
					if (gridTotalMarksRowConfigs.isAddTotalPercentColumn()) {
						percentMarksRow.add(new CellData(getParagraph(percentValue, rgb.get(0), rgb.get(1), rgb.get(2)),
							totalMarksRowCellLayoutSetup));
					} else {
						percentMarksRow.add(new CellData(getParagraph(percentValue, rgb.get(0), rgb.get(1), rgb.get(2)),
								totalMarksRowCellLayoutSetup, 1, dimensionMultiplier == 2? 2: 1));
					}
					if (dimensionMultiplier == 2) {
						dimensionRow.add(new CellData(getParagraph(divisionValue, rgb.get(0), rgb.get(1), rgb.get(2)),
								totalMarksRowCellLayoutSetup, 1, 2));
					}
					else{
						dimensionRow.add(new CellData(getParagraph(divisionValue, rgb.get(0), rgb.get(1), rgb.get(2)),
								totalMarksRowCellLayoutSetup));

					}

				}
			}
		}
		return new CourseTotalMarksRows(maxMarksRow, obtainedMarksRow, percentMarksRow, dimensionRow);
	}

	public List<ExamGridAdditionalAttributeRow> generateAdditionalAttributeRowsInGrid(ExamReportMarksGrid examReportMarksGrid,
																			   ExamReportStructureMetaData examReportStructureMetaData, CourseType courseType,
																			   CellLayoutSetup totalMarksRowCellLayoutSetup, CellLayoutSetup courseCellLayoutSetup,
																			   boolean showGrade, String totalRowValuesHexColor, String emptyColumnValue,
																			   List<Integer> rgbCourseNameColorCode, ExamReportCourseStructure examReportCourseStructure,
																			   GridConfigs gridConfigs) throws IOException {

		if(CollectionUtils.isEmpty(examReportMarksGrid.getExamReportAdditionalAttributesRows())){
			return new ArrayList<>();
		}
//		List<CellData> maxMarksRow = new ArrayList<>();
//		List<CellData> obtainedMarksRow = new ArrayList<>();
//		List<CellData> percentMarksRow = new ArrayList<>();
		GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();

		String gridCoursesBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesBackgroundColor();
		String gridCoursesTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getGridCoursesTextColor();

		if(!StringUtils.isBlank(gridCoursesBackgroundColor)) {
			courseCellLayoutSetup.setBackgroundColor(gridCoursesBackgroundColor);
		}

		if(!StringUtils.isBlank(gridCoursesTextColor)) {
			rgbCourseNameColorCode = EColorUtils.hex2Rgb(gridCoursesTextColor);
		}

//		String totalRowBackgroundColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowBackgroundColor();
//		String totalRowTextColor = examReportCourseStructure == null ? null : examReportCourseStructure.getTotalRowTextColor();

//		if(!StringUtils.isBlank(totalRowBackgroundColor)) {
//			totalMarksRowCellLayoutSetup.setBackgroundColor(totalRowBackgroundColor);
//		}
//
//		if(!StringUtils.isBlank(totalRowTextColor)) {
//			totalRowValuesHexColor = totalRowTextColor;
//		}
		List<ExamGridAdditionalAttributeRow> examGridAdditionalAttributeRowList = new ArrayList<>();
		for(ExamReportAdditionalAttributesRow examReportAdditionalAttributesRow : examReportMarksGrid.getExamReportAdditionalAttributesRows()){
			if(examReportAdditionalAttributesRow.getAttribute() == ExamGridRowAttribute.ATTENDANCE && !gridTotalMarksRowConfigs.isAddAttendanceRow()){
				continue;
			}
			List<CellData> columns = new ArrayList<>();
			String title = null;
			switch (examReportAdditionalAttributesRow.getAttribute()){
				case EXAM_DATE:
					title = gridTotalMarksRowConfigs.getExamDateTitle();
					break;
				case RESULT_DATE:
					title = gridTotalMarksRowConfigs.getResultDateTitle();
					break;
				case ATTENDANCE:
					title = gridTotalMarksRowConfigs.getAttendanceTitle();
					break;
				case DIVISION:
					title = gridTotalMarksRowConfigs.getDivisionTitle();
					break;
				case RANK:
					title = gridTotalMarksRowConfigs.getRankTitle();
					break;
				case RESULT:
					title = gridTotalMarksRowConfigs.getResultTitle();
					break;
			}
			if(!CollectionUtils.isEmpty(rgbCourseNameColorCode)) {
				columns.add(new CellData(getParagraph(title, rgbCourseNameColorCode.get(0), rgbCourseNameColorCode.get(1), rgbCourseNameColorCode.get(2)), courseCellLayoutSetup));
			} else {
				columns.add(new CellData(title, courseCellLayoutSetup));
			}

//			totalMarksRowCellLayoutSetup.setPdfFont(getRegularBoldFont());

			for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow
					.getExamReportMarksColumns()) {
				if(examReportMarksColumn.isHide()) {
					continue;
				}
				int dimensionMultiplier = getDimensionMultiplier(examReportStructureMetaData,
						examReportMarksColumn.getExamReportGridColumnType(), courseType, showGrade);

				String columnValue = examReportMarksColumn.getColumnValue();
				if (columnValue == null) {
					columnValue = emptyColumnValue;
				}

				if (dimensionMultiplier == 2) {
					columns.add(new CellData(columnValue, totalMarksRowCellLayoutSetup, 1, 2));
				} else {
					columns.add(new CellData(columnValue, totalMarksRowCellLayoutSetup));
				}
			}

			examGridAdditionalAttributeRowList.add(new ExamGridAdditionalAttributeRow(examReportAdditionalAttributesRow.getAttribute(), columns));
		}

		return examGridAdditionalAttributeRowList;
	}

	public void generateDynamicPersonalityTraitsGridAndCoScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont, float coscholasticColumnWidths, String personalityTraitsTitle, GridConfigs gridConfigs) throws IOException {
		if (gridConfigs == null || 
			gridConfigs.getGridPersonalityTraitsConfigs() == null) {
			return;
		}
		Map<String, String> personalityTraitKeyValueMap = getPersonalityTraitKeyValueData(examReportData.getStudentPersonalityTraitsResponseList());
		if(gridConfigs.getGridPersonalityTraitsConfigs().getPersonalityTraitsColumnType() != null && gridConfigs.getGridPersonalityTraitsConfigs().getPersonalityTraitsColumnType() == PersonalityTraitsColumnType.ONLY_PERSONALITY_TRAITS) {
			generatePersonalityTraitSection(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1, examReportCardLayoutData.getDefaultBorderWidth(), personalityTraitKeyValueMap, gridConfigs, examReportData, regularFont, boldFont, personalityTraitsTitle);
		}else{
			generateCustomeCoScholasticMarksGridWithPersonalityTraits(examReportCardLayoutData, examReportData, reportType, boldFont, regularFont, coscholasticColumnWidths, personalityTraitsTitle);
		}
	}

	public void generateCustomeCoScholasticMarksGridWithPersonalityTraits(ExamReportCardLayoutData examReportCardLayoutData, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont, float coscholasticColumnWidths, String personalityTraitColumnTitle) throws IOException {
        Map<String, String> personalityTraitKeyValueMap = getPersonalityTraitKeyValueData(examReportData.getStudentPersonalityTraitsResponseList());
        if(examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
            if(!MapUtils.isEmpty(personalityTraitKeyValueMap)){
                generateCoScholasticMarksGridWithPersonalityTraitsSection(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,  examReportCardLayoutData.getDefaultBorderWidth(),
                examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC), false, examReportData,
                regularFont, boldFont, personalityTraitKeyValueMap, false, personalityTraitColumnTitle);
            }
            else{
                generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
                    coscholasticColumnWidths);
            }

        }else if(!MapUtils.isEmpty(personalityTraitKeyValueMap)){
            generateCoScholasticMarksGridWithPersonalityTraitsSection(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,  examReportCardLayoutData.getDefaultBorderWidth(),
                examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.COSCHOLASTIC), false, examReportData,
                regularFont, boldFont, personalityTraitKeyValueMap, true, personalityTraitColumnTitle);
        }
    }

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, ExamReportData examReportData, GridConfigs gridConfigs,
			String subjectColumnTitle, float subjectColumnWidth, Set<UUID> coursesToDisplay, String totalRowHexColorCode) throws IOException {

		generateScholasticMarksGrid(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(),
				contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, coursesToDisplay, "MM", "MO", totalRowHexColorCode);

	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, ExamReportData examReportData, GridConfigs gridConfigs,
			String subjectColumnTitle, float subjectColumnWidth, Set<UUID> coursesToDisplay, String totalRowHexColorCode,
			List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode, PdfFont regularFont, PdfFont boldFont) throws IOException {

		generateScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont,
				contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, coursesToDisplay, "MM", "MO", totalRowHexColorCode,
				rgbGridHeadingColorCode, rgbCourseNameColorCode);

	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
											ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
											Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode) throws IOException {

		generateScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, coursesToDisplay, maxMarksTitle,
				marksObtainedTitle, totalRowHexColorCode, "-");
	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode, String emptyColumnValue) throws IOException {

		generateScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
			examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, coursesToDisplay, maxMarksTitle,
			marksObtainedTitle, totalRowHexColorCode, emptyColumnValue, false);
			
	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode, String emptyColumnValue, boolean additionalCourses) throws IOException {

		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.SCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.SCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isScholasticGradingEnabled(), coursesToDisplay,
				maxMarksTitle, marksObtainedTitle, totalRowHexColorCode, emptyColumnValue);

	}
	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
											ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
											Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode,
											List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode) throws IOException {

			generateScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle,
			subjectColumnWidth, coursesToDisplay, maxMarksTitle, marksObtainedTitle, totalRowHexColorCode, rgbGridHeadingColorCode, rgbCourseNameColorCode, false);
	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
											ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
											Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode,
											List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode, boolean additionalCourses) throws IOException {

		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.SCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.SCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isScholasticGradingEnabled(), coursesToDisplay,
				maxMarksTitle, marksObtainedTitle, totalRowHexColorCode, rgbGridHeadingColorCode, rgbCourseNameColorCode);

	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle,
			CellLayoutSetup courseCellLayoutSetup, CellLayoutSetup marksCellLayoutSetup, String totalRowHexColorCode) throws IOException {

				generateScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, coursesToDisplay, maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup, marksCellLayoutSetup, totalRowHexColorCode, false);
	}

	public void generateScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			Set<UUID> coursesToDisplay, String maxMarksTitle, String marksObtainedTitle,
			CellLayoutSetup courseCellLayoutSetup, CellLayoutSetup marksCellLayoutSetup, String totalRowHexColorCode, boolean additionalCourses) throws IOException {

		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.SCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.SCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isScholasticGradingEnabled(), coursesToDisplay,
				maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup, marksCellLayoutSetup, totalRowHexColorCode, "-");

	}


	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											  float contentFontSize, float defaultBorderWidth, ExamReportData examReportData, GridConfigs gridConfigs,
											  String subjectColumnTitle, float subjectColumnWidth) throws IOException {
		generateCoScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, examReportData, gridConfigs,
			subjectColumnTitle, subjectColumnWidth, "-");
	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
		float contentFontSize, float defaultBorderWidth, ExamReportData examReportData, GridConfigs gridConfigs,
		String subjectColumnTitle, float subjectColumnWidth, String emptyColumnValue) throws IOException {

		generateCoScholasticMarksGrid(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(),
				contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, "MM", "MO", emptyColumnValue);

	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, ExamReportData examReportData, GridConfigs gridConfigs,
			String subjectColumnTitle, float subjectColumnWidth,
			List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode,
											  PdfFont nunitoRegular, PdfFont nunitoBold) throws IOException {

		generateCoScholasticMarksGrid(document, documentLayoutSetup, nunitoRegular, nunitoBold,
				contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, "MM", "MO", rgbGridHeadingColorCode, rgbCourseNameColorCode);

	}
	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											  PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
											  ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
											  String maxMarksTitle, String marksObtainedTitle,
											  List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode) throws IOException {

        generateCoScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth,
		maxMarksTitle, marksObtainedTitle, rgbGridHeadingColorCode, rgbCourseNameColorCode, false);

	}
	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											  PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
											  ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
											  String maxMarksTitle, String marksObtainedTitle,
											  List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode, boolean additionalCourses) throws IOException {


		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.COSCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}
		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.COSCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isCoScholasticGradingEnabled(), null,
				maxMarksTitle, marksObtainedTitle, null, rgbGridHeadingColorCode, rgbCourseNameColorCode);

	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											  PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
											  ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
											  String maxMarksTitle, String marksObtainedTitle) throws IOException {

		generateCoScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
			examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, maxMarksTitle, marksObtainedTitle, "-");
	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			String maxMarksTitle, String marksObtainedTitle, String emptyColumnValue) throws IOException {

		generateCoScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
			examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, maxMarksTitle, marksObtainedTitle, emptyColumnValue, false);
	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			String maxMarksTitle, String marksObtainedTitle, String emptyColumnValue, boolean additionalCourses) throws IOException {

		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.COSCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.COSCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isCoScholasticGradingEnabled(), null,
				maxMarksTitle, marksObtainedTitle, null, emptyColumnValue);

	}
	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											  float contentFontSize, float defaultBorderWidth,
											  ExamReportData examReportData, GridConfigs gridConfigs,
											  String subjectColumnTitle, float subjectColumnWidth,
											  Set<UUID> coursesToDisplay, String totalRowHexColorCode) throws IOException {

		generateCoScholasticMarksGrid(document, documentLayoutSetup, contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, coursesToDisplay,
		totalRowHexColorCode, false);
	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
											  float contentFontSize, float defaultBorderWidth,
											  ExamReportData examReportData, GridConfigs gridConfigs,
											  String subjectColumnTitle, float subjectColumnWidth,
											  Set<UUID> coursesToDisplay, String totalRowHexColorCode, boolean additionalCourses) throws IOException {

		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.COSCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}
		generateMarksGrid(document, documentLayoutSetup, getRegularFont(), getRegularBoldFont(),
				contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.COSCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isCoScholasticGradingEnabled(), coursesToDisplay,
				"MM", "MO", totalRowHexColorCode, "-");

	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			String maxMarksTitle, String marksObtainedTitle, CellLayoutSetup courseCellLayoutSetup,
			CellLayoutSetup marksCellLayoutSetup) throws IOException {

		generateCoScholasticMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth, examReportData, gridConfigs, subjectColumnTitle, subjectColumnWidth, maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup, marksCellLayoutSetup, false);
	}

	public void generateCoScholasticMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup,
			PdfFont regularFont, PdfFont boldFont, float contentFontSize, float defaultBorderWidth,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth,
			String maxMarksTitle, String marksObtainedTitle, CellLayoutSetup courseCellLayoutSetup,
			CellLayoutSetup marksCellLayoutSetup, boolean additionalCourses) throws IOException {

		Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid = new HashMap<>();
		if(additionalCourses){
			courseTypeExamReportMarksGrid = examReportData.getAdditionalCourseTypeExamReportMarksGrid();
		}
		else{
			courseTypeExamReportMarksGrid = examReportData.getCourseTypeExamReportMarksGrid();
		}

		ExamReportMarksGrid examReportMarksGrid = courseTypeExamReportMarksGrid.get(CourseType.COSCHOLASTIC);
		if (examReportMarksGrid == null) {
			logger.error("examReportMarksGrid is not available, Skipping...");
			return;
		}
		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, CourseType.COSCHOLASTIC, gridConfigs, subjectColumnTitle,
				subjectColumnWidth, examReportData.getStandardMetaData().isCoScholasticGradingEnabled(), null,
				maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup, marksCellLayoutSetup, null, "-");

	}

	private void generateMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont regularFont,
			PdfFont boldFont, float contentFontSize, float defaultBorderWidth, ExamReportData examReportData,
			ExamReportMarksGrid examReportMarksGrid, CourseType courseType, GridConfigs gridConfigs,
			String subjectColumnTitle, float subjectColumnWidth, boolean showGrade, Set<UUID> coursesToDisplay,
			String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode, String emptyColumnValue) throws IOException {

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
				.setTextAlignment(TextAlignment.LEFT).setPaddingLeft(5f);

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, courseType, gridConfigs, subjectColumnTitle, subjectColumnWidth,
				showGrade, coursesToDisplay, maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup,
				marksCellLayoutSetup, totalRowHexColorCode, emptyColumnValue);
	}

	private void generateMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont regularFont,
								   PdfFont boldFont, float contentFontSize, float defaultBorderWidth, ExamReportData examReportData,
								   ExamReportMarksGrid examReportMarksGrid, CourseType courseType, GridConfigs gridConfigs,
								   String subjectColumnTitle, float subjectColumnWidth, boolean showGrade, Set<UUID> coursesToDisplay,
								   String maxMarksTitle, String marksObtainedTitle, String totalRowHexColorCode,
								   List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode) throws IOException {

		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(contentFontSize)
				.setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

		CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
				.setTextAlignment(TextAlignment.LEFT).setPaddingLeft(5f);

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, courseType, gridConfigs, subjectColumnTitle, subjectColumnWidth,
				showGrade, coursesToDisplay, maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup,
				marksCellLayoutSetup, totalRowHexColorCode, rgbGridHeadingColorCode, rgbCourseNameColorCode, "-");
	}

	private void generateMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont regularFont,
								   PdfFont boldFont, float contentFontSize, float defaultBorderWidth, ExamReportData examReportData,
								   ExamReportMarksGrid examReportMarksGrid, CourseType courseType, GridConfigs gridConfigs,
								   String subjectColumnTitle, float subjectColumnWidth, boolean showGrade, Set<UUID> coursesToDisplay,
								   String maxMarksTitle, String marksObtainedTitle, CellLayoutSetup courseCellLayoutSetup,
								   CellLayoutSetup marksCellLayoutSetup, String totalRowHexColorCode, String emptyColumnValue) throws IOException {

		generateMarksGrid(document, documentLayoutSetup, regularFont, boldFont, contentFontSize, defaultBorderWidth,
				examReportData, examReportMarksGrid, courseType, gridConfigs, subjectColumnTitle, subjectColumnWidth,
				showGrade, coursesToDisplay, maxMarksTitle, marksObtainedTitle, courseCellLayoutSetup,
				marksCellLayoutSetup, totalRowHexColorCode, null, null, emptyColumnValue);
	}

	private void generateMarksGrid(Document document, DocumentLayoutSetup documentLayoutSetup, PdfFont regularFont,
			PdfFont boldFont, float contentFontSize, float defaultBorderWidth, ExamReportData examReportData,
			ExamReportMarksGrid examReportMarksGrid, CourseType courseType, GridConfigs gridConfigs,
								   String subjectColumnTitle, float subjectColumnWidth, boolean showGrade, Set<UUID> coursesToDisplay,
			String maxMarksTitle, String marksObtainedTitle, CellLayoutSetup courseCellLayoutSetup,
			CellLayoutSetup marksCellLayoutSetup, String totalRowHexColorCode,
			List<Integer> rgbGridHeadingColorCode, List<Integer> rgbCourseNameColorCode, String emptyColumnValue) throws IOException {

		ExamReportStructureMetaData examReportStructureMetaData = examReportData.getExamReportStructure()
				.getExamReportStructureMetaData();
		float[] columnWidths = getMarksGridWidth(examReportMarksGrid, courseType, false, examReportStructureMetaData,
				subjectColumnWidth, showGrade, false, 0f);
		Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);

		ExamReportCourseStructure examReportCourseStructure = examReportData.getExamReportStructure().getExamReportCourseStructure() == null ? null :
				CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()) ? null :
						examReportData.getExamReportStructure().getExamReportCourseStructure().get(courseType);
		addRow(headerTable, documentLayoutSetup,
				getHeaderCells(examReportMarksGrid, examReportStructureMetaData, courseType, defaultBorderWidth,
						regularFont, boldFont, contentFontSize - 2, contentFontSize, subjectColumnWidth,
						subjectColumnTitle, showGrade, maxMarksTitle, marksObtainedTitle, false,
						rgbGridHeadingColorCode, examReportCourseStructure, gridConfigs));

		fillCourseMarksInGrid(documentLayoutSetup, examReportMarksGrid, examReportData.getExamReportStructure(),
				courseType, headerTable, marksCellLayoutSetup, courseCellLayoutSetup, showGrade, coursesToDisplay, rgbCourseNameColorCode,
				emptyColumnValue, examReportCourseStructure, gridConfigs);

		if (gridConfigs != null && gridConfigs.getGridTotalMarksRowConfigs() != null) {
			GridTotalMarksRowConfigs gridTotalMarksRowConfigs = gridConfigs.getGridTotalMarksRowConfigs();
			boolean addMaxRowInTotalRow = gridTotalMarksRowConfigs.isAddMaxRowInTotalRow();
			boolean addTotalObtainedMarksRow = gridTotalMarksRowConfigs.isAddTotalRow();
			boolean addPercentRowInTotalRow =  gridTotalMarksRowConfigs.isAddPercentRowInTotalRow();
			boolean addDivisionRowInTotalRow = gridTotalMarksRowConfigs.isAddDivisionRow();

			CourseTotalMarksRows courseTotalMarksRows = generateCourseTotalMarksInGrid(examReportMarksGrid, examReportStructureMetaData,
					courseType, marksCellLayoutSetup, courseCellLayoutSetup, showGrade, totalRowHexColorCode, emptyColumnValue, examReportCourseStructure, gridConfigs);

			if(addTotalObtainedMarksRow) {
				addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getObtainedMarksRow());
			}
			if(addMaxRowInTotalRow){
				addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getMaxMarksRow());
			}
			if(addPercentRowInTotalRow){
				addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getPercentMarksRow());
			}
			if(addDivisionRowInTotalRow){
				addRow(headerTable, documentLayoutSetup, courseTotalMarksRows.getDivisionRow());
			}
			List<ExamGridAdditionalAttributeRow> examGridAdditionalAttributeRowList = generateAdditionalAttributeRowsInGrid(examReportMarksGrid, examReportStructureMetaData,
					courseType, marksCellLayoutSetup, courseCellLayoutSetup, showGrade, totalRowHexColorCode, emptyColumnValue, null, examReportCourseStructure, gridConfigs);
			if(!CollectionUtils.isEmpty(examGridAdditionalAttributeRowList)){
				for(ExamGridAdditionalAttributeRow examGridAdditionalAttributeRow : examGridAdditionalAttributeRowList){
					addRow(headerTable, documentLayoutSetup, examGridAdditionalAttributeRow.getColumns());
				}
			}
		}

		document.add(headerTable);
	}

	protected void addBlankLine(ExamReportCardLayoutData examReportCardLayoutData, boolean nextLine, int count) {
		for (int i = 0; i < count; i++) {
			examReportCardLayoutData.getDocument().add(new Paragraph(nextLine ? NEXT_LINE : EMPTY_TEXT));
		}
	}

	protected void generateLogo(ExamReportCardLayoutData examReportCardLayoutData, Institute institute, byte[] logo,
			float offsetX, float offsetY) throws MalformedURLException, IOException {

		generateImage(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(), logo,
				examReportCardLayoutData.getLogoWidth(), examReportCardLayoutData.getLogoHeight(), offsetX, offsetY);
	}

	protected void generateWatermark(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										   float height, float offsetX, float offsetY) {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	protected void generateStudentImage(int instituteId, UUID studentId, Document document, DocumentLayoutSetup documentLayoutSetup,
									  StudentManager studentManager, float width, float height, float offsetX, float offsetY) {

		byte[] image = getStudentImage(instituteId, studentId, studentManager);
		if (image == null) {
			return;
		}
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	protected void generateClassReportDataPageHeader(DocumentLayoutSetup documentLayoutSetup, Document document, PdfFont boldFont, String instituteName,
									  String sessionName, CellLayoutSetup marksCellLayoutSetup) {
		Table table = getPDFTable(documentLayoutSetup, 1);
		String headerText = StringUtils.isBlank(sessionName) ? instituteName : instituteName + " (" + sessionName + ")";
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(headerText)), marksCellLayoutSetup.copy());
		document.add(table);
	}

	protected void generateClassReportDataStudentDetails(DocumentLayoutSetup documentLayoutSetup, Document document,
													 CellLayoutSetup courseCellLayoutSetup, StudentLite studentLite) {

		Table table = getPDFTable(documentLayoutSetup, 3);
		String admissionNumber = "SR No. : " + (StringUtils.isBlank(studentLite.getAdmissionNumber())
				? NOT_AVAILABLE : studentLite.getAdmissionNumber());
		String studentName = "Student Name : " + (StringUtils.isBlank(studentLite.getName())
				? NOT_AVAILABLE : studentLite.getName());
		String fatherName = "Father Name : " + (StringUtils.isBlank(studentLite.getFathersName())
				? NOT_AVAILABLE : studentLite.getFathersName());
		String className = "Class : " + (StringUtils.isBlank(studentLite.getStudentSessionData()
				.getStandardNameWithSection()) ? NOT_AVAILABLE : studentLite.getStudentSessionData()
				.getStandardNameWithSection());
		String rollNumber = "Roll No. : " + (StringUtils.isBlank(studentLite.getStudentSessionData()
				.getRollNumber()) ? NOT_AVAILABLE : studentLite.getStudentSessionData().getRollNumber());
		String motherName = "Mother Name : " + (StringUtils.isBlank(studentLite.getMothersName())
				? NOT_AVAILABLE : studentLite.getMothersName());

		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph(admissionNumber), getParagraph(studentName), getParagraph(fatherName)),
				courseCellLayoutSetup.copy());
		addRow(table, documentLayoutSetup, Arrays.asList(
						getParagraph(className), getParagraph(rollNumber), getParagraph(motherName)),
				courseCellLayoutSetup.copy().setBorderBottom(null));
		document.add(table);
	}

	protected void generateClassReportDataStudentResult(DocumentLayoutSetup documentLayoutSetup, Document document,
														ExamReportData examReportData, CellLayoutSetup courseCellLayoutSetup) {

		Table table = getPDFTable(documentLayoutSetup, 3);

		String percentageValue = examReportData.getPercentage() == null ? NOT_AVAILABLE :
				String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%";
		String percentage = "Percentage : " + percentageValue;

		String gradeValue = examReportData.getTotalGrade() == null ? NOT_AVAILABLE : examReportData.getTotalGrade().getGradeName();
		String grade = "Overall Grade : " + gradeValue;

		String resultValue = examReportData.getExamResultStatus() == null ? NOT_AVAILABLE : examReportData.getExamResultStatus().getDisplayName();
		String result = "Result : " +  resultValue;

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(percentage), getParagraph(grade),
				getParagraph(result)), courseCellLayoutSetup.copy().setBorderTop(null));
		document.add(table);
	}
}
