package com.embrate.cloud.core.utils;


import java.util.List;

/**
 * Utility class for List related operations.
 */
public class EListUtils {

    public static interface ListToStringFunction<T> {
        String getString(T entry);
    }

    public static <T> String convertListToString(List<T> list, ListToStringFunction<T> func) {
        return convertListToString(list, func, ", ");
    }

    public static <T> String convertListToString(List<T> list, ListToStringFunction<T> func, String delimiter) {
        if (list == null || list.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        String currentDelimiter = "";
        for (T entry : list) {
            if (entry == null) {
                continue;
            }
            String value = func.getString(entry);
            if (value != null) {
                result.append(currentDelimiter).append(value);
                currentDelimiter = delimiter;
            }
        }
        return result.toString();
    }
}

