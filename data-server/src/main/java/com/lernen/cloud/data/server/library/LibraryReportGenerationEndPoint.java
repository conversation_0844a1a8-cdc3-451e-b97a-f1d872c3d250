package com.lernen.cloud.data.server.library;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.UUID;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.HTMLReportTable;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.user.Module;
import com.embrate.cloud.pdf.reports.PdfReportGenerator;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.library.report.LibraryReportFilterationCriteria;
import com.lernen.cloud.core.lib.library.report.LibraryReportGenerator;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.utils.report.ReportUtils;

/**
 * REST endpoint for library report generation
 */
@Path("/2.0/library/reports")
public class LibraryReportGenerationEndPoint {

    private static final Logger logger = LogManager.getLogger(LibraryReportGenerationEndPoint.class);

    private final LibraryReportGenerator libraryReportGenerator;
    private final ExcelReportGenerator excelReportGenerator;
	private final PdfReportGenerator pdfReportGenerator;

    public LibraryReportGenerationEndPoint(LibraryReportGenerator libraryReportGenerator,
                                           ExcelReportGenerator excelReportGenerator,
                                           PdfReportGenerator pdfReportGenerator) {
        this.libraryReportGenerator = libraryReportGenerator;
        this.excelReportGenerator = excelReportGenerator;
        this.pdfReportGenerator = pdfReportGenerator;
    }

    /**
     * Generate library report for viewing (returns HTML table format)
     */
    @POST
    @Path("/view-report/{institute_id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getReportData(@PathParam("institute_id") int instituteId,
                                  @QueryParam("user_id") UUID userId,
                                  LibraryReportFilterationCriteria filterationPayload) {

        logger.info("Generating library report view for institute: {}", instituteId);

        ReportDetails reportDetails = libraryReportGenerator.generateReport(
            instituteId, userId, filterationPayload, null);

        if(reportDetails == null){
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
		}

		HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
		return Response.status(Response.Status.OK.getStatusCode()).entity(htmlReportTable).build();
    }

    /**
     * Generate library report for download (returns file stream)
     */
    @POST
    @Path("/generate-report/{institute_id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response generateReport(@PathParam("institute_id") int instituteId,
                                   @QueryParam("user_id") UUID userId,
                                   LibraryReportFilterationCriteria filterationPayload,
                                   @QueryParam("download_format") DownloadFormat downloadFormat) throws IOException {

            logger.info("Generating library report download for institute: {}, format: {}", instituteId, downloadFormat);

            // Default to PDF if no format specified
            if (downloadFormat == null) {
                downloadFormat = DownloadFormat.PDF;
            }

            final ReportDetails reportDetails = libraryReportGenerator.generateReport(
                instituteId, userId, filterationPayload, downloadFormat);

            if (reportDetails == null) {
			return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
		}
        
		if (downloadFormat == DownloadFormat.PDF) {
			final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails, Module.LIBRARY_MANAGEMENT);
			if (documentOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
					.header("Content-Disposition", "filename=" + documentOutput.getName())
					.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
		} else {
			final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
			if (reportOutput == null) {
				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
			}
			return Response.status(Response.Status.OK.getStatusCode())
					.header("Content-Disposition", "attachment;filename=" + reportOutput.getReportName())
					.entity(new ByteArrayInputStream(reportOutput.getReportContent().toByteArray())).build();
		}

   
    }
}
