package com.lernen.cloud.dao.tier.library.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.library.BookStatus;
import com.lernen.cloud.core.api.library.IndividualBookDetails;
import com.lernen.cloud.core.api.library.LibraryType;
import com.lernen.cloud.core.api.library.Vendor;
import com.lernen.cloud.core.api.library.report.LibraryReportDetailedData;
import com.lernen.cloud.core.api.library.report.LibraryReportDetailedDataRow;

public class LibraryDetailedReportMapper implements RowMapper<LibraryReportDetailedDataRow> {

    // Book fields
    private static final String BOOK_ID = "book_details.book_id";
    private static final String BOOK_TITLE = "book_details.book_title";
    private static final String ISBN_NUMBER = "book_details.isbn_number";
    private static final String EDITION = "book_details.edition";
    private static final String PUBLISH_YEAR = "book_details.publish_year";
    private static final String LANGUAGE = "book_details.language";
    private static final String NO_OF_COPIES = "book_details.no_of_copies";
    private static final String ADDED_AT = "book_details.added_at";
    private static final String INSTITUTE_ID = "book_details.institute_id";

    // Genre fields
    private static final String GENRE_ID = "genre.genre_id";
    private static final String GENRE_NAME = "genre.genre_name";
    private static final String CLASSIFICATION_NUMBER = "genre.classification_number";

    // Author fields
    private static final String AUTHOR_ID = "author.author_id";
    private static final String AUTHOR_NAME = "author.author_name";

    // Publication fields
    private static final String PUBLICATION_ID = "publication.publication_id";
    private static final String PUBLICATION_NAME = "publication.publication_name";

    // Publisher fields
    private static final String PUBLISHER_ID = "publisher.publisher_id";
    private static final String PUBLISHER_NAME = "publisher.publisher_name";

    // Individual book details fields
    private static final String ACCESSION_ID = "individual_book_details.accession_id";
    private static final String ACCESSION_NUMBER = "individual_book_details.accession_number";
    private static final String RACK = "individual_book_details.rack";

    private static final String BILL_NUMBER = "individual_book_details.bill_number";
    private static final String INDIVIDUAL_ADDED_ON = "individual_book_details.added_on";
    private static final String STATUS = "individual_book_details.status";
    private static final String VOLUME = "individual_book_details.volume";
    private static final String REMARK = "individual_book_details.remark";

    // Vendor fields
    private static final String VENDOR_ID = "vendor.vendor_id";
    private static final String VENDOR_NAME = "vendor.vendor_name";

    // Library type fields
    private static final String LIBRARY_TYPE_ID = "library_type.library_type_id";
    private static final String LIBRARY_TYPE_NAME = "library_type.library_type_name";

    @Override
    public LibraryReportDetailedDataRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(BOOK_ID) == null) {
            return null;
        }

        UUID bookId = UUID.fromString(rs.getString(BOOK_ID));
        String bookTitle = rs.getString(BOOK_TITLE);
        String isbn = rs.getString(ISBN_NUMBER);
        String edition = rs.getString(EDITION);
        int publishYear = rs.getInt(PUBLISH_YEAR);
        String language = rs.getString(LANGUAGE);
        int totalCopies = rs.getInt(NO_OF_COPIES);
        Integer addedAt = rs.getTimestamp(ADDED_AT) != null ? 
            (int) (rs.getTimestamp(ADDED_AT).getTime() / 1000L) : null;

        String genre = rs.getString(GENRE_NAME);
        String author = rs.getString(AUTHOR_NAME);
        String publication = rs.getString(PUBLICATION_NAME);
        String publisher = rs.getString(PUBLISHER_NAME);

        // Map Vendor
        Vendor vendor = null;
        if (rs.getString(VENDOR_ID) != null) {
            vendor = new Vendor(UUID.fromString(rs.getString(VENDOR_ID)), 
                              rs.getString(VENDOR_NAME), "", "", "", "", "", "", "", "", "");
        }

        // Map Library Type
        LibraryType libraryType = null;
        if (rs.getString(LIBRARY_TYPE_ID) != null) {
            libraryType = new LibraryType(UUID.fromString(rs.getString(LIBRARY_TYPE_ID)), 
                                        rs.getString(LIBRARY_TYPE_NAME));
        }

        // Map Individual Book Details
        IndividualBookDetails individualBookDetails = null;
        if (rs.getString(ACCESSION_ID) != null) {
            UUID accessionId = UUID.fromString(rs.getString(ACCESSION_ID));
            String accessionNumber = rs.getString(ACCESSION_NUMBER);
            String rack = rs.getString(RACK);
            String billNumber = rs.getString(BILL_NUMBER);
            Integer individualAddedOn = rs.getTimestamp(INDIVIDUAL_ADDED_ON) != null ? 
                (int) (rs.getTimestamp(INDIVIDUAL_ADDED_ON).getTime() / 1000L) : null;
            BookStatus status = BookStatus.getStatusFromString(rs.getString(STATUS));
            String volume = rs.getString(VOLUME);
            String remark = rs.getString(REMARK);
            int instituteId = rs.getInt(INSTITUTE_ID);

            individualBookDetails = new IndividualBookDetails(accessionId, instituteId, 
                bookId, accessionNumber, rack, 0, null, billNumber, 
                 null, individualAddedOn, vendor, status, volume, libraryType, remark);
        }

        return new LibraryReportDetailedDataRow(bookId, bookTitle, isbn, author, genre, 
                                              publication, publisher, edition, publishYear, 
                                              language, totalCopies, addedAt, individualBookDetails);
    }

    public static List<LibraryReportDetailedData> convertToGroupedResponse(List<LibraryReportDetailedDataRow> rows) {
        List<LibraryReportDetailedData> result = new ArrayList<>();
        Map<UUID, LibraryReportDetailedData> bookMap = new HashMap<>();

        for (LibraryReportDetailedDataRow row : rows) {
            UUID bookId = row.getBookId();
            LibraryReportDetailedData existing = bookMap.get(bookId);

            if (existing == null) {
                // First time this book is seen
                List<IndividualBookDetails> individualBookDetailsList = new ArrayList<>();
                if (row.getIndividualBookDetails() != null) {
                    individualBookDetailsList.add(row.getIndividualBookDetails());
                }

                LibraryReportDetailedData newData = new LibraryReportDetailedData(
                    row.getBookId(), row.getBookTitle(), row.getIsbn(), row.getAuthor(),
                    row.getGenre(), row.getPublication(), row.getPublisher(), 
                    row.getEdition(), row.getPublishYear(), row.getLanguage(),
                    row.getTotalCopies(), row.getAddedAt(), individualBookDetailsList);
                
                bookMap.put(bookId, newData);
            } else {
                // Add individual book details to existing book
                if (row.getIndividualBookDetails() != null) {
                    existing.getIndividualBookDetails().add(row.getIndividualBookDetails());
                }
            }
        }

        result.addAll(bookMap.values());
        return result;
    }
}
