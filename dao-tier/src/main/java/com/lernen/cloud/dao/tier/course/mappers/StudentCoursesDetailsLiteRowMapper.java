package com.lernen.cloud.dao.tier.course.mappers;

import com.amazonaws.util.CollectionUtils;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.StudentCoursesDetailsLite;
import com.lernen.cloud.core.api.course.StudentCoursesDetailsLiteRow;
import com.lernen.cloud.core.api.course.StudentInfoDetails;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.institute.Stream;

import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class StudentCoursesDetailsLiteRowMapper implements RowMapper<StudentCoursesDetailsLiteRow> {

    private static final String ACADEMIC_SESSION_ID = "academic_session_id";
    // private static final StudentLiteRowMapper STUDENT_LITE_ROW_MAPPER = new StudentLiteRowMapper();
    private static final CourseRowMapper COURSE_ROW_MAPPER = new CourseRowMapper();

    private static final String STUDENT_ID = "students.student_id";
    private static final String NAME = "student_name";
    private static final String ADMISSION_NUMBER = "student_admission_number";
    private static final String ADMISSION_DATE = "student_admission_date";
    private static final String FATHER_NAME = "father_name";
    private static final String SECTION_NAME = "student_section";
    private static final String ROLL_NUMBER = "student_roll_number";
    private static final String STANDARD_LEVEL = "standard_level";

    @Override
    public StudentCoursesDetailsLiteRow mapRow(ResultSet rs, int rowNum) throws SQLException {
        Course course = COURSE_ROW_MAPPER.mapRow(rs, rowNum);
        // StudentAcademicDetails studentAcademicDetails = new 
        UUID studentId = UUID.fromString(rs.getString(STUDENT_ID));
        String name = WordUtils.capitalizeFully(rs.getString(NAME));
        String admissionNumber = rs.getString(ADMISSION_NUMBER);
        Integer admissionDate = rs.getTimestamp(ADMISSION_DATE) == null ? null
            : (int) (rs.getTimestamp(ADMISSION_DATE).getTime() / 1000);
        String fathersName = WordUtils.capitalizeFully(rs.getString(FATHER_NAME));
        String rollNumber = rs.getString(ROLL_NUMBER);
        // Build class name from standard name and stream
        String standardName = null;
        Stream stream = null;
        String sectionName = rs.getString(SECTION_NAME);
    
        String className = Standard.getStandardDisplayName(stream, standardName, 
            StringUtils.isBlank(sectionName) ? null : new StandardSections(0, sectionName));

        int standardLevel = rs.getInt(STANDARD_LEVEL);
        StudentInfoDetails studentInfoDetails = new StudentInfoDetails(studentId, name, admissionNumber, admissionDate, fathersName, null, sectionName, rollNumber, standardLevel);

        return new StudentCoursesDetailsLiteRow(rs.getInt(ACADEMIC_SESSION_ID), studentInfoDetails, course);
    }
    
    public static List<StudentCoursesDetailsLite> getStudentAssignedCoursesDetails(List<StudentCoursesDetailsLiteRow> studentCoursesRowList) {

        if (CollectionUtils.isNullOrEmpty(studentCoursesRowList)) {
            return new ArrayList<>();
        }

        Map<UUID, StudentCoursesDetailsLite> studentDetailsMap = new HashMap<>();
        for (StudentCoursesDetailsLiteRow studentCoursesRow : studentCoursesRowList) {
            if (studentCoursesRow == null || studentCoursesRow.getStudentInfoDetails() == null
                    || studentCoursesRow.getCourse() == null) {
                continue;
            }
            StudentInfoDetails studentDetailedRow = studentCoursesRow.getStudentInfoDetails();
            UUID studentId = studentDetailedRow.getStudentId();
            if (!studentDetailsMap.containsKey(studentId)) {
                studentDetailsMap.put(studentId, new StudentCoursesDetailsLite(studentCoursesRow.getAcademicSessionId(),
                        studentDetailedRow, new ArrayList<>()));
            }

            studentDetailsMap.get(studentId).getCourses().add(studentCoursesRow.getCourse());
        }
        List<StudentCoursesDetailsLite> studentCoursesList = new ArrayList<>();
        /**
         * doing this instead of new ArrayList<>(studentDetailsMap.values()); because we are seperating
         * scholastic and coscholastic courses at the time of object creation
         */
        for(Map.Entry<UUID, StudentCoursesDetailsLite> entry : studentDetailsMap.entrySet()) {
            studentCoursesList.add(new StudentCoursesDetailsLite(entry.getValue().getAcademicSessionId(),
                    entry.getValue().getStudentInfoDetails(), entry.getValue().getCourses()));
        }
        return CollectionUtils.isNullOrEmpty(studentCoursesList) ? null : studentCoursesList;
    }
}

