package com.lernen.cloud.dao.tier.library.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;

import com.lernen.cloud.core.api.library.Author;
import com.lernen.cloud.core.api.library.Genre;
import com.lernen.cloud.core.api.library.Publication;
import com.lernen.cloud.core.api.library.Publisher;
import com.lernen.cloud.core.api.library.report.LibraryReportSummaryData;

public class LibrarySummaryReportMapper implements RowMapper<LibraryReportSummaryData> {

    private static final String BOOK_ID = "book_details.book_id";
    private static final String BOOK_TITLE = "book_details.book_title";
    private static final String ISBN_NUMBER = "book_details.isbn_number";
    private static final String EDITION = "book_details.edition";
    private static final String PUBLISH_YEAR = "book_details.publish_year";
    private static final String LANGUAGE = "book_details.language";
    private static final String NO_OF_COPIES = "book_details.no_of_copies";
    private static final String ADDED_AT = "book_details.added_at";
    private static final String LIBRARY_TYPE_NAME = "library_type.library_type_name";

    // Genre fields
    private static final String GENRE_ID = "genre.genre_id";
    private static final String GENRE_NAME = "genre.genre_name";
    private static final String CLASSIFICATION_NUMBER = "genre.classification_number";

    // Author fields
    private static final String AUTHOR_ID = "author.author_id";
    private static final String AUTHOR_NAME = "author.author_name";

    // Publication fields
    private static final String PUBLICATION_ID = "publication.publication_id";
    private static final String PUBLICATION_NAME = "publication.publication_name";

    // Publisher fields
    private static final String PUBLISHER_ID = "publisher.publisher_id";
    private static final String PUBLISHER_NAME = "publisher.publisher_name";

    @Override
    public LibraryReportSummaryData mapRow(ResultSet rs, int rowNum) throws SQLException {
        if (rs.getString(BOOK_ID) == null) {
            return null;
        }

        UUID bookId = UUID.fromString(rs.getString(BOOK_ID));
        String bookTitle = rs.getString(BOOK_TITLE);
        String isbn = rs.getString(ISBN_NUMBER);
        String edition = rs.getString(EDITION);
        int publishYear = rs.getInt(PUBLISH_YEAR);
        String language = rs.getString(LANGUAGE);
        int totalCopies = rs.getInt(NO_OF_COPIES);
        Integer addedAt = rs.getTimestamp(ADDED_AT) != null ? 
            (int) (rs.getTimestamp(ADDED_AT).getTime() / 1000L) : null;
        String libraryTypeName = rs.getString(LIBRARY_TYPE_NAME);
        String genre = rs.getString(GENRE_NAME);
        String author = rs.getString(AUTHOR_NAME);
        String publication = rs.getString(PUBLICATION_NAME);
        String publisher = rs.getString(PUBLISHER_NAME);

        LibraryReportSummaryData libraryReportSummaryData = new LibraryReportSummaryData(bookId, bookTitle, isbn, author, libraryTypeName, genre, 
                                          publication, publisher, edition, publishYear, 
                                          language, totalCopies, addedAt);

        return libraryReportSummaryData;
    }
}
