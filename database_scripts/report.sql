select * from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join standards on fee_assignment.entity_id = standards.standard_id join (select standard_id, count(*) as count from student_academic_session_details where academic_session_id = 2 group by standard_id) as student_counts on standards.standard_id = student_counts.standard_id where fee_assignment.institute_id = 10001;


select fee_assignment.institute_id, fee_assignment.entity_id, fee_assignment.entity_name, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id, fee_assignment.amount , student_counts.count, fee_assignment.amount * student_counts.count as total_amount from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join standards on fee_assignment.entity_id = standards.standard_id join (select standard_id, count(*) as count from student_academic_session_details where academic_session_id = 2 group by standard_id) as student_counts on standards.standard_id = student_counts.standard_id where fee_assignment.institute_id = 10001 ;


select fee_assignment.institute_id, fee_assignment.entity_id, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id, fee_assignment.amount , student_counts.count, fee_assignment.amount * student_counts.count as total_amount from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join standards on fee_assignment.entity_id = standards.standard_id join (select standard_id, count(*) as count from student_academic_session_details where academic_session_id = 2 group by standard_id) as student_counts on standards.standard_id = student_counts.standard_id where fee_assignment.institute_id = 10001 ;



select * from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join student_academic_session_details on student_academic_session_details.student_id = fee_assignment.entity_id and  student_academic_session_details.academic_session_id = 2 join standards on student_academic_session_details.standard_id = standards.standard_id

select fee_assignment.institute_id,standards.standard_id, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id, count(fee_assignment.entity_id) as student_count, sum(fee_assignment.amount) as total_amount from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join student_academic_session_details on student_academic_session_details.student_id = fee_assignment.entity_id and  student_academic_session_details.academic_session_id = 2 join standards on student_academic_session_details.standard_id = standards.standard_id where fee_assignment.institute_id = 10001 group by fee_assignment.institute_id,standards.standard_id, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id;







select fee_assignment.institute_id,  fee_assignment.entity_name, fee_assignment.entity_id, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id , student_counts.count, fee_assignment.amount * student_counts.count as total_amount from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join standards on fee_assignment.entity_id = standards.standard_id join (select standard_id, count(*) as count from student_academic_session_details where academic_session_id = 2 group by standard_id) as student_counts on standards.standard_id = student_counts.standard_id where fee_assignment.institute_id = 10001 
 union 
 select fee_assignment.institute_id,  fee_assignment.entity_name, standards.standard_id, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id, count(fee_assignment.entity_id) as student_count, sum(fee_assignment.amount) as total_amount from fee_assignment join fee_configuration on fee_assignment.fee_id = fee_configuration.fee_id and fee_configuration.academic_session_id = 2 join student_academic_session_details on student_academic_session_details.student_id = fee_assignment.entity_id and  student_academic_session_details.academic_session_id = 2 join standards on student_academic_session_details.standard_id = standards.standard_id where fee_assignment.institute_id = 10001 group by fee_assignment.institute_id, fee_assignment.entity_name, standards.standard_id, standards.standard_name, fee_assignment.fee_id, fee_assignment.fee_head_id;


select institute_id, standards.standard_id, standards.standard_name , student_fee_payments.fee_id, student_fee_payments.fee_head_id, AVG(assigned_amount) 
as assigned_amount, AVG(paid_amount) as paid_amount, AVG(instant_discount_amount) as instant_discount_amount, 
SUM(student_fee_payment_discounts.discount_amount) as assigned_discount_amount from student_fee_payments 
left join student_fee_payment_discounts on student_fee_payments.discount_group_id = 
student_fee_payment_discounts.discount_group_id where student_id = ? and institute_id = ? and fee_id in 
%s and transaction_count > 0 group by institute_id, student_id, fee_id, fee_head_id, student_fee_payments.discount_group_id

select institute_id, standards.standard_id, standards.standard_name , student_fee_payments.fee_id, student_fee_payments.fee_head_id,
SUM(paid_amount) as paid_amount, SUM(instant_discount_amount) + SUM(assigned_discount_amount)  as total_discount from   
(select ) as fee_payments join standards on fee_payments.entity_id = standards.standard_id  group by institute_id, standards.standard_id, standards.standard_name, fee_id, fee_head_id



select * from student_academic_session_details join (select institute_id, student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, AVG(assigned_amount) 
as assigned_amount, AVG(paid_amount) as paid_amount, AVG(instant_discount_amount) as instant_discount_amount, 
SUM(student_fee_payment_discounts.discount_amount) as assigned_discount_amount from student_fee_payments 
left join student_fee_payment_discounts on student_fee_payments.discount_group_id = 
student_fee_payment_discounts.discount_group_id where institute_id = 10001 and fee_id in (select fee_id from fee_configuration where academic_session_id = 2) 
group by institute_id, student_id, fee_id, fee_head_id, student_fee_payments.discount_group_id) as fee_payments on student_academic_session_details.student_id = fee_payments.student_id and student_academic_session_details.academic_session_id = 2 join standards on student_academic_session_details.standard_id = standards.standard_id;


select fee_payments.institute_id, standards.standard_id, standards.standard_name , fee_payments.fee_id, fee_payments.fee_head_id,
SUM(paid_amount) as paid_amount, SUM(instant_discount_amount) , COALESCE(SUM(assigned_discount_amount),0) , SUM(instant_discount_amount) + COALESCE(SUM(assigned_discount_amount),0) as total_discount from student_academic_session_details join (select student_fee_payments.institute_id, student_fee_payments.student_id, student_fee_payments.fee_id, student_fee_payments.fee_head_id, AVG(assigned_amount) 
as assigned_amount, AVG(paid_amount) as paid_amount, AVG(instant_discount_amount) as instant_discount_amount, 
SUM(student_fee_payment_discounts.discount_amount) as assigned_discount_amount from student_fee_payments 
left join student_fee_payment_discounts on student_fee_payments.discount_group_id = 
student_fee_payment_discounts.discount_group_id where institute_id = 10001 and fee_id in (select fee_id from fee_configuration where academic_session_id = 2) 
group by institute_id, student_id, fee_id, fee_head_id, student_fee_payments.discount_group_id) as fee_payments on 
student_academic_session_details.student_id = fee_payments.student_id and student_academic_session_details.academic_session_id = 2 
join standards on student_academic_session_details.standard_id = standards.standard_id 
group by fee_payments.institute_id, standards.standard_id, standards.standard_name , fee_payments.fee_id, fee_payments.fee_head_id;





select * from student_fee_payments join (select discount_group_id, SUM(discount_amount) as discount_amount from student_fee_payment_discounts group student_fee_payment_discounts_sum
