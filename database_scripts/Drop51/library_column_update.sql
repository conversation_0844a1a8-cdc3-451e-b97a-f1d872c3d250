-- 1. Rename column `price` to `purchase_price`
ALTER TABLE individual_book_details
CHANGE COLUMN price purchase_price DECIMAL(10, 2);

-- 2. Add `mrp` column
ALTER TABLE individual_book_details
ADD COLUMN mrp DECIMAL(10, 2) AFTER purchase_price;

-- 3. Add `added_on` column
ALTER TABLE individual_book_details
ADD COLUMN added_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER date_of_purchase;

INSERT INTO counters (institute_id, counter_type, count, counter_prefix)
SELECT i.institute_id, 'ISBN_NUMBER', 0, ''
FROM institute i
WHERE NOT EXISTS (
  SELECT 1 FROM counters c 
  WHERE c.institute_id = i.institute_id AND c.counter_type = 'ISBN_NUMBER'
);

ALTER TABLE vendor MODIFY gst_number VARCHAR(50);
