
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "6th",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "7th",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "8th",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "9th",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "10th",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "11th",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10031",
  "standardName": "12th",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL
