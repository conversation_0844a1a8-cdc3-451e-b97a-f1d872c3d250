
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "L.K.G",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "U.K.G",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "1st",
  "stream" : "NA",
  "level" : 3,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "2nd",
  "stream" : "NA",
  "level" : 4,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "3rd",
  "stream" : "NA",
  "level" : 5,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "4th",
  "stream" : "NA",
  "level" : 6,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10030",
  "standardName": "5th",
  "stream" : "NA",
  "level" : 7,
  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
}' $INSTITUTE_STANDARDS_URL

