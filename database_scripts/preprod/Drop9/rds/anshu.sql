use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10035,'THAKUR K.S.H.D. PUBLIC INTER COLLEGE', 'RAJ NAGAR, UNCHA AMIRPUR, N.T.P.C.', null, '<PERSON><PERSON>, <PERSON><PERSON><PERSON>', 'Uttar Pradesh', 'India', '201008', null, '', '<EMAIL>', '8800223095', 'RAJ NAGAR, UNCHA AMIRPUR, N.T.P.C., DADRI', '');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10035, 2019, 2020, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10035, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10035, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10035, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10035, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10035, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10035, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10035, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10035, (SELECT fee_category_id from fee_category where institute_id = 10035 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10035,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10035,'books',0);
insert into categories (institute_id,category_name) values (10035,'clothing');
insert into categories (institute_id,category_name) values (10035,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10035,'note book',0,0);
insert into categories (institute_id,category_name) values (10035,'art & craft');
insert into categories (institute_id,category_name) values (10035,'personal care');
insert into categories (institute_id,category_name) values (10035,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10035,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10035,'accessories');
insert into categories (institute_id,category_name) values (10035,'furniture');
insert into categories (institute_id,category_name) values (10035,'electronics');
insert into categories (institute_id,category_name) values (10035,'sports');



insert into colors (institute_id, color_name) values (10035,'maroon');
insert into colors (institute_id, color_name) values (10035,'black');
insert into colors (institute_id, color_name) values (10035,'brown');
insert into colors (institute_id, color_name) values (10035,'white');
insert into colors (institute_id, color_name) values (10035,'red');
insert into colors (institute_id, color_name) values (10035,'yellow');
insert into colors (institute_id, color_name) values (10035,'blue');
insert into colors (institute_id, color_name) values (10035,'navy blue');
insert into colors (institute_id, color_name) values (10035,'green');
insert into colors (institute_id, color_name) values (10035,'dark green');
insert into colors (institute_id, color_name) values (10035,'pink');
insert into colors (institute_id, color_name) values (10035,'purple');
insert into colors (institute_id, color_name) values (10035,'grey');
insert into colors (institute_id, color_name) values (10035,'olive');
insert into colors (institute_id, color_name) values (10035,'cyan');
insert into colors (institute_id, color_name) values (10035,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10035, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10035', 'meta_data', 'enable_permission', 'true');

insert into default_fee_assignment_structure_meta_data values(10035, "Monthly Fees", "fe712ded-1963-4b93-8d73-601f56163efc", "ENROLLMENT");
insert into default_fee_assignment_structure select "fe712ded-1963-4b93-8d73-601f56163efc", (select standard_id from standards where institute_id=10035 and standard_name = '2nd'), "CLASS", fee_id , fee_head_id, amount from fee_configuration where institute_id = 10035 and fee_type = "REGULAR";





