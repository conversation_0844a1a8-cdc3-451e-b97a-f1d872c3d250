use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10031,'ADARSH INTER COLLEGE', 'Vill & P.O - <PERSON>ya<PERSON>i <PERSON>', null, '<PERSON><PERSON>, <PERSON><PERSON><PERSON>', 'Uttar Pradesh', 'India', '201008', 'Near Govt. Primary School', '', '<EMAIL>', '9350507045', 'Vill & P.O - Piyawali Tajpur, Dadri', '');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10031, 2019, 2020, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10031, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10031, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10031, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10031, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10031, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10031, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10031, 'Other Fee' ,'Any Other Fees');



INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10031, (SELECT fee_category_id from fee_category where institute_id = 10031 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10031,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10031,'books',0);
insert into categories (institute_id,category_name) values (10031,'clothing');
insert into categories (institute_id,category_name) values (10031,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10031,'note book',0,0);
insert into categories (institute_id,category_name) values (10031,'art & craft');
insert into categories (institute_id,category_name) values (10031,'personal care');
insert into categories (institute_id,category_name) values (10031,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10031,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10031,'accessories');
insert into categories (institute_id,category_name) values (10031,'furniture');
insert into categories (institute_id,category_name) values (10031,'electronics');
insert into categories (institute_id,category_name) values (10031,'sports');



insert into colors (institute_id, color_name) values (10031,'maroon');
insert into colors (institute_id, color_name) values (10031,'black');
insert into colors (institute_id, color_name) values (10031,'brown');
insert into colors (institute_id, color_name) values (10031,'white');
insert into colors (institute_id, color_name) values (10031,'red');
insert into colors (institute_id, color_name) values (10031,'yellow');
insert into colors (institute_id, color_name) values (10031,'blue');
insert into colors (institute_id, color_name) values (10031,'navy blue');
insert into colors (institute_id, color_name) values (10031,'green');
insert into colors (institute_id, color_name) values (10031,'dark green');
insert into colors (institute_id, color_name) values (10031,'pink');
insert into colors (institute_id, color_name) values (10031,'purple');
insert into colors (institute_id, color_name) values (10031,'grey');
insert into colors (institute_id, color_name) values (10031,'olive');
insert into colors (institute_id, color_name) values (10031,'cyan');
insert into colors (institute_id, color_name) values (10031,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10031, 'FEE_INVOICE', 1, "");
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10031', 'meta_data', 'enable_permission', 'true');
