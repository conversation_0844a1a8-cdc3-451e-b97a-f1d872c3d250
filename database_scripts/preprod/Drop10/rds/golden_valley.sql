use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10040,'GOLDEN VALLEY INTERNATIONAL SCHOOL', 'Roza Jalalpur', null, 'Greater Noida West', 'Uttar Pradesh', 'India', '201009', 'Main market', '', '', '9911254445', 'Roza Jalalpur, Greater Noida West - 201009', '(AN ENGLISH MEDIUM SCHOOL RECOGNISED)');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10040, 2019, 2020, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10040, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10040, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10040, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10040, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10040, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10040, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10040, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10040, (SELECT fee_category_id from fee_category where institute_id = 10040 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10040,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10040,'books',0);
insert into categories (institute_id,category_name) values (10040,'clothing');
insert into categories (institute_id,category_name) values (10040,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10040,'note book',0,0);
insert into categories (institute_id,category_name) values (10040,'art & craft');
insert into categories (institute_id,category_name) values (10040,'personal care');
insert into categories (institute_id,category_name) values (10040,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10040,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10040,'accessories');
insert into categories (institute_id,category_name) values (10040,'furniture');
insert into categories (institute_id,category_name) values (10040,'electronics');
insert into categories (institute_id,category_name) values (10040,'sports');



insert into colors (institute_id, color_name) values (10040,'maroon');
insert into colors (institute_id, color_name) values (10040,'black');
insert into colors (institute_id, color_name) values (10040,'brown');
insert into colors (institute_id, color_name) values (10040,'white');
insert into colors (institute_id, color_name) values (10040,'red');
insert into colors (institute_id, color_name) values (10040,'yellow');
insert into colors (institute_id, color_name) values (10040,'blue');
insert into colors (institute_id, color_name) values (10040,'navy blue');
insert into colors (institute_id, color_name) values (10040,'green');
insert into colors (institute_id, color_name) values (10040,'dark green');
insert into colors (institute_id, color_name) values (10040,'pink');
insert into colors (institute_id, color_name) values (10040,'purple');
insert into colors (institute_id, color_name) values (10040,'grey');
insert into colors (institute_id, color_name) values (10040,'olive');
insert into colors (institute_id, color_name) values (10040,'cyan');
insert into colors (institute_id, color_name) values (10040,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10040, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10040', 'meta_data', 'enable_permission', 'true');

-- insert into default_fee_assignment_structure_meta_data values(10040, "Monthly Fees", "fe712ded-1963-4b93-8d73-601f56163efc", "ENROLLMENT");
-- insert into default_fee_assignment_structure select "fe712ded-1963-4b93-8d73-601f56163efc", (select standard_id from standards where institute_id=10040 and standard_name = '2nd'), "CLASS", fee_id , fee_head_id, amount from fee_configuration where institute_id = 10040 and fee_type = "REGULAR";





