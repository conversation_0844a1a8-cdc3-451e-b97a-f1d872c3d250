use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code, organisation_id) 
VALUES (10051,'SWAMI VIVEKANAND SHIKSHAN SANSTHAN SEC. SCHOOL(E)', 'Gogasar', null, 'Churu', 'Rajasthan', 'India', '331504', '', '', '', '9911254445', 'Gogasar, Churu - 331504', '(AN ENGLISH MEDIUM SCHOOL RECOGNISED)', "9a6a2052-9070-44ac-8840-2bdbf2171c99", "e87c4ba5-fc14-4285-9d04-3477389b6361");


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10051, 2019, 2020, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10051, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10051, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10051, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10051, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10051, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10051, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10051, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10051, (SELECT fee_category_id from fee_category where institute_id = 10051 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10051,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10051,'books',0);
insert into categories (institute_id,category_name) values (10051,'clothing');
insert into categories (institute_id,category_name) values (10051,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10051,'note book',0,0);
insert into categories (institute_id,category_name) values (10051,'art & craft');
insert into categories (institute_id,category_name) values (10051,'personal care');
insert into categories (institute_id,category_name) values (10051,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10051,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10051,'accessories');
insert into categories (institute_id,category_name) values (10051,'furniture');
insert into categories (institute_id,category_name) values (10051,'electronics');
insert into categories (institute_id,category_name) values (10051,'sports');



insert into colors (institute_id, color_name) values (10051,'maroon');
insert into colors (institute_id, color_name) values (10051,'black');
insert into colors (institute_id, color_name) values (10051,'brown');
insert into colors (institute_id, color_name) values (10051,'white');
insert into colors (institute_id, color_name) values (10051,'red');
insert into colors (institute_id, color_name) values (10051,'yellow');
insert into colors (institute_id, color_name) values (10051,'blue');
insert into colors (institute_id, color_name) values (10051,'navy blue');
insert into colors (institute_id, color_name) values (10051,'green');
insert into colors (institute_id, color_name) values (10051,'dark green');
insert into colors (institute_id, color_name) values (10051,'pink');
insert into colors (institute_id, color_name) values (10051,'purple');
insert into colors (institute_id, color_name) values (10051,'grey');
insert into colors (institute_id, color_name) values (10051,'olive');
insert into colors (institute_id, color_name) values (10051,'cyan');
insert into colors (institute_id, color_name) values (10051,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10051, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10051', 'meta_data', 'enable_permission', 'true');

-- insert into default_fee_assignment_structure_meta_data values(10051, "Monthly Fees", "fe712ded-1963-4b93-8d73-601f56163efc", "ENROLLMENT");
-- insert into default_fee_assignment_structure select "fe712ded-1963-4b93-8d73-601f56163efc", (select standard_id from standards where institute_id=10051 and standard_name = '2nd'), "CLASS", fee_id , fee_head_id, amount from fee_configuration where institute_id = 10051 and fee_type = "REGULAR";


insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10051, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10051, "Grade", "SYSTEM", "GRADE", 1);



