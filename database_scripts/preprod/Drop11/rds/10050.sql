use lernen

INSERT INTO organisation (organisation_id, name)
VALUES ("e87c4ba5-fc14-4285-9d04-3477389b6361", "SWAMI VIVEKANAND SHIKSHAN SANSTHAN SEC. SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code, organisation_id) 
VALUES (10050,'SWAMI VIVEKANAND SHIKSHAN SANSTHAN SEC. SCHOOL(H)', 'Gogasar', null, 'Churu', 'Rajasthan', 'India', '331504', '', '', '', '9911254445', 'Gogasar, Churu - 331504', '(A HINDI MEDIUM SCHOOL RECOGNISED)', "603e1e5a-27f2-4374-991e-601d20a6bb1d", "e87c4ba5-fc14-4285-9d04-3477389b6361");


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10050, 2019, 2020, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10050, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10050, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10050, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10050, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10050, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10050, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10050, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10050, (SELECT fee_category_id from fee_category where institute_id = 10050 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10050,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10050,'books',0);
insert into categories (institute_id,category_name) values (10050,'clothing');
insert into categories (institute_id,category_name) values (10050,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10050,'note book',0,0);
insert into categories (institute_id,category_name) values (10050,'art & craft');
insert into categories (institute_id,category_name) values (10050,'personal care');
insert into categories (institute_id,category_name) values (10050,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10050,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10050,'accessories');
insert into categories (institute_id,category_name) values (10050,'furniture');
insert into categories (institute_id,category_name) values (10050,'electronics');
insert into categories (institute_id,category_name) values (10050,'sports');



insert into colors (institute_id, color_name) values (10050,'maroon');
insert into colors (institute_id, color_name) values (10050,'black');
insert into colors (institute_id, color_name) values (10050,'brown');
insert into colors (institute_id, color_name) values (10050,'white');
insert into colors (institute_id, color_name) values (10050,'red');
insert into colors (institute_id, color_name) values (10050,'yellow');
insert into colors (institute_id, color_name) values (10050,'blue');
insert into colors (institute_id, color_name) values (10050,'navy blue');
insert into colors (institute_id, color_name) values (10050,'green');
insert into colors (institute_id, color_name) values (10050,'dark green');
insert into colors (institute_id, color_name) values (10050,'pink');
insert into colors (institute_id, color_name) values (10050,'purple');
insert into colors (institute_id, color_name) values (10050,'grey');
insert into colors (institute_id, color_name) values (10050,'olive');
insert into colors (institute_id, color_name) values (10050,'cyan');
insert into colors (institute_id, color_name) values (10050,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10050, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10050', 'meta_data', 'enable_permission', 'true');

-- insert into default_fee_assignment_structure_meta_data values(10050, "Monthly Fees", "fe712ded-1963-4b93-8d73-601f56163efc", "ENROLLMENT");
-- insert into default_fee_assignment_structure select "fe712ded-1963-4b93-8d73-601f56163efc", (select standard_id from standards where institute_id=10050 and standard_name = '2nd'), "CLASS", fee_id , fee_head_id, amount from fee_configuration where institute_id = 10050 and fee_type = "REGULAR";





