use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10045,'SREE JAIN SWETAMBER TERAPANTHI VIDYALAYA', '3, PORTUGUESE CHURCH STREET', null, 'KOLKATA', 'WEST BENGAL', 'India', '700001', 'BESIDE PORTUGUESE CHURCH / VERY NEAR OF BRABOURNE ROAD', '', '<EMAIL>', '033 - 2235 - 9336 (OFFICE) / 8100080878 (H.O.I)', '3, PORTUGUESE CHURCH STREET KOLKATA - 700001', '(AN ENGLISH MEDIUM SCHOOL RECOGNISED)');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10045, 2020, 2020, 1, 12);

insert into fee_category (institute_id, fee_category, description) values (10045, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10045, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10045, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10045, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10045, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10045, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10045, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10045, (SELECT fee_category_id from fee_category where institute_id = 10045 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10045,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10045,'books',0);
insert into categories (institute_id,category_name) values (10045,'clothing');
insert into categories (institute_id,category_name) values (10045,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10045,'note book',0,0);
insert into categories (institute_id,category_name) values (10045,'art & craft');
insert into categories (institute_id,category_name) values (10045,'personal care');
insert into categories (institute_id,category_name) values (10045,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10045,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10045,'accessories');
insert into categories (institute_id,category_name) values (10045,'furniture');
insert into categories (institute_id,category_name) values (10045,'electronics');
insert into categories (institute_id,category_name) values (10045,'sports');



insert into colors (institute_id, color_name) values (10045,'maroon');
insert into colors (institute_id, color_name) values (10045,'black');
insert into colors (institute_id, color_name) values (10045,'brown');
insert into colors (institute_id, color_name) values (10045,'white');
insert into colors (institute_id, color_name) values (10045,'red');
insert into colors (institute_id, color_name) values (10045,'yellow');
insert into colors (institute_id, color_name) values (10045,'blue');
insert into colors (institute_id, color_name) values (10045,'navy blue');
insert into colors (institute_id, color_name) values (10045,'green');
insert into colors (institute_id, color_name) values (10045,'dark green');
insert into colors (institute_id, color_name) values (10045,'pink');
insert into colors (institute_id, color_name) values (10045,'purple');
insert into colors (institute_id, color_name) values (10045,'grey');
insert into colors (institute_id, color_name) values (10045,'olive');
insert into colors (institute_id, color_name) values (10045,'cyan');
insert into colors (institute_id, color_name) values (10045,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10045, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10045', 'meta_data', 'enable_permission', 'true');

-- insert into default_fee_assignment_structure_meta_data values(10045, "Monthly Fees", "fe712ded-1963-4b93-8d73-601f56163efc", "ENROLLMENT");
-- insert into default_fee_assignment_structure select "fe712ded-1963-4b93-8d73-601f56163efc", (select standard_id from standards where institute_id=10045 and standard_name = '2nd'), "CLASS", fee_id , fee_head_id, amount from fee_configuration where institute_id = 10045 and fee_type = "REGULAR";





