use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (703,'LERNEN SCHOOL', 'B-5, Sector 18 , Noida', null, 'Noida', 'Uttar Pradesh', 'India', '201301', null, '/static/core/images/lernen_white_logo.png', '<EMAIL>', '01565-223991', '(Co-Educational English Medium School)', 'Sector 18 , Noida');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(703, 2018, 2019, 5, 4);
INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(703, 2019, 2020, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (703, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (703, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (703, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (703, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (703, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (703, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (703, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(703, (SELECT fee_category_id from fee_category where institute_id = 703 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (703,'stationery',0);
insert into categories (institute_id,category_name,genders) values (703,'books',0);
insert into categories (institute_id,category_name) values (703,'clothing');
insert into categories (institute_id,category_name) values (703,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (703,'note book',0,0);
insert into categories (institute_id,category_name) values (703,'art & craft');
insert into categories (institute_id,category_name) values (703,'personal care');
insert into categories (institute_id,category_name) values (703,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (703,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (703,'accessories');



insert into colors (institute_id, color_name) values (703,'maroon');
insert into colors (institute_id, color_name) values (703,'black');
insert into colors (institute_id, color_name) values (703,'brown');
insert into colors (institute_id, color_name) values (703,'white');
insert into colors (institute_id, color_name) values (703,'red');
insert into colors (institute_id, color_name) values (703,'yellow');
insert into colors (institute_id, color_name) values (703,'blue');
insert into colors (institute_id, color_name) values (703,'navy blue');
insert into colors (institute_id, color_name) values (703,'green');
insert into colors (institute_id, color_name) values (703,'dark green');
insert into colors (institute_id, color_name) values (703,'pink');
insert into colors (institute_id, color_name) values (703,'purple');
insert into colors (institute_id, color_name) values (703,'grey');
insert into colors (institute_id, color_name) values (703,'olive');
insert into colors (institute_id, color_name) values (703,'cyan');
insert into colors (institute_id, color_name) values (703,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (703, 'FEE_INVOICE', 1, "");
// Created after fees were created. This is for admission fees. Date : 30-03-2019
//insert into default_fee_assignment_structure values(703, "703","INSTITUTE","e4f0ae08-348b-47eb-b760-7bb670c3ed97",7,1000);


insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(703, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(703, "Grade", "SYSTEM", "GRADE", 1);


insert into configuration(entity,entity_id,config_type,config_key,config_value) values('INSTITUTE','703','fees_fine_configurations','fine_calculator_type','FIXED');
insert into configuration(entity,entity_id,config_type,config_key,config_value) values('INSTITUTE','703','fees_fine_configurations','fixed_fine_amount',20);
insert into configuration(entity,entity_id,config_type,config_key,config_value) values('INSTITUTE','703','fees_fine_configurations','fine_exempt_days',0);

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '703', 'meta_data', 'fees_sms_service_enabled', 'true');
