insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10015, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10015, "Grade", "SYSTEM", "GRADE", 1);

-- to create default exams
POST : http://localhost:8080/data-server/2.0/examination/create-default-exam?institute_id=10015&academic_session_id=7
	
	
	
	
drop table report_card_variables;
drop table exam_report_structure;
drop table marks_feeding;
drop table exam_courses_assignment;
drop table exam_graph_edges;
drop table exam_dimensions_mapping;
drop table exam_metadata;
drop table exam_dimensions;

drop table student_course_assignment;
drop table class_courses;
