use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10020,'MAHARANA PRATAP PUBLIC SENIOR SECONDARY SCHOOL', '<PERSON><PERSON> Bass , Shri Dungargarh', null, 'Shri Dungargarh', 'Rajasthan', 'India', '331803', null, '/static/core/images/balaji_convent_logo.png', '<EMAIL>', '01565-223991', '(Co-Educational English Medium School)', 'Kalu Bass , Shri Dungargarh');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10020, 2019, 2020, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (10020, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10020, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10020, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10020, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10020, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10020, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10020, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10020, (SELECT fee_category_id from fee_category where institute_id = 10020 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10020,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10020,'books',0);
insert into categories (institute_id,category_name) values (10020,'clothing');
insert into categories (institute_id,category_name) values (10020,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10020,'note book',0,0);
insert into categories (institute_id,category_name) values (10020,'art & craft');
insert into categories (institute_id,category_name) values (10020,'personal care');
insert into categories (institute_id,category_name) values (10020,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10020,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10020,'accessories');



insert into colors (institute_id, color_name) values (10020,'maroon');
insert into colors (institute_id, color_name) values (10020,'black');
insert into colors (institute_id, color_name) values (10020,'brown');
insert into colors (institute_id, color_name) values (10020,'white');
insert into colors (institute_id, color_name) values (10020,'red');
insert into colors (institute_id, color_name) values (10020,'yellow');
insert into colors (institute_id, color_name) values (10020,'blue');
insert into colors (institute_id, color_name) values (10020,'navy blue');
insert into colors (institute_id, color_name) values (10020,'green');
insert into colors (institute_id, color_name) values (10020,'dark green');
insert into colors (institute_id, color_name) values (10020,'pink');
insert into colors (institute_id, color_name) values (10020,'purple');
insert into colors (institute_id, color_name) values (10020,'grey');
insert into colors (institute_id, color_name) values (10020,'olive');
insert into colors (institute_id, color_name) values (10020,'cyan');
insert into colors (institute_id, color_name) values (10020,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10020, 'FEE_INVOICE', 1, "");


insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10020, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10020, "Grade", "SYSTEM", "GRADE", 1);

//Created after fees were created. This is for admission fees. Date : 30-03-2019
insert into default_fee_assignment_structure values(10020, "10020","INSTITUTE","fb695613-9c19-4b15-97ae-b1c98e6e6894",66,200);
insert into default_fee_assignment_structure values(10020, "10020","INSTITUTE","70f85683-90e1-4212-ba8b-88fcd3be7ba2",67,200);
insert into default_fee_assignment_structure values(10020, "10020","INSTITUTE","0d056cb2-3cb2-4e46-8cd5-620a0779dd24",68,300);
insert into default_fee_assignment_structure values(10020, "10020","INSTITUTE","e56976de-139c-40de-874d-aa8a2014cdfe",69,150);
insert into default_fee_assignment_structure values(10020, "10020","INSTITUTE","d0a7ed12-4833-4fed-9dc8-eb6d88290610",70,200);
