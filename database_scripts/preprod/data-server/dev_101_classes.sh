INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "1st",
#  "stream" : "NA",
#  "level" : 1,
#  "standardSectionList" : []
#}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "101",
  "academicSessionId" : 40,
  "standardName": "2nd",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "3rd",
#  "stream" : "NA",
#  "level" : 3,
#  "standardSectionList" : []
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "4th",
#  "stream" : "NA",
#  "level" : 4,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "5th",
#  "stream" : "NA",
#  "level" : 5,
#  "standardSectionList" : []
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "6th",
#  "stream" : "NA",
#  "level" : 6,
#  "standardSectionList" : []
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "7th",
#  "stream" : "NA",
#  "level" : 7,
#  "standardSectionList" : []
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "8th",
#  "stream" : "NA",
#  "level" : 8,
#  "standardSectionList" : []
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "9th",
#  "stream" : "NA",
#  "level" : 9,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}, {"sectionName" : "C"}]
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "10th",
#  "stream" : "NA",
#  "level" : 10,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}, {"sectionName" : "C"}, {"sectionName" : "D"}]
#}' $INSTITUTE_STANDARDS_URL
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "11th",
#  "stream" : "SCIENCE",
#  "level" : 11,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
#}' $INSTITUTE_STANDARDS_URL
#
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "11th",
#  "stream" : "COMMERCE",
#  "level" : 12,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"}]
#}' $INSTITUTE_STANDARDS_URL
#
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "12th",
#  "stream" : "SCIENCE",
#  "level" : 13,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
#}' $INSTITUTE_STANDARDS_URL
#
#
#curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
#  "instituteId": "101",
#  "academicSessionId" : 40,
#  "standardName": "12th",
#  "stream" : "COMMERCE",
#  "level" : 14,
#  "standardSectionList" : [{"sectionName" : "A"},{"sectionName" : "B"},{"sectionName" : "C"}]
#}' $INSTITUTE_STANDARDS_URL
