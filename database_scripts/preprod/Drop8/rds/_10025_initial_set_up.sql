use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10025,'<PERSON><PERSON><PERSON><PERSON>i Secondry School', 'Shastri Nagar Ward No.27', null, 'Ratangarh', 'Rajasthan', 'India', '331022', null, '/static/core/images/lernen_white_logo.png', '<EMAIL>', '01565-222121', '(Co-Educational Hindi Medium School)', 'Shastri Nagar Ward No.27, Ratangarh');


INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10025, 2019, 2020, 7, 6);

insert into fee_category (institute_id, fee_category, description) values (10025, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10025, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10025, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10025, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10025, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10025, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10025, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10025, (SELECT fee_category_id from fee_category where institute_id = 10025 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10025,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10025,'books',0);
insert into categories (institute_id,category_name) values (10025,'clothing');
insert into categories (institute_id,category_name) values (10025,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10025,'note book',0,0);
insert into categories (institute_id,category_name) values (10025,'art & craft');
insert into categories (institute_id,category_name) values (10025,'personal care');
insert into categories (institute_id,category_name) values (10025,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10025,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10025,'accessories');



insert into colors (institute_id, color_name) values (10025,'maroon');
insert into colors (institute_id, color_name) values (10025,'black');
insert into colors (institute_id, color_name) values (10025,'brown');
insert into colors (institute_id, color_name) values (10025,'white');
insert into colors (institute_id, color_name) values (10025,'red');
insert into colors (institute_id, color_name) values (10025,'yellow');
insert into colors (institute_id, color_name) values (10025,'blue');
insert into colors (institute_id, color_name) values (10025,'navy blue');
insert into colors (institute_id, color_name) values (10025,'green');
insert into colors (institute_id, color_name) values (10025,'dark green');
insert into colors (institute_id, color_name) values (10025,'pink');
insert into colors (institute_id, color_name) values (10025,'purple');
insert into colors (institute_id, color_name) values (10025,'grey');
insert into colors (institute_id, color_name) values (10025,'olive');
insert into colors (institute_id, color_name) values (10025,'cyan');
insert into colors (institute_id, color_name) values (10025,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10025, 'FEE_INVOICE', 1, "");
// Created after fees were created. This is for admission fees. Date : 30-03-2019
//insert into default_fee_assignment_structure values(10025, "10025","INSTITUTE","e4f0ae08-348b-47eb-b760-7bb670c3ed97",7,1000);


insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10025, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10025, "Grade", "SYSTEM", "GRADE", 1);


insert into configuration(entity,entity_id,config_type,config_key,config_value) values('INSTITUTE','10025','fees_fine_configurations','fine_calculator_type','FIXED');
insert into configuration(entity,entity_id,config_type,config_key,config_value) values('INSTITUTE','10025','fees_fine_configurations','fixed_fine_amount',0);
insert into configuration(entity,entity_id,config_type,config_key,config_value) values('INSTITUTE','10025','fees_fine_configurations','fine_exempt_days',0);

insert into counters (institute_id, counter_type, count, counter_prefix) values (10025, 'REGISTRATION_NUMBER', 1, "REG-");
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10025', 'meta_data', 'registration_counter', 'true');
	
