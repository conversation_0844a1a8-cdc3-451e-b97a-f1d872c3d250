
INSTITUTE_STANDARDS_URL=$1/2.0/institute/standard

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Nursery",
  "stream" : "NA",
  "level" : 1,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "KG",
  "stream" : "NA",
  "level" : 2,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "First",
  "stream" : "NA",
  "level" : 11,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Second",
  "stream" : "NA",
  "level" : 12,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Third",
  "stream" : "NA",
  "level" : 13,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Fourth",
  "stream" : "NA",
  "level" : 14,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Fifth",
  "stream" : "NA",
  "level" : 15,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Sixth",
  "stream" : "NA",
  "level" : 16,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Seventh",
  "stream" : "NA",
  "level" : 17,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Eigth",
  "stream" : "NA",
  "level" : 18,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Ninth",
  "stream" : "NA",
  "level" : 19,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "instituteId": "10025",
  "standardName": "Tenth",
  "stream" : "NA",
  "level" : 20,
  "standardSectionList" : []
}' $INSTITUTE_STANDARDS_URL
