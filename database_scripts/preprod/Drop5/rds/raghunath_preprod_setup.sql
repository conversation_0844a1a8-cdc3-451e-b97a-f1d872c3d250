INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number) 
VALUES (10015,'SHRI RAGHUNATH SEN SEC SCHOOL', 'Station Road, Opp. Govt Hospital', 'Ratangarh', 'Churu', 'Rajasthan', 'India', '331803', null, '/static/core/images/raghunath_logo.png', '<EMAIL>', '1234567890');

update institute set letter_head_line1 = "(Co-Educational English-Hindi Medium School)" , letter_head_line2 = "Ratangarh-Churu-(Raj)" where institute_id = 10015;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10015, 2018, 2019, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (10015, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10015, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10015, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10015, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10015, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10015, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10015, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10015, (SELECT fee_category_id from fee_category where institute_id = 10015 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');

insert into counters (institute_id, counter_type, count, counter_prefix) values (10015, 'FEE_INVOICE', 1, "");

insert into categories (institute_id,category_name,genders) values (10015,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10015,'books',0);
insert into categories (institute_id,category_name) values (10015,'clothing');
insert into categories (institute_id,category_name) values (10015,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10015,'note book',0,0);
insert into categories (institute_id,category_name) values (10015,'art & craft');
insert into categories (institute_id,category_name) values (10015,'personal care');
insert into categories (institute_id,category_name) values (10015,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10015,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10015,'accessories');



insert into colors (institute_id, color_name) values (10015,'maroon');
insert into colors (institute_id, color_name) values (10015,'black');
insert into colors (institute_id, color_name) values (10015,'brown');
insert into colors (institute_id, color_name) values (10015,'white');
insert into colors (institute_id, color_name) values (10015,'red');
insert into colors (institute_id, color_name) values (10015,'yellow');
insert into colors (institute_id, color_name) values (10015,'blue');
insert into colors (institute_id, color_name) values (10015,'navy blue');
insert into colors (institute_id, color_name) values (10015,'green');
insert into colors (institute_id, color_name) values (10015,'dark green');
insert into colors (institute_id, color_name) values (10015,'pink');
insert into colors (institute_id, color_name) values (10015,'purple');
insert into colors (institute_id, color_name) values (10015,'grey');
insert into colors (institute_id, color_name) values (10015,'olive');
insert into colors (institute_id, color_name) values (10015,'cyan');
insert into colors (institute_id, color_name) values (10015,'magenta');







-- Testing counter config --

insert into counters (institute_id, counter_type, count, counter_prefix) values (10015, 'REGISTRATION_NUMBER', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10015, 'ADMISSION_NUMBER', 1, "");


insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10015', 'meta_data', 'registration_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10015', 'meta_data', 'admission_counter', 'true');


