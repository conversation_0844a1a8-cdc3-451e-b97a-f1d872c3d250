SET @institute = 700;

delete from transport_history_fee_id_mapping where transport_history_id IN (select transport_history_id from transport_history where institute_id = @institute);
delete from transport_history where institute_id = @institute;
delete from transport_service_route_stoppages where service_route_id IN (select service_route_id from transport_service_route where institute_id = @institute);
delete from transport_service_route where institute_id = @institute;
delete from transport_vehicle where institute_id = @institute;
delete from transport_area where institute_id = @institute;

delete from fee_payment_transaction_amounts where transaction_id IN (select transaction_id from fee_payment_transactions where institute_id = @institute);
delete from fee_payment_transactions where institute_id = @institute;
delete from student_fee_payment_discounts where discount_group_id IN (select discount_group_id from student_fee_payments where institute_id = @institute);
delete from student_fee_payments where institute_id = @institute;
delete from student_discount_configuration where institute_id = @institute;
delete from fee_assignment where institute_id = @institute;
delete from discount_fee_head_mapping where discount_id IN (select discount_id from fee_discount_configuration where institute_id = @institute);
delete from fee_discount_configuration where institute_id = @institute;
delete from fee_assignment_permissions where institute_id = @institute;
delete from fee_configuration where institute_id = @institute;


delete from student_wallet where institute_id = @institute;

delete from student_academic_session_details where student_id IN (select student_id from students where institute_id = @institute);
delete from students where institute_id = @institute;

delete from academic_session where institute_id = @institute;

delete from standard_section_mapping where standard_id IN (select standard_id from standards where institute_id = @institute);
delete from standards where institute_id = @institute;

delete from configuration;
delete from counters where institute_id = @institute;

delete from transactions where institute_id = @institute;
delete from product_group_sku_mapping where group_id IN (select group_id from product_group where institute_id = @institute);
delete from product_group where institute_id = @institute;
delete from products where institute_id = @institute;
delete from brand_category_mapping where brand_id IN (select brand_id from brands where institute_id = @institute);
delete from brands where institute_id = @institute;
delete from categories where institute_id = @institute;
delete from colors where institute_id = @institute;
delete from vendors where institute_id = @institute;

delete from users where institute_id = @institute;
delete from institute where institute_id = @institute;