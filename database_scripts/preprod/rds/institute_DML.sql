-- Academic session
insert into academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10001, 2018, 2019, 4, 3);

insert into academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10001, 2019, 2020, 4, 3);

insert into academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10005, 2018, 2019, 5, 4);

use lernen;
INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number) 
VALUES (700,'Lernen', '-', 'Noida', '-', 'Uttar Pradesh', 'India', '201301',null, null, '<EMAIL>', '9982370071');

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number) 
VALUES (701,'<PERSON>rnen-<PERSON>', '-', 'Noida', '-', 'Uttar Pradesh', 'India', '201301',null, null, '<EMAIL>', '9982370071');

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number) 
VALUES (10005,'Vidyasthali Shikshan Sansthan', 'Ward No. 18, Bigga Bass', 'Sri Dungargarh', 'Bikaner', 'Rajasthan', 'India', '331803', null, null, '<EMAIL>', '76573521753');