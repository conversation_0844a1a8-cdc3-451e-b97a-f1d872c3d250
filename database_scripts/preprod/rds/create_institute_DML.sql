
DECLARE @institute INT = 700;

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number) 
VALUES (@institute,'LERNEN', 'Station Road, Opp. Govt Hospital', 'Ratangarh', 'Churu', 'Rajasthan', 'India', '331803', null, '/static/core/images/raghunath_logo.png', '<EMAIL>', '1234567890');

update institute set letter_head_line1 = "(Co-Educational English-Hindi Medium School)" , letter_head_line2 = "Ratangarh-Churu-(Raj)" where institute_id = @institute;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(@institute, 2018, 2019, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (@institute, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (@institute, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (@institute, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (@institute, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (@institute, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (@institute, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (@institute, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(@institute, (SELECT fee_category_id from fee_category where institute_id = @institute and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');

insert into counters (institute_id, counter_type, count, counter_prefix) values (@institute, 'FEE_INVOICE', 1, "");

insert into categories (institute_id,category_name,genders) values (@institute,'stationery',0);
insert into categories (institute_id,category_name,genders) values (@institute,'books',0);
insert into categories (institute_id,category_name) values (@institute,'clothing');
insert into categories (institute_id,category_name) values (@institute,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (@institute,'note book',0,0);
insert into categories (institute_id,category_name) values (@institute,'art & craft');
insert into categories (institute_id,category_name) values (@institute,'personal care');
insert into categories (institute_id,category_name) values (@institute,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (@institute,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (@institute,'accessories');



insert into colors (institute_id, color_name) values (@institute,'maroon');
insert into colors (institute_id, color_name) values (@institute,'black');
insert into colors (institute_id, color_name) values (@institute,'brown');
insert into colors (institute_id, color_name) values (@institute,'white');
insert into colors (institute_id, color_name) values (@institute,'red');
insert into colors (institute_id, color_name) values (@institute,'yellow');
insert into colors (institute_id, color_name) values (@institute,'blue');
insert into colors (institute_id, color_name) values (@institute,'navy blue');
insert into colors (institute_id, color_name) values (@institute,'green');
insert into colors (institute_id, color_name) values (@institute,'dark green');
insert into colors (institute_id, color_name) values (@institute,'pink');
insert into colors (institute_id, color_name) values (@institute,'purple');
insert into colors (institute_id, color_name) values (@institute,'grey');
insert into colors (institute_id, color_name) values (@institute,'olive');
insert into colors (institute_id, color_name) values (@institute,'cyan');
insert into colors (institute_id, color_name) values (@institute,'magenta');







-- Testing counter config --

insert into counters (institute_id, counter_type, count, counter_prefix) values (@institute, 'REGISTRATION_NUMBER', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (@institute, 'ADMISSION_NUMBER', 1, "");
