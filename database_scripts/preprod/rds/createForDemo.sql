CREATE TABLE IF NOT EXISTS students(
	institute_id int NOT NULL,
	registration_number varchar(225) NOT NULL,
	student_id varchar(36) PRIMARY KEY NOT NULL,
	admission_date TIMESTAMP NULL DEFAULT NULL,
	name varchar(225)NOT NULL,
	date_of_birth TIMESTAMP NULL DEFAULT NULL,
	birth_place varchar(225),
	gender varchar(15),
	caste varchar(256),
	rte tinyint(1) default 0,
	aadhar_number varchar(225),
	permanent_address text,
	city varchar(256),
	state varchar(256),
	zipcode varchar (15),
	father_name varchar(225),
	mother_name varchar(225),
	father_occupation varchar(225),
	mother_occupation varchar(225),
	father_contact_number varchar(225),
	mother_contact_number varchar(225),
	family_approx_income varchar(225),
	gardians_details text,
	previous_school_name varchar(225),
	class_passed varchar(225),
	previous_school_medium varchar(225),
	result varchar(225),
	percentage varchar(225),
	year_of_passing int,
	blood_group varchar(225),
	blood_pressure varchar(225),
	pulse varchar(225),
	height varchar(50),
	weight varchar(50),
	date_of_physical_examination TIMESTAMP NULL DEFAULT NULL,
	admission_academic_session int,
	UNIQUE KEY (institute_id, registration_number),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
	FOREIGN KEY (admission_academic_session) REFERENCES academic_session(academic_session_id)
);


CREATE TABLE IF NOT EXISTS student_academic_session_details(
	academic_session_id int NOT NULL,
	student_id varchar(36) NOT NULL,
	standard_id varchar(36) NOT NULL,
	section_id int,
	roll_number varchar(225),
	primary key(academic_session_id, student_id),
	FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);







CREATE TABLE IF NOT EXISTS fee_configuration(
	institute_id int NOT NULL ,
	fee_id varchar(36) NOT NULL ,
	fee_name varchar(1024) ,
	fee_type varchar(225) ,
	due_date TIMESTAMP NULL DEFAULT NULL ,
	academic_session_id int NOT NULL,
	start_month int(2),
	start_year int,
	end_month int(2),
	end_year int,
	description text,
	primary key(fee_id),
	UNIQUE KEY (institute_id, academic_session_id, fee_name),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS fee_category(
	fee_category_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	institute_id int,
	fee_category varchar(1024) NOT NULL,
	description text,
	UNIQUE KEY (institute_id, fee_category)
);


CREATE TABLE IF NOT EXISTS fee_head_configuration(
	institute_id int NOT NULL,
	fee_head_id int PRIMARY KEY AUTO_INCREMENT,
	fee_category_id int NOT NULL,
	fee_head varchar(1024) NOT NULL,
	refundable TinyInt(1),
	description text,
	FOREIGN KEY (fee_category_id) REFERENCES fee_category(fee_category_id),
	UNIQUE KEY (institute_id, fee_head)
);


CREATE TABLE IF NOT EXISTS fee_discount_configuration(
	institute_id int NOT NULL,
	discount_id varchar(36) NOT NULL,
	discount_name varchar(1024) NOT NULL,
	variable TinyInt(1),
	description text,
	primary key(discount_id),
	UNIQUE KEY (institute_id, discount_name)
);

CREATE TABLE IF NOT EXISTS discount_fee_head_mapping(
	discount_id varchar(36) NOT NULL,
	fee_head_id int NOT NULL,
	is_percent tinyint(1) NOT NULL,
	amount double,
	primary key(discount_id, fee_head_id),
	FOREIGN KEY (discount_id) REFERENCES fee_discount_configuration(discount_id),
	FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
);


CREATE TABLE IF NOT EXISTS fee_assignment(
	institute_id int NOT NULL,
	entity_id varchar(256) NOT NULL,
	entity_name varchar(36) NOT NULL,
	fee_id varchar(36) NOT NULL,
	fee_head_id int NOT NULL,
	amount double,
	primary key(institute_id, entity_id, fee_id, fee_head_id),
	FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
	FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
);



CREATE TABLE IF NOT EXISTS student_discount_configuration(
	institute_id int NOT NULL,
	student_id varchar(36) NOT NULL,
	academic_session_id int NOT NULL,
	discount_id varchar(36) NOT NULL,
	primary key(student_id, academic_session_id, discount_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
	FOREIGN KEY (discount_id) REFERENCES fee_discount_configuration(discount_id)
);



CREATE TABLE IF NOT EXISTS student_fee_payments(
	institute_id int NOT NULL ,
	student_id varchar(36) NOT NULL,
	fee_id varchar(36) NOT NULL,
	fee_head_id int NOT NULL,
	assigned_amount double NOT NULL default 0,
	paid_amount double NOT NULL default 0,
	instant_discount_amount double NOT NULL default 0,
	transaction_count int NOT NULL default 0,
	discount_group_id varchar(36),
	primary key(student_id, fee_id, fee_head_id),
	unique key(discount_group_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
	FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
);


CREATE TABLE IF NOT EXISTS student_fee_payment_discounts(
	discount_group_id varchar(36) NOT NULL,
	discount_id varchar(36) NOT NULL,
	discount_amount double NOT NULL,
	primary key(discount_group_id, discount_id),
	FOREIGN KEY (discount_group_id) REFERENCES student_fee_payments(discount_group_id),
	FOREIGN KEY (discount_id) REFERENCES fee_discount_configuration(discount_id)
);



CREATE TABLE IF NOT EXISTS fee_payment_transactions(
	institute_id int NOT NULL ,
	transaction_id varchar(36) NOT NULL,
	student_id varchar(36) NOT NULL,
	transaction_mode varchar(36) NOT NULL,
	transaction_reference varchar(256),
	transaction_date timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
	transaction_added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
	status varchar(36) NOT NULL,
	description text,
	primary key(transaction_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id)
);

CREATE TABLE IF NOT EXISTS fee_payment_transaction_amounts(
	transaction_id varchar(36) NOT NULL,
	fee_id varchar(36) NOT NULL,
	fee_head_id int NOT NULL,
	paid_amount double NOT NULL,
	instant_discount_amount double NOT NULL,
	primary key(transaction_id, fee_id, fee_head_id),
	FOREIGN KEY (transaction_id) REFERENCES fee_payment_transactions(transaction_id),
	FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
	FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
);



ALTER TABLE `lernen`.`academic_session` ADD COLUMN `display_name` VARCHAR(255) NULL AFTER `end_month`;

ALTER TABLE `lernen`.`students` ADD COLUMN `status` VARCHAR(255) NOT NULL DEFAULT "ENROLLED";

ALTER TABLE `lernen`.`students` ADD COLUMN `student_documents` text NULL;






ALTER TABLE `lernen`.`fee_payment_transactions` ADD COLUMN `debit_wallet_amount` double NOT NULL default 0;
ALTER TABLE `lernen`.`fee_payment_transactions` ADD COLUMN `credit_wallet_amount` double NOT NULL default 0;
ALTER TABLE `lernen`.`fee_payment_transactions` ADD COLUMN `academic_session_id` double NOT NULL;

ALTER TABLE `lernen`.`fee_configuration` ADD COLUMN `include_transport` tinyint(1) NOT NULL default 0;	
ALTER TABLE `lernen`.`fee_configuration` ADD COLUMN `transport_proportion_numerator` double;	
ALTER TABLE `lernen`.`fee_configuration` ADD COLUMN `transport_proportion_denominator` double;



CREATE TABLE IF NOT EXISTS student_wallet(
	institute_id int NOT NULL,
	student_id varchar(36) NOT NULL,
	net_amount double NOT NULL default 0,
	primary key(student_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id)
);




CREATE TABLE IF NOT EXISTS transport_area(
	institute_id int NOT NULL,
	area_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	area varchar(1024) NOT NULL,
	area_key varchar(255) NOT NULL,
	UNIQUE KEY (institute_id, area_key)
);

CREATE TABLE IF NOT EXISTS transport_vehicle(
	institute_id int NOT NULL,
	vehicle_id int PRIMARY KEY AUTO_INCREMENT NOT NULL,
	vehicle_number varchar(255) NOT NULL,
	vehicle_code varchar(525) NOT NULL,
	registration_number varchar(525),
	vehicle_type varchar(100) NOT NULL,
	capacity int NOT NULL,
	active tinyint NOT NULL,
	UNIQUE KEY (institute_id, vehicle_code),
	UNIQUE KEY (institute_id, vehicle_number),
	UNIQUE KEY (institute_id, registration_number)
);

CREATE TABLE IF NOT EXISTS transport_service_route(
	institute_id int NOT NULL,
	service_route_id varchar(36) PRIMARY KEY NOT NULL,
	service_route_name varchar(1024) NOT NULL,
	vehicle_id int NOT NULL,
	UNIQUE KEY (institute_id, service_route_name),
	FOREIGN KEY (vehicle_id) REFERENCES transport_vehicle(vehicle_id)
);


CREATE TABLE IF NOT EXISTS transport_service_route_stoppages(
	service_route_id varchar(36) NOT NULL,
	area_id int NOT NULL,
	pickup_time varchar(255),
	drop_time varchar(255),
	assigned_amount double,
	PRIMARY KEY (service_route_id, area_id),
	FOREIGN KEY (service_route_id) REFERENCES transport_service_route(service_route_id),
	FOREIGN KEY (area_id) REFERENCES transport_area(area_id)
);


CREATE TABLE IF NOT EXISTS transport_history(
	institute_id int NOT NULL,
	transport_history_id varchar(36) PRIMARY KEY NOT NULL,
	academic_session_id int NOT NULL,
	student_id varchar(36) NOT NULL,
	service_route_id varchar(36) NOT NULL,
	area_id int NOT NULL,
	current_time_stamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	start_date TIMESTAMP NULL DEFAULT NULL ,
	end_date TIMESTAMP NULL DEFAULT NULL,
	transport_status varchar(100),
	FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
	FOREIGN KEY (student_id) REFERENCES students(student_id),
	FOREIGN KEY (service_route_id) REFERENCES transport_service_route(service_route_id),
	FOREIGN KEY (area_id) REFERENCES transport_area(area_id)
);


CREATE TABLE IF NOT EXISTS transport_history_fee_id_mapping(
	transport_history_id varchar(36) NOT NULL,
	fee_id varchar(36) NOT NULL,
	amount double,
	PRIMARY KEY (transport_history_id, fee_id),
	FOREIGN KEY (transport_history_id) REFERENCES transport_history(transport_history_id),
	FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id)
);