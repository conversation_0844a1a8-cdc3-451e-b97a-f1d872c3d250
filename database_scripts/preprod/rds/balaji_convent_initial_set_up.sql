use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2) 
VALUES (10010,'BALA JI CONVENT SCHOOL', 'Derajasar ward no 1 Ratangarh', null, 'Ratangarh', 'Rajasthan', 'India', '331022', null, '/static/core/images/balaji_convent_logo.png', '<EMAIL>', '8239994813,01567-222936', '(Co-Educational English Medium School)', 'Derajasar Ward No. 1 Ratangarh-(Raj)');

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10010, 2018, 2019, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10010, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10010, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10010, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10010, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10010, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10010, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10010, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10010, (SELECT fee_category_id from fee_category where institute_id = 10010 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10010,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10010,'books',0);
insert into categories (institute_id,category_name) values (10010,'clothing');
insert into categories (institute_id,category_name) values (10010,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10010,'note book',0,0);
insert into categories (institute_id,category_name) values (10010,'art & craft');
insert into categories (institute_id,category_name) values (10010,'personal care');
insert into categories (institute_id,category_name) values (10010,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10010,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10010,'accessories');



insert into colors (institute_id, color_name) values (10010,'maroon');
insert into colors (institute_id, color_name) values (10010,'black');
insert into colors (institute_id, color_name) values (10010,'brown');
insert into colors (institute_id, color_name) values (10010,'white');
insert into colors (institute_id, color_name) values (10010,'red');
insert into colors (institute_id, color_name) values (10010,'yellow');
insert into colors (institute_id, color_name) values (10010,'blue');
insert into colors (institute_id, color_name) values (10010,'navy blue');
insert into colors (institute_id, color_name) values (10010,'green');
insert into colors (institute_id, color_name) values (10010,'dark green');
insert into colors (institute_id, color_name) values (10010,'pink');
insert into colors (institute_id, color_name) values (10010,'purple');
insert into colors (institute_id, color_name) values (10010,'grey');
insert into colors (institute_id, color_name) values (10010,'olive');
insert into colors (institute_id, color_name) values (10010,'cyan');
insert into colors (institute_id, color_name) values (10010,'magenta');

insert into counters (institute_id, counter_type, count, counter_prefix) values (10010, 'FEE_INVOICE', 251, "");

