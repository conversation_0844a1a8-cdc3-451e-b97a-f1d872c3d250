use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (100,'C.R.P Sec. School', 'Ramanagar, Ward No. 6', 'Nohar, Hanumangarh', '', 'Rajasthan', 'India', '', '', '', '<EMAIL>', '9460687346,9414636350', '', '', "add463c8-44be-4471-b20c-18cc10494c75");

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 10100;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(100, 2020, 2021, 5, 4);

insert into fee_category (institute_id, fee_category, description) values (100, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (100, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (100, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (100, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (100, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (100, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (100, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(100, (SELECT fee_category_id from fee_category where institute_id = 100 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (100,'stationery',0);
insert into categories (institute_id,category_name,genders) values (100,'books',0);
insert into categories (institute_id,category_name) values (100,'clothing');
insert into categories (institute_id,category_name) values (100,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (100,'note book',0,0);
insert into categories (institute_id,category_name) values (100,'art & craft');
insert into categories (institute_id,category_name) values (100,'personal care');
insert into categories (institute_id,category_name) values (100,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (100,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (100,'accessories');
insert into categories (institute_id,category_name) values (100,'furniture');
insert into categories (institute_id,category_name) values (100,'electronics');
insert into categories (institute_id,category_name) values (100,'sports');


insert into colors (institute_id, color_name) values (100,'maroon');
insert into colors (institute_id, color_name) values (100,'black');
insert into colors (institute_id, color_name) values (100,'brown');
insert into colors (institute_id, color_name) values (100,'white');
insert into colors (institute_id, color_name) values (100,'red');
insert into colors (institute_id, color_name) values (100,'yellow');
insert into colors (institute_id, color_name) values (100,'blue');
insert into colors (institute_id, color_name) values (100,'navy blue');
insert into colors (institute_id, color_name) values (100,'green');
insert into colors (institute_id, color_name) values (100,'dark green');
insert into colors (institute_id, color_name) values (100,'pink');
insert into colors (institute_id, color_name) values (100,'purple');
insert into colors (institute_id, color_name) values (100,'grey');
insert into colors (institute_id, color_name) values (100,'olive');
insert into colors (institute_id, color_name) values (100,'cyan');
insert into colors (institute_id, color_name) values (100,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (100, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '100', 'meta_data', 'enable_permission', 'true');

--insert into default_fee_assignment_structure_meta_data values(100, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=100 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 100 and fee_type = "REGULAR" and academic_session_id = 26;



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(100, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(100, "Grade", "SYSTEM", "GRADE", 1);