use lernen

--INSERT INTO organisation (organisation_id, name)
--VALUES ("048a25e4-62e6-11ea-bc55-0242ac130003", "A.G. MISSION SCHOOL");

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code) 
VALUES (10060,'A.G. MISSION SHIKSHAN SANSTHAN', 'Bigga Bass', null, 'Sri Dungargarh', 'Rajasthan', 'India', '331803', '', '', '<EMAIL>', '8058079538', '', '', "1f397570-62e6-11ea-bc55-0242ac130003");

--update institute set branch_name = "A.G. MISSION SCHOOL", institute_name = "A.G. MISSION SCHOOL", logo_url="" where institute_id = 10060;

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10060, 2019, 2020, 7, 6);

insert into fee_category (institute_id, fee_category, description) values (10060, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10060, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10060, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10060, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10060, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10060, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10060, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag) 
VALUES(10060, (SELECT fee_category_id from fee_category where institute_id = 10060 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10060,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10060,'books',0);
insert into categories (institute_id,category_name) values (10060,'clothing');
insert into categories (institute_id,category_name) values (10060,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10060,'note book',0,0);
insert into categories (institute_id,category_name) values (10060,'art & craft');
insert into categories (institute_id,category_name) values (10060,'personal care');
insert into categories (institute_id,category_name) values (10060,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10060,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10060,'accessories');
insert into categories (institute_id,category_name) values (10060,'furniture');
insert into categories (institute_id,category_name) values (10060,'electronics');
insert into categories (institute_id,category_name) values (10060,'sports');



insert into colors (institute_id, color_name) values (10060,'maroon');
insert into colors (institute_id, color_name) values (10060,'black');
insert into colors (institute_id, color_name) values (10060,'brown');
insert into colors (institute_id, color_name) values (10060,'white');
insert into colors (institute_id, color_name) values (10060,'red');
insert into colors (institute_id, color_name) values (10060,'yellow');
insert into colors (institute_id, color_name) values (10060,'blue');
insert into colors (institute_id, color_name) values (10060,'navy blue');
insert into colors (institute_id, color_name) values (10060,'green');
insert into colors (institute_id, color_name) values (10060,'dark green');
insert into colors (institute_id, color_name) values (10060,'pink');
insert into colors (institute_id, color_name) values (10060,'purple');
insert into colors (institute_id, color_name) values (10060,'grey');
insert into colors (institute_id, color_name) values (10060,'olive');
insert into colors (institute_id, color_name) values (10060,'cyan');
insert into colors (institute_id, color_name) values (10060,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10060, 'FEE_INVOICE', 1, "");
-- insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10060', 'meta_data', 'enable_permission', 'true');

insert into default_fee_assignment_structure_meta_data values(10060, "Monthly Fees", "2fc2de45-c1f6-400e-9e8a-3109422e22dd", "ENROLLMENT");
insert into default_fee_assignment_structure select "2fc2de45-c1f6-400e-9e8a-3109422e22dd", (select standard_id from standards where institute_id=10060 and standard_name = '8th'), "CLASS", fee_id , 134, 1550 from fee_configuration where institute_id = 10060 and fee_type = "REGULAR";



insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10060, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10060, "Grade", "SYSTEM", "GRADE", 1);


