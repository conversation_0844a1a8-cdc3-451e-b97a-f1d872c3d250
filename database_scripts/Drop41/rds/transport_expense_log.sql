CREATE TABLE IF NOT EXISTS vehicle_transactions (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    transaction_id VARCHAR(36) NOT NULL PRIMARY KEY,
    transaction_mode VARCHAR(225) NOT NULL,
    amount DOUBLE,
    transaction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    vehicle_id INT NOT NULL,
    transport_staff_id VARCHAR(36),
    service_route_id VARCHAR(36),
    transaction_documents TEXT NULL,
    transaction_category VARCHAR(225) NOT NULL,
    created_user_id VARCHAR(36) NOT NULL,
    created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_user_id VARCHAR(36) NULL,
    updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    quantity DOUBLE NULL,
    rate DOUBLE NULL,
    km_reading INT NULL,
    part_of_vehicle varchar(1024) NULL,
    transaction_title varchar(1024) NULL,
    transaction_type VARCHAR(225) NULL,
    FOREIG<PERSON> KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (vehicle_id) REFERENCES transport_vehicle(vehicle_id),
    FOREIGN KEY (service_route_id) REFERENCES transport_service_route(service_route_id),
    FOREIGN KEY (transport_staff_id) REFERENCES staff_details(staff_id)
) COLLATE = latin1_swedish_ci;

CREATE TABLE IF NOT EXISTS vehicle_reading_logs (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    log_id VARCHAR(36) NOT NULL PRIMARY KEY,
    vehicle_id INT NOT NULL,
    log_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    transport_staff_id varchar(36),
    service_route_id VARCHAR(36),
    morning_km_reading DOUBLE NULL,
    evening_km_reading DOUBLE NULL,
    toll_tax DOUBLE NULL,
    remarks varchar(500) NULL,
    created_user_id VARCHAR(36) NOT NULL,
    created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_user_id VARCHAR(36) NULL,
    updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (vehicle_id) REFERENCES transport_vehicle(vehicle_id),
    FOREIGN KEY (service_route_id) REFERENCES transport_service_route(service_route_id),
    FOREIGN KEY (transport_staff_id) REFERENCES staff_details(staff_id)
) COLLATE = latin1_swedish_ci;

ALTER TABLE vehicle_transactions ADD COLUMN description TEXT;
