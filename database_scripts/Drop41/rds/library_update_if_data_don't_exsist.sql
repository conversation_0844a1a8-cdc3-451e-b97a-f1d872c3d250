drop table library_ledger;
drop table book_tags;
drop table book_details;

CREATE TABLE IF NOT EXISTS publication(
	institute_id int NOT NULL,
	publication_id varchar(36) PRIMARY KEY NOT NULL,
	publication_name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, publication_name)
);
CREATE TABLE IF NOT EXISTS publisher (
	institute_id int NOT NULL,
    publisher_id varchar(36) PRIMARY KEY NOT NULL,
    publisher_name VARCHAR(255) NOT NULL,
    contact_information VARCHAR(500),
    affiliation VARCHAR(255),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, publisher_name)
);
CREATE TABLE IF NOT EXISTS genre (
	entity_id VARCHAR(255) NOT NULL,
    entity_name VARCHAR(255) NOT NULL,
    genre_id varchar(36) PRIMARY KEY NOT NULL,
    genre_name VARCHAR(255) NOT NULL,
    classification_number VARCHAR(50),
    UNIQUE (entity_id, genre_name)
    
);
CREATE TABLE IF NOT EXISTS vendor (
	institute_id int NOT NULL,
    vendor_id varchar(36) PRIMARY KEY NOT NULL,
    vendor_name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    gst_number VARCHAR(20),
    account_type VARCHAR(100),
    bank_name VARCHAR(255),
    account_holder_name VARCHAR(500),
    account_number VARCHAR(100),
    ifsc_code VARCHAR(50),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, vendor_name)
);
CREATE TABLE IF NOT EXISTS author (
	institute_id int NOT NULL,
    author_id varchar(36) PRIMARY KEY NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    nationality VARCHAR(100),
    date_of_birth TIMESTAMP,
    associated_genres VARCHAR(1024),
    short_biography VARCHAR(2048),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, author_name)
);
CREATE TABLE IF NOT EXISTS library_type (
	institute_id int NOT NULL,
    library_type_id varchar(36) PRIMARY KEY NOT NULL,
    library_type_name VARCHAR(100) NOT NULL,
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, library_type_name)
);

CREATE TABLE IF NOT EXISTS book_details (
	institute_id int NOT NULL,
    book_id VARCHAR(36) PRIMARY KEY NOT NULL, 
    book_title VARCHAR(255) NOT NULL,
    book_no varchar(255),
    book_tags varchar(1024) CHARACTER SET utf8,
    publication_id VARCHAR(36), 
    publisher_id VARCHAR(36), 
    genre_id VARCHAR(36), 
    author_id VARCHAR(36), 
    isbn_number VARCHAR(20) NOT NULL UNIQUE, 
    edition VARCHAR(1024),
    no_of_copies INT DEFAULT 1, 
    publish_year INT, 
    language VARCHAR(32),
    book_documents TEXT, 
	type_of_binding VARCHAR(50),
    number_of_pages INT, 
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    
    Foreign Key (institute_id) REFERENCES institute(institute_id),
	FOREIGN KEY (publication_id) REFERENCES publication(publication_id),
	FOREIGN KEY (publisher_id) REFERENCES publisher(publisher_id),
	FOREIGN KEY (genre_id) REFERENCES genre(genre_id),
	FOREIGN KEY (author_id) REFERENCES author(author_id)
);

CREATE TABLE IF NOT EXISTS individual_book_details (
    accession_id VARCHAR(36) PRIMARY KEY, 
    institute_id int NOT NULL,
    book_id VARCHAR(36) NOT NULL, 
    accession_number VARCHAR(50) NOT NULL, 
    rack VARCHAR(100), 
    price DECIMAL(10, 2), 
    bill_number VARCHAR(50), 
    date_of_purchase TIMESTAMP, 
    vendor_id VARCHAR(36),
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE', 
    volume VARCHAR(50), 
    library_type_id VARCHAR(36), 
    remark VARCHAR(500), 
    
	FOREIGN KEY (book_id) REFERENCES book_details(book_id),
    FOREIGN KEY (vendor_id) REFERENCES vendor(vendor_id),
    FOREIGN KEY (library_type_id) REFERENCES library_type(library_type_id),
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, accession_number)
);

CREATE TABLE IF NOT EXISTS library_ledger(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
	transaction_id varchar(36) PRIMARY KEY, 
	user_id varchar(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
	user_type varchar(255) NOT NULL,
	book_id varchar(36) NOT NULL,
	accession_id VARCHAR(36) NOT NULL,
	duration int NOT NULL,
	issued_timestamp TIMESTAMP NOT NULL,
	issued_by varchar(36) NOT NULL,
	returned_timestamp TIMESTAMP,
	received_by varchar(36),
	status varchar(255) NOT NULL,
	added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	Foreign Key (book_id) REFERENCES book_details(book_id),
    Foreign Key (accession_id) REFERENCES individual_book_details(accession_id),
	Foreign Key (academic_session_id) REFERENCES academic_session(academic_session_id),
	Foreign Key (institute_id) REFERENCES institute(institute_id),
	Foreign Key (user_id) REFERENCES users(user_id)
);

CREATE INDEX idx_book_details_book_tags ON book_details(book_tags);
CREATE INDEX idx_book_details_book_title ON book_details(book_title);
CREATE INDEX idx_book_details_book_no ON book_details(book_no);
CREATE INDEX idx_book_details_isbn_number ON book_details(isbn_number);
CREATE INDEX idx_individual_book_details_accession_number ON individual_book_details(accession_number);
CREATE INDEX idx_library_type_library_type_name ON library_type(library_type_name);
CREATE INDEX idx_author_author_name ON author(author_name);
CREATE INDEX idx_genre_genre_name ON genre(genre_name);
CREATE INDEX idx_genre_classification_number ON genre(classification_number);
CREATE INDEX idx_publisher_publisher_name ON publisher(publisher_name);
CREATE INDEX idx_publication_publication_name ON publication(publication_name);