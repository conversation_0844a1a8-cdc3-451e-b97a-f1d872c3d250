ALTER TABLE assessment_metadata ADD COLUMN total_marks DOUBLE NOT NULL DEFAULT 0.0;

RENAME TABLE assessment_student_answer TO assessment_student_responses;

CREATE INDEX idx_assessment ON assessment_student_responses (institute_id, academic_session_id, assessment_id, student_id);

select * from user_permissions where action = "ADD_ASSESSMENT_ACCESS";
UPDATE user_permissions set action = "ASSESSMENT_MODIFICATION_ACCESS" where action = "ADD_ASSESSMENT_ACCESS";

select * from user_permissions where action = "ADD_QUESTION_ACCESS";
UPDATE user_permissions set action = "QUESTION_MODIFICATION_ACCESS" where action = "ADD_QUESTION_ACCESS";

