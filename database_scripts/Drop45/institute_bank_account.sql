CREATE TABLE IF NOT EXISTS institute_bank_account_details (
institute_id INT NOT NULL,
bank_account_id VARCHAR(36) PRIMARY KEY NOT NULL,
account_holder_name VARCHAR(512),
bank_name VA<PERSON>HAR(512),
branch_name VA<PERSON>HA<PERSON>(512),
account_number VARCHAR(64),
ifsc_code VA<PERSON>HAR(32),
account_type VA<PERSON>HAR(32),
is_primary TINYINT(1) DEFAULT 0,
status VARCHAR(32) NOT NULL DEFAULT "ACTIVE",
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT NULL,
FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);


ALTER TABLE fee_payment_transactions ADD COLUMN bank_account_id VARCHAR(36) DEFAULT NULL;

-- add FOREIGN key
ALTER TABLE fee_payment_transactions MODIFY bank_account_id VARCHAR(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL;
ALTER TABLE fee_payment_transactions ADD CONSTRAINT fk_fee_payment_bank_account FOREIGN KEY (bank_account_id) REFERENCES institute_bank_account_details(bank_account_id);
