CREATE TABLE IF NOT EXISTS student_expense_services (
    institute_id INT NOT NULL,
    service_id varchar(36) NOT NULL PRIMARY KEY,
    service_name VARCHAR(255) NOT NULL,
    service_category VARCHAR(255) NOT NULL,
    status VARCHAR(36) DEFAULT 'ACTIVE',
    description VARCHAR(1024),
    price double
);

CREATE TABLE IF NOT EXISTS student_finance_transaction (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    transaction_id VARCHAR(36) PRIMARY KEY,
    invoice_id VARCHAR(36) NOT NULL,
    service_id VARCHAR(36) NOT NULL,
    student_id VARCHAR(36) CHARACTER SET latin1 NOT NULL,
    transaction_mode VARCHAR(32),
    paid_amount DOUBLE,
    wallet_debit_amount DOUBLE,
    wallet_based_credit_amount DOUBLE,
    transaction_reference VARCHAR(512),
    transaction_status VARCHAR(36) DEFAULT 'ACTIVE',
    transaction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_user_id VARCHAR(36) NOT NULL,
    created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_user_id VARCHAR(36),
    documents TEXT,
    description VARCHAR(2000),
    FOREIGN KEY (service_id) REFERENCES student_expense_services(service_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);

select * from configuration where config_key = "wallet_based_credit_limit";
UPDATE configuration set config_type = 'student_wallet_preferences' where config_key = 'wallet_based_credit_limit';

select institute_id, "STUDENTS_FINANCE_EXPENSE_NUMBER", 1, 'SS-' from institute;

insert into counters select institute_id, "STUDENTS_FINANCE_EXPENSE_NUMBER", 1, 'SS-' from institute;


select distinct(transaction_mode) from user_wallet_transaction_history where transaction_category in ('INVENTORY_PAYMENT', 'INVENTORY_CREDIT');
update user_wallet_transaction_history set transaction_mode = 'WALLET' where transaction_category in ('INVENTORY_PAYMENT', 'INVENTORY_CREDIT');
