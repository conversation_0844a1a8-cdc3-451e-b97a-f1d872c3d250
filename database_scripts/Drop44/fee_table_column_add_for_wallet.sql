alter table fee_configuration ADD COLUMN transfer_to_wallet tinyint(1) DEFAULT 0;

--Uncomment and run in DB
--select * from user_wallet_transaction_history where transaction_category = "FEE_PAYMENT_CANCELLATION";
--update user_wallet_transaction_history set transaction_category = "FEE_CANCELLATION_TRANSFERRED_TO_WALLET" where transaction_category = "FEE_PAYMENT_CANCELLATION";
--select * from user_wallet_transaction_history where transaction_category = "FEE_PAYMENT";
--update user_wallet_transaction_history set transaction_category = "FEE_PAYMENT_TRANSFERRED_TO_WALLET" where transaction_category = "FEE_PAYMENT";
