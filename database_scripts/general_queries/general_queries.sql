select users.institute_id, users.user_id, count(*) from tracking_events join  users on users.user_id = tracking_events.user_id where event_name = "MOBILE_APP_OPEN"  group by users.institute_id, users.user_id;

select users.institute_id, count(distinct(users.user_id)) from tracking_events join  users on users.user_id = tracking_events.user_id where event_name = "MOBILE_APP_OPEN"  group by users.institute_id;