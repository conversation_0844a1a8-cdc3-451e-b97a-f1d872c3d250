CREATE TABLE IF NOT EXISTS hpc_form (
    form_id VARCHAR(36) NOT NULL,
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    standard_id VARCHAR(36) CHARACTER SET latin1 NOT NULL,
    name VA<PERSON>HAR(128) NOT NULL,
    form TEXT NOT NULL,
    PRIMARY KEY (form_id),
    UNIQUE KEY (institute_id, academic_session_id, standard_id, name),
    FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);

alter table hpc_form modify column form longtext;