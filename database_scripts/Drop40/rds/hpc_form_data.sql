CREATE TABLE IF NOT EXISTS hpc_form_data (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    student_id varchar(36) CHARACTER SET latin1 NOT NULL,
    exam_type VARCHAR(36) NOT NULL,
    form_user_type VARCHAR(36) NOT NULL,
    status VARCHAR(36) NOT NULL,
    form_value TEXT NOT NULL,
    PRIMARY KEY (institute_id, academic_session_id, student_id, exam_type, form_user_type),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);