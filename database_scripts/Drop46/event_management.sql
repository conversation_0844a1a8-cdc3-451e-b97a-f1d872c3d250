CREATE TABLE IF NOT EXISTS event_details (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    event_id VARCHAR(36) NOT NULL PRIMARY KEY,
    title VARCHAR(256) NOT NULL,
    description VARCHAR(1024),
    event_date TIMESTAMP NOT NULL,
    category VA<PERSON>HAR(256) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    meta_data TEXT,
    documents TEXT,
    created_by <PERSON><PERSON><PERSON><PERSON>(36),
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id)
);


ALTER TABLE event_details
RENAME COLUMN event_date TO start_date,
ADD COLUMN end_date TIMESTAMP,
ADD COLUMN remarks VARCHAR(1024);


