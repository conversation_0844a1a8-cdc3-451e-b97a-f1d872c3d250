CREATE TABLE IF NOT EXISTS question_bank (
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    question_id VARCHAR(36) PRIMARY KEY NOT NULL,
    question_text TEXT NOT NULL,
    question_type ENUM('OB<PERSON>ECTIVE', 'SU<PERSON><PERSON>ECTIVE') NOT NULL,
    answer TEXT,
    marks double,
    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    difficulty_level ENUM('EASY', 'MEDIUM', 'HARD') NULL,
    tags VARCHAR(255),
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    Foreign Key (academic_session_id) REFERENCES academic_session(academic_session_id)
);

CREATE TABLE IF NOT EXISTS question_options(
    options_id VARCHAR(36) PRIMARY KEY NOT NULL,
    question_id VARCHAR(36) NOT NULL,
    option_text TEXT NOT NULL,
    Foreign Key (question_id) REFERENCES question_bank(question_id)
);

CREATE TABLE IF NOT EXISTS assessment_metadata(
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    assessment_id VARCHAR(36) PRIMARY KEY NOT NULL,
    assessment_name VARCHAR(255) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    assessment_date TIMESTAMP NOT NULL, 
    duration INT NOT NULL, 
    passkey VARCHAR(50), 
    assessment_instructions VARCHAR(255), 
    auto_submit_timer BOOLEAN DEFAULT TRUE, 
    show_result_immediately BOOLEAN DEFAULT FALSE, 
    published_status ENUM('PUBLISHED', 'UNPUBLISHED') DEFAULT 'UNPUBLISHED' NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, 
    last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    Foreign Key (institute_id) REFERENCES institute(institute_id),
    Foreign Key (academic_session_id) REFERENCES academic_session(academic_session_id)
);
CREATE TABLE IF NOT EXISTS assessment_entity_mapping (
    entity_id VARCHAR(36) NOT NULL,
    entity_name VARCHAR(36) NOT NULL,
    exam_id VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci, 
    course_id VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci,
    standard_id VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci,
    section_id INT,
    UNIQUE (entity_id, entity_name, exam_id, course_id, standard_id, section_id),
    FOREIGN KEY (exam_id) REFERENCES exam_metadata(exam_id),
    FOREIGN KEY (course_id) REFERENCES class_courses(course_id),
    FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
    FOREIGN KEY (section_id) REFERENCES standard_section_mapping(section_id)
);

CREATE TABLE IF NOT EXISTS assessment_questions_mapping(
	assessment_id VARCHAR(36) NOT NULL,
    question_id VARCHAR(36) NOT NULL,
    sequence INT NOT NULL,
    UNIQUE (assessment_id, question_id),
    UNIQUE (assessment_id, sequence),
	FOREIGN KEY (assessment_id) REFERENCES assessment_metadata(assessment_id),
	Foreign Key (question_id) REFERENCES question_bank(question_id)
);

CREATE TABLE IF NOT EXISTS assessment_student_answer(
	institute_id int NOT NULL,
    academic_session_id int NOT NULL,
	student_id VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    assessment_id VARCHAR(36) NOT NULL,
    question_id VARCHAR(36) NOT NULL,
    answer TEXT,
    marks double,
    answer_status ENUM('SAVED', 'SUBMITTED') DEFAULT 'SAVED' NOT NULL,
    UNIQUE (student_id, assessment_id, question_id, institute_id, academic_session_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    Foreign Key (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (student_id) REFERENCES students(student_id),
    FOREIGN KEY (assessment_id) REFERENCES assessment_metadata(assessment_id),
	FOREIGN KEY (question_id) REFERENCES question_bank(question_id)
);

