CREATE TABLE IF NOT EXISTS book_details(
	institute_id int NOT NULL,
	book_id varchar(36) PRIMARY KEY NOT NULL,
	genre varchar(255),
	book_title varchar(255) NOT NULL,
	author varchar(255),
	isbn_number varchar(255) UNIQUE NOT NULL,
	publication varchar(255),
	edition varchar(1024),
	no_of_copies int NOT NULL,
	publish_year int,
	language varchar(32), 
	rack varchar(512),
	book_documents text,
	price_per_copy double,
	added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	Foreign Key (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS book_tags(
	tag varchar(255) NOT NULL,
	book_id varchar(36) NOT NULL,
	Foreign Key (book_id) REFERENCES book_details(book_id),
	PRIMARY KEY(tag,book_id)
);

CREATE TABLE IF NOT EXISTS library_ledger(
	institute_id int NOT NULL,
	academic_session_id int NOT NULL,
	transaction_id varchar(36) PRIMARY KEY, 
	user_id varchar(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
	user_type varchar(255) NOT NULL,
	book_id varchar(36) NOT NULL,
	book_no varchar(255),
	duration int NOT NULL,
	issued_timestamp TIMESTAMP NOT NULL,
	issued_by varchar(36) NOT NULL,
	returned_timestamp TIMESTAMP,
	received_by varchar(36),
	status varchar(255) NOT NULL,
	added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	Foreign Key (book_id) REFERENCES book_details(book_id),
	Foreign Key (academic_session_id) REFERENCES academic_session(academic_session_id),
	Foreign Key (institute_id) REFERENCES institute(institute_id),
	Foreign Key (user_id) REFERENCES users(user_id)
);