-- === DROP TABLES IF THEY EXIST ===
DROP TABLE IF EXISTS library_ledger;
DROP TABLE IF EXISTS individual_book_details;
DROP TABLE IF EXISTS book_details;
DROP TABLE IF EXISTS author;
DROP TABLE IF EXISTS vendor;
DROP TABLE IF EXISTS genre;
DROP TABLE IF EXISTS publisher;
DROP TABLE IF EXISTS publication;
DROP TABLE IF EXISTS library_type;

-- === CREATE TABLES ===
CREATE TABLE IF NOT EXISTS publication (
    institute_id INT NOT NULL,
    publication_id VARCHAR(36) PRIMARY KEY NOT NULL,
    publication_name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS publisher (
    institute_id INT NOT NULL,
    publisher_id VARCHAR(36) PRIMARY KEY NOT NULL,
    publisher_name VARCHAR(255) NOT NULL,
    contact_information VARCHAR(500),
    affiliation VARCHAR(255),
    FOREI<PERSON><PERSON> KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS genre (
    entity_id VARCHAR(255) NOT NULL,
    entity_name VARCHAR(255) NOT NULL,
    genre_id VARCHAR(36) PRIMARY KEY NOT NULL,
    genre_name VARCHAR(255) NOT NULL,
    classification_number VARCHAR(50)
);

CREATE TABLE IF NOT EXISTS vendor (
    institute_id INT NOT NULL,
    vendor_id VARCHAR(36) PRIMARY KEY NOT NULL,
    vendor_name VARCHAR(255) NOT NULL,
    address VARCHAR(500),
    phone_number VARCHAR(20),
    email VARCHAR(255),
    gst_number VARCHAR(50),
    account_type VARCHAR(100),
    bank_name VARCHAR(255),
    account_holder_name VARCHAR(500),
    account_number VARCHAR(100),
    ifsc_code VARCHAR(50),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS author (
    institute_id INT NOT NULL,
    author_id VARCHAR(36) PRIMARY KEY NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    nationality VARCHAR(100),
    date_of_birth TIMESTAMP,
    associated_genres VARCHAR(1024),
    short_biography VARCHAR(2048),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS library_type (
    institute_id INT NOT NULL,
    library_type_id VARCHAR(36) PRIMARY KEY NOT NULL,
    library_type_name VARCHAR(100) NOT NULL,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

CREATE TABLE IF NOT EXISTS book_details (
    institute_id INT NOT NULL,
    book_id VARCHAR(36) PRIMARY KEY NOT NULL,
    book_title VARCHAR(255) NOT NULL,
    book_no VARCHAR(255),
    book_tags VARCHAR(1024) CHARACTER SET utf8,
    publication_id VARCHAR(36),
    publisher_id VARCHAR(36),
    genre_id VARCHAR(36),
    author_id VARCHAR(36),
    isbn_number VARCHAR(20) NOT NULL UNIQUE,
    edition VARCHAR(1024),
    no_of_copies INT DEFAULT 1,
    publish_year INT,
    language VARCHAR(32),
    book_documents TEXT,
    type_of_binding VARCHAR(50),
    number_of_pages INT,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (publication_id) REFERENCES publication(publication_id),
    FOREIGN KEY (publisher_id) REFERENCES publisher(publisher_id),
    FOREIGN KEY (genre_id) REFERENCES genre(genre_id),
    FOREIGN KEY (author_id) REFERENCES author(author_id)
);

CREATE TABLE IF NOT EXISTS individual_book_details (
    accession_id VARCHAR(36) PRIMARY KEY,
    institute_id INT NOT NULL,
    book_id VARCHAR(36) NOT NULL,
    accession_number VARCHAR(50) NOT NULL,
    rack VARCHAR(100),
    purchase_price DECIMAL(10,2),
    mrp DECIMAL(10,2),
    bill_number VARCHAR(255),
    date_of_purchase TIMESTAMP,
    added_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    vendor_id VARCHAR(36),
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    volume VARCHAR(50),
    library_type_id VARCHAR(36),
    remark VARCHAR(500),
    FOREIGN KEY (book_id) REFERENCES book_details(book_id),
    FOREIGN KEY (vendor_id) REFERENCES vendor(vendor_id),
    FOREIGN KEY (library_type_id) REFERENCES library_type(library_type_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    UNIQUE (institute_id, accession_number)
);

CREATE TABLE IF NOT EXISTS library_ledger (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    transaction_id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    user_type VARCHAR(255) NOT NULL,
    book_id VARCHAR(36) NOT NULL,
    accession_id VARCHAR(36) NOT NULL,
    duration INT NOT NULL,
    issued_timestamp TIMESTAMP NOT NULL,
    issued_by VARCHAR(36) NOT NULL,
    returned_timestamp TIMESTAMP,
    received_by VARCHAR(36),
    status VARCHAR(255) NOT NULL,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES book_details(book_id),
    FOREIGN KEY (accession_id) REFERENCES individual_book_details(accession_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- === INDEXES ===
CREATE INDEX idx_book_details_book_tags ON book_details(book_tags);
CREATE INDEX idx_book_details_book_title ON book_details(book_title);
CREATE INDEX idx_book_details_book_no ON book_details(book_no);
CREATE INDEX idx_book_details_isbn_number ON book_details(isbn_number);
CREATE INDEX idx_individual_book_details_accession_number ON individual_book_details(accession_number);
CREATE INDEX idx_library_type_library_type_name ON library_type(library_type_name);
CREATE INDEX idx_author_author_name ON author(author_name);
CREATE INDEX idx_genre_genre_name ON genre(genre_name);
CREATE INDEX idx_genre_classification_number ON genre(classification_number);
CREATE INDEX idx_publisher_publisher_name ON publisher(publisher_name);
CREATE INDEX idx_publication_publication_name ON publication(publication_name);

-- === COUNTER INSERT ===
INSERT INTO counters (institute_id, counter_type, count, counter_prefix)
SELECT i.institute_id, 'ISBN_NUMBER', 0, ''
FROM institute i
WHERE NOT EXISTS (
  SELECT 1 FROM counters c 
  WHERE c.institute_id = i.institute_id AND c.counter_type = 'ISBN_NUMBER'
);



-- AUTHOR
ALTER TABLE author DROP FOREIGN KEY author_ibfk_1;
ALTER TABLE author DROP INDEX institute_id;
ALTER TABLE author ADD CONSTRAINT author_ibfk_1 FOREIGN KEY (institute_id) REFERENCES institute(institute_id);
ALTER TABLE author ADD INDEX idx_institute_author_name (institute_id, author_name);

-- VENDOR
ALTER TABLE vendor DROP FOREIGN KEY vendor_ibfk_1;
ALTER TABLE vendor DROP INDEX institute_id;
ALTER TABLE vendor ADD CONSTRAINT vendor_ibfk_1 FOREIGN KEY (institute_id) REFERENCES institute(institute_id);
ALTER TABLE vendor ADD INDEX idx_institute_vendor_name (institute_id, vendor_name);

-- GENRE
ALTER TABLE genre DROP INDEX entity_id;
ALTER TABLE genre ADD INDEX idx_entity_genre_name (entity_id, genre_name);

-- PUBLISHER
ALTER TABLE publisher DROP FOREIGN KEY publisher_ibfk_1;
ALTER TABLE publisher DROP INDEX institute_id;
ALTER TABLE publisher ADD CONSTRAINT publisher_ibfk_1 FOREIGN KEY (institute_id) REFERENCES institute(institute_id);
ALTER TABLE publisher ADD INDEX idx_institute_publisher_name (institute_id, publisher_name);

-- PUBLICATION
ALTER TABLE publication DROP FOREIGN KEY publication_ibfk_1;
ALTER TABLE publication DROP INDEX institute_id;
ALTER TABLE publication ADD CONSTRAINT publication_ibfk_1 FOREIGN KEY (institute_id) REFERENCES institute(institute_id);
ALTER TABLE publication ADD INDEX idx_institute_publication_name (institute_id, publication_name);

-- LIBRARY TYPE
ALTER TABLE library_type DROP FOREIGN KEY library_type_ibfk_1;
ALTER TABLE library_type DROP INDEX institute_id;
ALTER TABLE library_type ADD CONSTRAINT library_type_ibfk_1 FOREIGN KEY (institute_id) REFERENCES institute(institute_id);
ALTER TABLE library_type ADD INDEX idx_institute_library_type_name (institute_id, library_type_name);



ALTER TABLE author
MODIFY COLUMN author_name VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE author
MODIFY COLUMN nationality VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE author
MODIFY COLUMN associated_genres VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE author
MODIFY COLUMN short_biography VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE genre
MODIFY COLUMN genre_name VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE library_type
MODIFY COLUMN library_type_name VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE vendor
MODIFY COLUMN vendor_name VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE vendor
MODIFY COLUMN address VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;


ALTER TABLE publisher
MODIFY COLUMN publisher_name VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE publication
MODIFY COLUMN publication_name VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE publication
MODIFY COLUMN address VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE publisher
MODIFY COLUMN contact_information VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE publisher
MODIFY COLUMN affiliation VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;



--if the book_details is also in latin1
ALTER TABLE book_details
MODIFY COLUMN book_title VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci
NOT NULL;

ALTER TABLE book_details
MODIFY COLUMN book_no VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE book_details
MODIFY COLUMN book_tags VARCHAR(1024)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE book_details
MODIFY COLUMN edition VARCHAR(1024)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE book_details
MODIFY COLUMN type_of_binding VARCHAR(50)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE individual_book_details
MODIFY COLUMN bill_number VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE individual_book_details
MODIFY COLUMN accession_number VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci NOT NULL;

ALTER TABLE individual_book_details
MODIFY COLUMN rack VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE individual_book_details
MODIFY COLUMN volume VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

ALTER TABLE individual_book_details
MODIFY COLUMN remark VARCHAR(255)
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;


-- Step 1: Drop the existing unique constraint on isbn_number
ALTER TABLE book_details DROP INDEX isbn_number;

-- Step 2: Add composite unique constraint on (institute_id, isbn_number)
ALTER TABLE book_details ADD UNIQUE KEY unique_institute_isbn (institute_id, isbn_number);


