Sesomu School - Voice Call Issue

1) Totals Calls Initiated - 371
2) Answered Calls - 166

Total Credits Given - 5000
Current Credit Count (As of 14th Feb, 2022 11:11 am) - 4939

mysql> select * from counters where institute_id = 10001;
+--------------+--------------------------+-------+----------------+
| institute_id | counter_type             | count | counter_prefix |
+--------------+--------------------------+-------+----------------+
|        10001 | AUDIO_VOICE_CALL_COUNTER |  4939 |                |
|        10001 | FEE_INVOICE              |  4949 |                |
|        10001 | SMS_COUNTER              | 22248 |                |
+--------------+--------------------------+-------+----------------+

mysql> select credits_transaction_id, count(*) from notification_status where institute_id = 10001 and delivery_mode = 'CALL' and status = 'UNANSWERED' group by credits_transaction_id order by credits_transaction_id;
+--------------------------------------+----------+
| credits_transaction_id               | count(*) |
+--------------------------------------+----------+
| 013dd0f0-dc29-490e-b4e7-3cf8a979d648 |        1 |
| 0a575c8e-7a67-49fc-99c4-9bd746bd194e |        6 |
| 0ceac486-61db-41b7-867f-e9da458e2e6e |        7 |
| 14a11442-2116-4a90-a8e3-509583d8399b |       25 |
| 477f8017-7079-4b4a-a37a-523c7346b111 |        7 |
| 505ae816-b282-473a-a13d-ed08e7869c5d |       49 |
| 68f1bbb2-6b26-4604-8eba-a430a8032ffc |        7 |
| 74b09857-017c-4c82-a4be-7eb210af9951 |       19 |
| 76715de4-72bf-44cd-97d3-ef9e1ffcfd8a |        6 |
| 7e96a830-7e8a-4bf5-a865-a889e7b1bd24 |        4 |
| 907ebc33-bc34-4ecd-9919-c4f9b0c06617 |       14 |
| c6858712-5df0-4e6a-a85f-57bd8676735e |       27 |
| d78695a5-8e98-4660-bd6b-1d91633245a4 |        1 |
| daaa794f-b081-4b13-80f9-9aa815b6850d |        2 |
| dd4ec3dc-c20e-458d-8f02-1b168bfb4bfe |       15 |
| e892beb4-b826-4563-ac6e-3d923ed8f50d |       15 |
+--------------------------------------+----------+
16 <USER> <GROUP> set (0.02 sec)

mysql> select meta_data, count(*) from communication_service_transactions where institute_id = 10001 and delivery_mode = 'CALL' and status = 'CREDIT' group by meta_data order by meta_data;
+-----------------------------------------------------------------------+----------+
| meta_data                                                             | count(*) |
+-----------------------------------------------------------------------+----------+
| {"refund_against_transaction":"013dd0f0-dc29-490e-b4e7-3cf8a979d648"} |        1 |
| {"refund_against_transaction":"0a575c8e-7a67-49fc-99c4-9bd746bd194e"} |        6 |
| {"refund_against_transaction":"0ceac486-61db-41b7-867f-e9da458e2e6e"} |        7 |
| {"refund_against_transaction":"14a11442-2116-4a90-a8e3-509583d8399b"} |       39 |
| {"refund_against_transaction":"477f8017-7079-4b4a-a37a-523c7346b111"} |       14 |
| {"refund_against_transaction":"505ae816-b282-473a-a13d-ed08e7869c5d"} |       67 |
| {"refund_against_transaction":"68f1bbb2-6b26-4604-8eba-a430a8032ffc"} |        7 |
| {"refund_against_transaction":"74b09857-017c-4c82-a4be-7eb210af9951"} |       32 |
| {"refund_against_transaction":"76715de4-72bf-44cd-97d3-ef9e1ffcfd8a"} |        6 |
| {"refund_against_transaction":"7e96a830-7e8a-4bf5-a865-a889e7b1bd24"} |        5 |
| {"refund_against_transaction":"907ebc33-bc34-4ecd-9919-c4f9b0c06617"} |       25 |
| {"refund_against_transaction":"c6858712-5df0-4e6a-a85f-57bd8676735e"} |       45 |
| {"refund_against_transaction":"d78695a5-8e98-4660-bd6b-1d91633245a4"} |        1 |
| {"refund_against_transaction":"daaa794f-b081-4b13-80f9-9aa815b6850d"} |        4 |
| {"refund_against_transaction":"dd4ec3dc-c20e-458d-8f02-1b168bfb4bfe"} |       24 |
| {"refund_against_transaction":"e892beb4-b826-4563-ac6e-3d923ed8f50d"} |       27 |
+-----------------------------------------------------------------------+----------+
16 <USER> <GROUP> set (0.00 sec)


create view t50 as select transaction_id from communication_service_transactions where institute_id = 10001 and delivery_mode = 'CALL' and status = 'CREDIT' and meta_data like '%505ae816-b282-473a-a13d-ed08e7869c5d%' limit 18;

select * from t144 union select * from t47 union  select * from t50 union  select * from t74 union  select * from t7e union  select * from t90  union  select * from tc6 union  select * from tda union  select * from tdd union  select * from te8;

create view delete_transaction_10001 as select * from t144 union select * from t47 union  select * from t50 union  select * from t74 union  select * from t7e union  select * from t90  union  select * from tc6 union  select * from tda union  select * from tdd union  select * from te8;

select * from delete_transaction_10001;

select * from communication_service_transactions join delete_transaction_10001 on communication_service_transactions.transaction_id = delete_transaction_10001.transaction_id where  institute_id = 10001 and delivery_mode = 'CALL' and status = 'CREDIT';

delete communication_service_transactions from communication_service_transactions join delete_transaction_10001 on communication_service_transactions.transaction_id = delete_transaction_10001.transaction_id where  institute_id = 10001 and delivery_mode = 'CALL' and status = 'CREDIT';

mysql> select * from counters where institute_id = 10001;
+--------------+--------------------------+-------+----------------+
| institute_id | counter_type             | count | counter_prefix |
+--------------+--------------------------+-------+----------------+
|        10001 | AUDIO_VOICE_CALL_COUNTER |  4834 |                |
|        10001 | FEE_INVOICE              |  4949 |                |
|        10001 | SMS_COUNTER              | 22248 |                |
+--------------+--------------------------+-------+----------------+
3 <USER> <GROUP> set (0.01 sec)
