CREATE TABLE IF NOT EXISTS lesson_plan_details (
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    lesson_plan_id VARCHAR(36) PRIMARY KEY NOT NULL,
    lesson_plan_title varchar(255),
    description varchar(255),
    standard_id varchar(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    section_id int,
    course_id varchar(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
    added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    added_by varchar(36) NOT NULL,
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by varchar(36) NOT NULL,
    status enum('ACTIVE','IN_ACTIVE') NOT NULL DEFAULT 'ACTIVE',
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIG<PERSON> KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREI<PERSON><PERSON> KEY (standard_id) REFERENCES standards(standard_id),
    FOREIGN KEY (section_id) REFERENCES standard_section_mapping(section_id),
    FOREIGN KEY (course_id) REFERENCES class_courses(course_id)
);

CREATE TABLE IF NOT EXISTS lesson_plan_chapter_mapping (
    lesson_plan_id VARCHAR(36),
    chapter_id VARCHAR(36) PRIMARY KEY NOT NULL,
    chapter_title varchar(255),
    chapter_description varchar(255),
    due_date timestamp DEFAULT NULL,
    FOREIGN KEY (lesson_plan_id) REFERENCES lesson_plan_details(lesson_plan_id)
);

CREATE TABLE IF NOT EXISTS chapter_topic_mapping (
	chapter_id VARCHAR(36),
	topic_id VARCHAR(36) PRIMARY KEY NOT NULL,
	topic_title VARCHAR(255),
	topic_description VARCHAR(255),
	due_date TIMESTAMP DEFAULT NULL,
	added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	FOREIGN KEY (chapter_id) REFERENCES lesson_plan_chapter_mapping(chapter_id)
);

CREATE TABLE IF NOT EXISTS staff_lesson_plan_mapping (
	lesson_plan_id VARCHAR(36) NOT NULL,
	staff_id varchar(36) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
	chapter_id VARCHAR(36) NOT NULL,
	topic_id VARCHAR(36) NOT NULL,
	start_on TIMESTAMP DEFAULT NULL,
	completed_on TIMESTAMP DEFAULT NULL,
	topic_status ENUM('NOT_STARTED', 'IN_PROGRESS', 'COMPLETED') NOT NULL DEFAULT 'NOT_STARTED',
	remarks VARCHAR(255),
	added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	KEY lesson_plan_id (lesson_plan_id),
	KEY staff_id (staff_id),
	KEY chapter_id (chapter_id),
	KEY topic_id (topic_id),
	FOREIGN KEY (lesson_plan_id) REFERENCES lesson_plan_details(lesson_plan_id),
	FOREIGN KEY (staff_id) REFERENCES staff_details(staff_id),
	FOREIGN KEY (chapter_id) REFERENCES lesson_plan_chapter_mapping(chapter_id),
	FOREIGN KEY (topic_id) REFERENCES chapter_topic_mapping(topic_id)
);
