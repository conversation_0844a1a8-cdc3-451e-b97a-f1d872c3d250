CREATE TABLE IF NOT EXISTS standard_staff_assignment(
    institute_id int NOT NULL,
    academic_session_id int NOT NULL,
    standard_id varchar(36) NOT NULL,
    section_id int(11),
    class_teacher_staff_id varchar(36) NOT NULL,
    UNIQUE KEY (institute_id, academic_session_id, standard_id, section_id, class_teacher_staff_id),
    FOREIGN KEY (section_id) REFERENCES standard_section_mapping(section_id),
    FOREIGN KEY (standard_id) REFERENCES standards(standard_id),
    FOREIGN KEY (class_teacher_staff_id) REFERENCES staff_details(staff_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    FOREIG<PERSON> KEY (institute_id) REFERENCES institute(institute_id)
) DEFAULT CHARSET=latin1;