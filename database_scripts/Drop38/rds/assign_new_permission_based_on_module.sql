select institute_id, role_id, 'ROLE', 'HOM<PERSON><PERSON>ORK_MANAGEMENT', 'ACCESS_ALL_CLASSES_HOMEWORK_DATA', 1 from user_role_mapping where user_id in (select user_id from users where authorized_modules like '%HOMEWORK_MANAGEMENT%' and user_type <> 'STUDENT') group by institute_id, role_id;
insert into user_permissions select institute_id, role_id, 'ROLE', 'HOMEWORK_MANAGEMENT', 'ACCESS_ALL_CLASSES_HOMEWORK_DATA', 1 from user_role_mapping where user_id in (select user_id from users where authorized_modules like '%HOMEWORK_MANAGEMENT%' and user_type <> 'STUDENT') group by institute_id, role_id;