CREATE INDEX user_id ON notification_status (user_id);
CREATE INDEX delivery_mode ON notification_status (delivery_mode);
CREATE INDEX composite_index_1 ON notification_status(institute_id, delivery_mode, notification_type);
CREATE INDEX notification_status_batch_id ON notification_status (batch_id);
--CREATE INDEX notification_status_1 ON notification_status (institute_id, academic_session_id, delivery_mode);?
CREATE INDEX composite_index_1 ON bell_notification_details(institute_id, entity_name);
CREATE INDEX composite_index_1 ON fee_payment_transactions(institute_id, academic_session_id);
CREATE INDEX composite_index_1 ON student_attendance_register(institute_id, academic_session_id, attendance_type);
CREATE INDEX composite_index_1 ON students(institute_id, final_status);
CREATE INDEX composite_index_1 ON student_academic_session_details(academic_session_id, standard_id);
CREATE INDEX notification_status_batch_id ON notification_status (batch_id);
CREATE INDEX student_fee_payments_institute_id ON student_fee_payments(institute_id);


CREATE INDEX composite_index_2 ON bell_notification_details(institute_id, user_id);

CREATE INDEX academic_session_id_index ON homework_details(academic_session_id);

CREATE INDEX homework_details_status ON homework_details(status);


CREATE INDEX composite_index_2 ON student_attendance_register(institute_id, academic_session_id, attendance_date);

CREATE TABLE `bell_notification_details_latest` (
  `institute_id` int(11) NOT NULL,
  `notification_id` varchar(36) NOT NULL,
  `user_id` varchar(36) NOT NULL,
  `title` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `entity_name` varchar(100) NOT NULL,
  `entity_id` varchar(36) DEFAULT NULL,
  `list_opened_on` timestamp NULL DEFAULT NULL,
  `clicked_on` timestamp NULL DEFAULT NULL,
  `meta_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`notification_id`),
  UNIQUE KEY `notification_id` (`notification_id`,`user_id`),
  CONSTRAINT `bell_notification_details_latest_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
);

CREATE INDEX user_index ON bell_notification_details_latest(user_id);
CREATE INDEX composite_index_1 ON bell_notification_details_latest(institute_id, entity_name);
CREATE INDEX composite_index_2 ON bell_notification_details_latest(institute_id, user_id);

--  KEY `user_id` (`user_id`),
--  KEY `composite_index_2` (`institute_id`,`user_id`),
--  KEY `composite_index_1` (`institute_id`,`entity_name`,`created_on`),


CREATE INDEX homework_details_status ON homework_details(status);


CREATE INDEX institute_session_status_index ON homework_details(institute_id, academic_session_id, status, created_timestamp);

alter table bell_notification_details drop index composite_index_1;
CREATE INDEX composite_index_1 ON bell_notification_details(institute_id, entity_name, created_on);

CREATE INDEX notification_status_composite_index_1 ON notification_status(delivery_mode, status, generation);

alter table staff_attendance_register drop index staff_attendance_register_attendance_date;
CREATE INDEX staff_attendance_register_attendance_date ON staff_attendance_register (attendance_date, institute_id);

CREATE INDEX institute_session_create_at ON remark_details(institute_id, academic_session_id, created_at);


CREATE INDEX composite_index_2 ON fee_payment_transactions(institute_id, academic_session_id, status);

CREATE INDEX composite_index_3 ON notification_status(institute_id, academic_session_id, delivery_mode, user_type, notification_type, batch_id, generation);

--select notification_status.batch_id, notification_status.batch_name, user_type, notification_type, batch_generation_time, status, delivered, notification_content, meta_data, credits_used, refunded_credits from notification_status join (select batch_id, min(generation) as batch_generation_time from notification_status where batch_id is not null and institute_id = 10340 and academic_session_id = 230 and delivery_mode = 'SMS' and user_type = 'STUDENT'  and notification_type in ('CUSTOM', 'RENEW_CREDENTIALS', 'STUDENT_BIRTHDAY', 'STUDENT_EARLY_DEPARTURE', 'STUDENT_ADMISSION', 'STUDENT_REGISTRATION', 'STUDENT_ATTENDANCE_STATUS', 'ADMIN_STAFF_ATTENDANCE') group by batch_id order by batch_generation_time desc limit 10 offset 0) as batch_notifications on notification_status.batch_id = batch_notifications.batch_id order by batch_notifications.batch_id