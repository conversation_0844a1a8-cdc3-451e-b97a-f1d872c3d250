select * from students where institute_id = 10130 and admission_number in ('19031', '13015', '13038', '13167', '14035', '14053', '14061', '17025', '18001', '21032', '21027', '21013', '13156', '18009', '21002', '16008', '16068', '16019', '17028', '16067', '15063', '14100', '19032', '19039', '13011', '13032', '13092', '13010', '13031', '13104', '13113', '13151', '13152', '13155', '14019', '14033', '14069', '14109', '14123', '14125', '13074', '19004', '13125', '13166', '14017', '14011', '14060', '14076', '14079', '14114', '15022', '15024', '14036', '14070', '14081', '14093', '14098', '14124', '14127', '14128', '15064', '16020', '17004', '17005', '17014', '14105', '13073', '16040', '17029', '17039', '17006', '17033', '17013', '16016', '17012', '17010', '17030', '17023', '16066', '16055', '18002', '18003', '18004', '18005', '18008', '18016', '18018', '18029', '20002');

update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '19031';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13015';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13038';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13167';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14035';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14053';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14061';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17025';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18001';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '21032';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '21027';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '21013';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13156';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18009';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '21002';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16008';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16068';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16019';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17028';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16067';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '15063';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14100';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '19032';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '19039';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13011';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13032';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13092';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13010';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13031';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13104';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13113';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13151';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13152';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13155';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14019';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14033';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14069';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14109';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14123';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14125';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13074';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '19004';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13125';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13166';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14017';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14011';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14060';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14076';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14079';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14114';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '15022';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '15024';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14036';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14070';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14081';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14093';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14098';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14124';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14127';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14128';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '15064';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16020';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17004';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17005';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17014';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '14105';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '13073';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16040';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17029';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17039';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17006';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17033';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17013';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16016';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17012';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17010';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17030';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '17023';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16066';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '16055';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18002';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18003';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18004';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18005';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18008';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18016';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18018';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '18029';
update students set primary_email = '<EMAIL>' where institute_id = 10130 and admission_number = '20002';