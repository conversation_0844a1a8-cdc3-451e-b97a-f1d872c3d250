ALTER TABLE attendance_types ADD COLUMN attendance_head_type VARCHAR(255) DEFAULT 'ATTENDANCE';

--TODO ADD PICKUP AND DROP ATTENDANCE TYPES (ONE TIME RUN)
insert into attendance_types (institute_id, academic_session_id, name, description, metadata, attendance_head_type) select institute_id, academic_session_id, 'Drop', 'Drop Route Attendance Type', null, 'TRANSPORT' from academic_session;
insert into attendance_types (institute_id, academic_session_id, name, description, metadata, attendance_head_type) select institute_id, academic_session_id, 'Pick Up', 'Pick Up Route Attendance Type', null, 'TRANSPORT' from academic_session;

select * from attendance_types where attendance_head_type = 'TRANSPORT';
select * from attendance_types;

