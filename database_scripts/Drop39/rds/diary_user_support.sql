alter table diary_remarks_category add column remark_user_type varchar(256) NOT NULL after category_name;

alter table standard_remarks add column remark_user_type varchar(256) NOT NULL;
alter table standard_remarks add column created_by varchar(36) NOT NULL;
alter table standard_remarks add column created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;
alter table standard_remarks add column updated_by varchar(36);
alter table standard_remarks add column updated_at timestamp DEFAULT CURRENT_TIMESTAMP;

alter table remark_details add column remark_user_type varchar(256) NOT NULL after date;
rename TABLE remark_student_mappings TO remark_user_mappings;
alter table remark_user_mappings change student_id user_id varchar(36) NOT NULL;
alter table remark_user_mappings add column user_type varchar(256) NOT NULL after user_id;

--update diary_remarks_category set remark_user_type = 'STUDENT';
--update standard_remarks set remark_user_type = 'STUDENT';
--update remark_details set remark_user_type = 'STUDENT';
--update remark_user_mappings set user_type = 'STUDENT';


alter table standard_remarks change updated_by updated_by varchar(36);

show create table remark_user_mappings;
ALTER TABLE remark_user_mappings DROP FOREIGN KEY remark_user_mappings_ibfk_2;