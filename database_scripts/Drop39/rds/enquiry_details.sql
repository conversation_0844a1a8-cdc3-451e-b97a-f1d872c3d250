CREATE TABLE IF NOT EXISTS enquiry_details (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    enquiry_id VARCHAR(36) NOT NULL PRIMARY KEY,
    tracking_id VARCHAR(100) NOT NULL,
    parent_name VA<PERSON>HA<PERSON>(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    child_name VA<PERSON>HA<PERSON>(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    phone_number VARCHAR(1024) NOT NULL,
    email VARCHAR(1024),
    class VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    message TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
    status VARCHAR(100) NOT NULL,
    is_successful TINYINT(1) NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_on TIMESTAMP NULL,
    accepted_by VARCHAR(36),
    accepted_on TIMESTAMP ,
    rejected_by VARCHAR(36),
    rejected_on TIMESTAMP,
    closed_by VARCHAR(36),
    closed_on TIMESTAMP,
    outcome TEXT,
    student_id VARCHAR(36),
    reason TEXT,
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
    FOREIGN KEY (academic_session_id) REFERENCES academic_session(academic_session_id),
    INDEX idx_tracking_id (tracking_id)
);