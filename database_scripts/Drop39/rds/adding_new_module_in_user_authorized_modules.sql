select entity_id from configuration where config_key = 'module_access' and config_value like '%PARENTS_APPOINTMENT%';
select * from users where institute_id in (select entity_id from configuration where config_key = 'module_access' and config_value like '%PARENTS_APPOINTMENT%') and user_type = 'STUDENT';
select authorized_modules from users where institute_id in (select entity_id from configuration where config_key = 'module_access' and config_value like '%PARENTS_APPOINTMENT%') and user_type = 'STUDENT';
update users set authorized_modules = JSON_ARRAY_APPEND(authorized_modules, '$', 'PARENTS_APPOINTMENT') where institute_id in (select entity_id from configuration where config_key = 'module_access' and config_value like '%PARENTS_APPOINTMENT%') and user_type = 'STUDENT';