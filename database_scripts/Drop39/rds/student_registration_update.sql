alter table student_registration add column institute_id int not null after institute_unique_code;
alter table student_registration add column assigned_amount double;
alter table student_registration add column paid_amount double;
alter table student_registration add column discount_amount double;
alter table student_registration add column transaction_mode varchar(36);
alter table student_registration add column transaction_date timestamp DEFAULT CURRENT_TIMESTAMP;
alter table student_registration add column transaction_reference varchar(512);
alter table student_registration add column transaction_added_at timestamp DEFAULT CURRENT_TIMESTAMP;
alter table student_registration add column transaction_added_by varchar(36);


CREATE INDEX institute_id ON student_registration (institute_id);

--update student_registration join institute on student_registration.institute_unique_code = institute.institute_unique_code set student_registration.institute_id = institute.institute_id;

CREATE TABLE IF NOT EXISTS student_registration_fee_assignment(
institute_id int NOT NULL,
registration_id varchar(36) NOT NULL,
fee_id varchar(36) NOT NULL,
fee_head_id int NOT NULL,
amount double,
PRIMARY KEY(institute_id, registration_id, fee_id, fee_head_id),
FOREIGN KEY (registration_id) REFERENCES student_registration(registration_id),
FOREIGN KEY (fee_id) REFERENCES fee_configuration(fee_id),
FOREIGN KEY (fee_head_id) REFERENCES fee_head_configuration(fee_head_id)
) DEFAULT CHARSET=latin1;
