CREATE TABLE IF NOT EXISTS visitor_details (
    institute_id INT NOT NULL,
    academic_session_id INT NOT NULL,
    visitor_id VARCHAR(36) PRIMARY KEY,
    visitor_name VARCHAR(1024) NOT NULL,
    date_of_birth TIMESTAMP DEFAULT NULL,
    gender VARCHAR(10) DEFAULT NULL,
    contact_number VARCHAR(15) NOT NULL,
    email VARCHAR(500) DEFAULT NULL,
    visiting_date_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    purpose_of_visit VARCHAR(2048) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    visitor_documents TEXT,
    transferred_from VARCHAR(36) DEFAULT NULL,
    transferred_by VARCHAR(36) DEFAULT NULL,
    status VARCHAR(36) DEFAULT 'PENDING',
    reason TEXT,
    comments TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    <PERSON><PERSON><PERSON>G<PERSON>Y (institute_id) REFERENCES institute(institute_id),
    <PERSON><PERSON>EIGN <PERSON>EY (academic_session_id) REFERENCES academic_session(academic_session_id)
);


ALTER TABLE visitor_details
ADD COLUMN visitor_status_updated_by VARCHAR(36) DEFAULT NULL;