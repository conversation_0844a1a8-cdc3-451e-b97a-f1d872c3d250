package com.embrate.cloud.core.lib.courses;

import com.embrate.cloud.core.api.homework.HomeworkDetails;
import com.embrate.cloud.core.api.lecture.StandardLectureDetails;
import com.embrate.cloud.core.api.timetable.*;
import com.embrate.cloud.core.utils.EMapUtils;
import com.embrate.cloud.dao.tier.homework.HomeworkDao;
import com.embrate.cloud.dao.tier.lecture.LectureDao;
import com.embrate.cloud.dao.tier.timetable.TimetableDao;
import com.lernen.cloud.core.api.course.*;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.dao.tier.course.CourseDao;
import com.lernen.cloud.dao.tier.examination.ExaminationDao;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.student.StudentSorter;

import java.util.*;

import static com.embrate.cloud.core.utils.courses.CourseUtils.*;

public class CourseManager {
	private static final Logger logger = LogManager.getLogger(CourseManager.class);

	private final CourseDao courseDao;
	private final InstituteManager instituteManager;
	private final UserPermissionManager userPermissionManager;
	private final ExaminationDao examinationDao;
	private final TimetableDao timetableDao;
	private final HomeworkDao homeworkDao;
	private final LectureDao lectureDao;

	public CourseManager(CourseDao courseDao, InstituteManager instituteManager, UserPermissionManager userPermissionManager, ExaminationDao examinationDao, HomeworkDao homeworkDao, TimetableDao timetableDao, LectureDao lectureDao) {
		this.courseDao = courseDao;
		this.instituteManager = instituteManager;
		this.userPermissionManager= userPermissionManager;
		this.examinationDao = examinationDao;
		this.homeworkDao = homeworkDao;
		this.timetableDao = timetableDao;
		this.lectureDao = lectureDao;
	}

	public Boolean addClassCourse(ClassCoursesPayload classCoursesPayload,int instituteId  , UUID userId, boolean skipAuth) {

		logger.info("Courses {}", classCoursesPayload);
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid Institute Id"));
		}
		if (!skipAuth) {
			if (userId == null) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
			}
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_CLASS_COURSES);
		}
		if (classCoursesPayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session id"));
		}
		if (classCoursesPayload.getStandardId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid  standard id"));
		}
		if ((classCoursesPayload == null) || CollectionUtils.isEmpty(classCoursesPayload.getCourses()) || (!valiateCourseInfo(classCoursesPayload))) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid class course payload"));
		}
		if (!instituteManager.validStandard(classCoursesPayload.getStandardId(), instituteId,
				classCoursesPayload.getAcademicSessionId())) {
			logger.error("Invalid standard " + classCoursesPayload.getStandardId() + " for institute " + instituteId
					+ " in course payload");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Standard does not belong to institute."));
		}

		List<Course> existingCourse = getClassCoursesByStandardId(instituteId, classCoursesPayload.getStandardId(), classCoursesPayload.getAcademicSessionId());
		checkDuplicateCourseAndSequence(classCoursesPayload, existingCourse);

		// Basic key generation which is equal to course name
		for (final Course course : classCoursesPayload.getCourses()) {
			course.setCourseKey(UUID.randomUUID().toString());
		}
		return courseDao.addClassCourses(classCoursesPayload);

	}

	private void checkDuplicateCourseAndSequence(ClassCoursesPayload classCoursesPayload, List<Course> existingCourses){
		List <String> courses= new ArrayList<>();
		List <Integer> sequences= new ArrayList<>();
		for(final Course course : classCoursesPayload.getCourses()){
			courses.add(course.getCourseName().toLowerCase());
			if(course.getSequence()!=null) {
				sequences.add(course.getSequence());
			}
		}
		if(!CollectionUtils.isEmpty(existingCourses)) {
			for (final Course existingCourse : existingCourses) {
				courses.add(existingCourse.getCourseName().toLowerCase());
				if(existingCourse.getSequence()!=null) {
					sequences.add(existingCourse.getSequence());
				}
			}
		}
		Set<String> uniqueCourse = new HashSet<>(courses);
		Set<Integer> uniqueSequences = new HashSet<>(sequences);
		if( uniqueCourse.size() != courses.size()){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Duplicate course name are not allowed"));
		}
		if( uniqueSequences.size() != sequences.size()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Duplicate sequences are not allowed"));
		}
	}

	private boolean valiateCourseInfo(ClassCoursesPayload classCoursesPayload) {
		for (final Course course : classCoursesPayload.getCourses()) {
			if (StringUtils.isBlank(course.getCourseName())) {
				logger.error("Course name cannot be blank");
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course name cannot be blank"));
			}

			if (course.getCourseType() == null) {
				logger.error("Course type is not valid");
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course type is not valid"));
			}
		}
		return true;
	}

	public List<ClassCourses> getClassCoursesByInstitute(int instituteId, int academicSessionId, List<UUID> standardIdsList) {
		return courseDao.getClassCoursesByInstitute(instituteId, academicSessionId, standardIdsList);
	}


public List<ClassCourses> getStandardCourses(int instituteId, int academicSessionId) {
	List<Standard> standardList = instituteManager.getInstituteStandardList(instituteId);
	List<ClassCourses> classCoursesList=courseDao.getClassCoursesByInstitute(instituteId, academicSessionId, null);

	Map<UUID, ClassCourses> classCourseMap = EMapUtils.getMap(classCoursesList, new EMapUtils.MapFunction<ClassCourses, UUID, ClassCourses>() {
		@Override
		public UUID getKey(ClassCourses entry) {
			return entry.getStandard().getStandardId();
		}
		@Override
		public ClassCourses getValue(ClassCourses entry) {
			return entry;
		}
	});

	List<ClassCourses> classCourse = new ArrayList<>();
	for(Standard standard : standardList) {
		if(classCourseMap.containsKey(standard.getStandardId())) {
			classCourse.add(classCourseMap.get(standard.getStandardId()));
			continue;
		}
		classCourse.add(new ClassCourses(standard.getAcademicSessionId(), standard, null));
	}
	return classCourse;
}

	public boolean updateClassCourses(ClassCoursesPayload classCoursesPayload,int instituteId, int academicSessionId, UUID userId){

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if(userId == null){
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_CLASS_COURSES);

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		if (!valiateCourseInfo(classCoursesPayload)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid class course payload"));
		}
		if (!instituteManager.validStandard(classCoursesPayload.getStandardId(), instituteId,
				classCoursesPayload.getAcademicSessionId())) {
			logger.error("Invalid standard " + classCoursesPayload.getStandardId() + " for institute " + instituteId
					+ " in course payload");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Standard does not belong to institute."));
		}
		checkDuplicateCourseAndSequence(classCoursesPayload, null);
		return courseDao.updateClassStandardCourses(classCoursesPayload);
	}

	public List<Course> getClassCoursesByStandardId(int instituteId, UUID standardID, int academicSessionId) {
		return getClassCoursesByStandardId(instituteId, standardID, academicSessionId, null, null, null);
	}

	public List<Course> getClassCoursesByStandardId(int instituteId, UUID standardID, int academicSessionId, Boolean showCoursesWithMandatoryFlag, Set<UUID>  courseIdSet, List<CourseType> courseTypes) {
		return sortCoursesBySequence(courseDao.getClassCoursesByStandardId(instituteId, standardID, academicSessionId, showCoursesWithMandatoryFlag, courseIdSet, courseTypes));
	}

	private List<Course> sortCoursesBySequence(List<Course> courseList) {
		if(CollectionUtils.isEmpty(courseList)) {
			return new ArrayList<>();
		}
		Collections.sort(courseList);
		return courseList;
	}

public boolean deleteClassCourses(int instituteId, UUID standardId, int academicSessionId, UUID userId, Set<UUID> courseIdSet) {
	if (instituteId <= 0) {
		throw new ApplicationException(
				new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid Institute Id"));
	}
	if(userId == null){
		throw new ApplicationException
				(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
	}
	userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_CLASS_COURSES);
	if (academicSessionId <= 0) {
		throw new ApplicationException
				(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
	}
	if(CollectionUtils.isEmpty(courseIdSet)) {
		throw new ApplicationException
				(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Please select atleast one course to delete"));
	}
//	    TODO : https://embrate.atlassian.net/browse/PD-2543
		return courseDao.deleteStandardCourses(instituteId, standardId, academicSessionId,courseIdSet);
	}

	public boolean assignCourses(int instituteId, int academicSessionId, UUID userId, StudentCoursesPayload studentCoursesPayload, boolean skipAuth) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid Institute Id"));
		}
		if (!skipAuth) {
			if (userId == null) {
				throw new ApplicationException
						(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
			}
			// validation for assigning optional courses
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.OPTIONAL_COURSE_ASSIGNMENT);
		}
		if (academicSessionId <= 0) {
			throw new ApplicationException
					(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}
		return courseDao.assignCourses(academicSessionId, studentCoursesPayload);
	}

	public List<Course> getStudentAssignedCourses(int instituteId, UUID studentId, int academicSessionId) {
		return sortCoursesBySequence(courseDao.getStudentAssignedCourses(instituteId, studentId, academicSessionId));
	}

	public List<CourseStudents> getStudentsCoursesAssignmentDetailsByStandardId(int academicSessionId, UUID standardId) {
		if(academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic Session id"));
		}
		if(standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "invalid standard id"));
		}
		return courseDao.getStudentsCoursesAssignmentDetailsByStandardId(academicSessionId, standardId);
	}

	public Map<UUID, List<Course>> getClassStudentsAssignedCourses(int instituteId, int academicSessionId,
			UUID standardId) {
		return sortCoursesMapBySequence(courseDao.getClassStudentsAssignedCourses(instituteId, academicSessionId, standardId));
	}

	private Map<UUID, List<Course>> sortCoursesMapBySequence(Map<UUID, List<Course>> courseMap) {
		if(CollectionUtils.isEmpty(courseMap)) {
			return new HashMap<>();
		}
		for(Map.Entry<UUID, List<Course>> entry : courseMap.entrySet()) {
			entry.setValue(sortCoursesBySequence(entry.getValue()));
		}
		return courseMap;
	}

	public List<Course> getClassCoursesByStudentId(int instituteId, UUID studentId, int academicSessionId) {
		return sortCoursesBySequence(courseDao.getClassCoursesByStudentId(instituteId, studentId, academicSessionId));
	}

	public CourseStudents getStudentsByCourse(UUID courseId) {
		return courseDao.getStudentsByCourse(courseId);
	}

	public List<CourseStudents> getStudentsByCourse(Set<UUID> courseIdSet) {
		if(CollectionUtils.isEmpty(courseIdSet)) {
			return null;
		}
		return courseDao.getStudentsByCourse(courseIdSet);
	}

	public Course getOptionalCourseDetailsByCourseNameStandardId(int academicSession, String courseName, UUID standardId) {
		if (StringUtils.isBlank(courseName)) {
			logger.error("Course name cannot be blank");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course name cannot be blank"));
		}
		if (academicSession <= 0) {
			logger.error("Invalid academic session");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session"));
		}
		if (standardId == null) {
			logger.error("standardId cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "standardId cannot be null"));
		}
		return courseDao.getOptionalCourseDetailsByCourseNameStandardId(academicSession, courseName, standardId);
	}
	
	public List<Course> getOptionalCourseDetailsByCourseNameStandardId(int academicSession, List<String> courseNameList, UUID standardId) {
		if (CollectionUtils.isEmpty(courseNameList)) {
			logger.error("Course name list cannot be blank");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course name cannot be blank"));
		}
		if (academicSession <= 0) {
			logger.error("Invalid academic session");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session"));
		}
		if (standardId == null) {
			logger.error("standardId cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "standardId cannot be null"));
		}
		return sortCoursesBySequence(courseDao.getOptionalCourseDetailsByCourseNameStandardId(academicSession, courseNameList, standardId));
	}

	public List<Course> getOptionalCoursesByStandardId(int academicSession, UUID standardId) {
		if (academicSession <= 0) {
			logger.error("Invalid academic session");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session"));
		}
		if (standardId == null) {
			logger.error("standardId cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "standardId cannot be null"));
		}
		return sortCoursesBySequence(courseDao.getOptionalCoursesByStandardId(academicSession, standardId));
	}

	public boolean assignBulkCourses(int instituteId, int academicSessionId,
			BulkStudentCoursesPayload bulkStudentCoursesPayload) {
		
		if(bulkStudentCoursesPayload == null) {
			logger.error("Student payload cannot be null.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Student payload cannot be null."));
		}
		
		if (bulkStudentCoursesPayload.getInstituteId() <= 0) {
			logger.error("Invalid institute id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (bulkStudentCoursesPayload.getAcademicSessionId() <= 0) {
			logger.error("Invalid academic session id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session id."));
		}
		
		if (CollectionUtils.isEmpty(bulkStudentCoursesPayload.getCourseIds())) {
			logger.error("Course name list cannot be blank");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course name cannot be blank"));
		}
		
		if (CollectionUtils.isEmpty(bulkStudentCoursesPayload.getStudentIds())) {
			logger.error("Student list cannot be blank");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Student list cannot be blank"));
		}

		//studentId vs list of courseIds
		Map<UUID, Set<UUID>> studentCourseIds = new HashMap<>();
		for(UUID studentId : bulkStudentCoursesPayload.getStudentIds()) {
			List<UUID> assignedCourseUUID = new ArrayList<UUID>();
			List<Course> courseList = getStudentAssignedCourses(instituteId, studentId, academicSessionId);
			for(Course course : courseList) {
				assignedCourseUUID.add(course.getCourseId());
			}
			bulkStudentCoursesPayload.getCourseIds().removeAll(assignedCourseUUID);
			studentCourseIds.put(studentId, new HashSet<>(bulkStudentCoursesPayload.getCourseIds()));
		}
		
		return courseDao.assignBulkCourses(instituteId, academicSessionId, studentCourseIds);
	}

	public boolean studentBulkCoursesAssignment(int instituteId, int academicSessionId,
			BulkCourseAssignmentPayload bulkCourseAssignmentPayload) {
		
		if(bulkCourseAssignmentPayload == null) {
			logger.error("Student payload cannot be null.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Student payload cannot be null."));
		}
		
		if (bulkCourseAssignmentPayload.getInstituteId() <= 0) {
			logger.error("Invalid institute id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (bulkCourseAssignmentPayload.getAcademicSessionId() <= 0) {
			logger.error("Invalid academic session id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session id."));
		}
		
		if (bulkCourseAssignmentPayload.getCourseId() == null) {
			logger.error("Course cannot be blank");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course name cannot be blank"));
		}
		
		if (CollectionUtils.isEmpty(bulkCourseAssignmentPayload.getAssignCourseStudentList())
				&& CollectionUtils.isEmpty(bulkCourseAssignmentPayload.getRemoveCourseStudentList())) {
			logger.error("Student list cannot be blank");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Student list cannot be blank"));
		}
		
		CourseStudents courseStudents = getStudentsByCourse(bulkCourseAssignmentPayload.getCourseId());
		
		bulkCourseAssignmentPayload.getAssignCourseStudentList().removeAll(courseStudents.getStudents());
		bulkCourseAssignmentPayload.getRemoveCourseStudentList().retainAll(courseStudents.getStudents());
		
		return courseDao.studentBulkCoursesAssignment(instituteId, academicSessionId, bulkCourseAssignmentPayload);
	}

	public List<CourseStudentAssignment> getCourseStudentAssignmentList(int instituteId, int academicSessionId,
															  Set<UUID> courseIdSet, UUID standardId, Integer sectionId) {

		if (instituteId <= 0) {
			logger.error("Invalid institute id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid academic session id."));
		}

		if (CollectionUtils.isEmpty(courseIdSet)) {
			logger.error("Course cannot be null.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Course Id cannot be null"));
		}

		if (standardId == null) {
			logger.error("Standard cannot be null.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Standard Id cannot be null"));
		}

		List<CourseStudentAssignment> courseStudentAssignmentList = courseDao
				.getCourseStudentAssignmentList(instituteId, academicSessionId, courseIdSet, standardId, sectionId);

		if(CollectionUtils.isEmpty(courseStudentAssignmentList)) {
			return null;
		}

		for(CourseStudentAssignment courseStudentAssignment : courseStudentAssignmentList) {
			Collections.sort(courseStudentAssignment.getStudentCourseAssignmentDetailsList(),
					new Comparator<StudentCourseAssignmentDetails>() {
						@Override
						public int compare(StudentCourseAssignmentDetails s1, StudentCourseAssignmentDetails s2) {
							return Boolean.compare(s2.isCourseAssign(), s1.isCourseAssign());
						}
					});
		}
		return courseStudentAssignmentList;

	}

	public List<StudentCourses> getStudentCourses(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIds) {
		return getStudentCourses(instituteId, academicSessionId, standardId, sectionIds, null);
	}
	public List<StudentCourses> getStudentCourses(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIds, Set<UUID> studentIdSet) {

		if (instituteId <= 0) {
			logger.error("Invalid institute id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Standard Id cannot be null"));
		}

		List<StudentCourses> studentCoursesList = courseDao.getStudentCourses(instituteId,academicSessionId,standardId,sectionIds, studentIdSet);
		Collections.sort(studentCoursesList);
		return studentCoursesList;
	}

	public List<StudentCoursesDetailsLite> getStudentCoursesDetails(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIds) {

		if (instituteId <= 0) {
			logger.error("Invalid institute id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Standard Id cannot be null"));
		}

		List<StudentCoursesDetailsLite> studentCoursesList = courseDao.getStudentCourseData(instituteId, academicSessionId, standardId, sectionIds, null);
    
		if(CollectionUtils.isEmpty(studentCoursesList)){
			return new ArrayList<>();
		}
    	StudentSorter.sortStudents(studentCoursesList,StudentSortingParameters.STANDARD,
    		new StudentSorter.StudentKeyExtractor<StudentCoursesDetailsLite>() {

        	@Override
        	public String getAdmissionNumber(StudentCoursesDetailsLite student) {
            	return student.getStudentInfoDetails().getAdmissionNumber();
        	}

        	@Override
        	public String getName(StudentCoursesDetailsLite student) {
            	return student.getStudentInfoDetails() != null ? student.getStudentInfoDetails().getName() : "";
        	}

        	@Override
        	public String getRollNumber(StudentCoursesDetailsLite student) {
            	return student.getStudentInfoDetails() != null ?
                   student.getStudentInfoDetails().getRollNumber() : "";
        	}
			@Override
        	public int getStandardLevel(StudentCoursesDetailsLite student) {
            	return student.getStudentInfoDetails() != null
                    ? student.getStudentInfoDetails().getStandardLevel()
                    : 0;
        	}

        	@Override
        	public String getSectionName(StudentCoursesDetailsLite student) {
            	return student.getStudentInfoDetails() != null?
                   student.getStudentInfoDetails().getSectionName() : "";
        	}

        	@Override
        	public Integer getAdmissionDate(StudentCoursesDetailsLite student) {
            	return student.getStudentInfoDetails() != null ? student.getStudentInfoDetails().getAdmissionDate() : null;
        	}
    	}
	);

	
    	return studentCoursesList;
	}
	
	public boolean deleteOptionalCourses(int academicSessionId, UUID standardId, UUID studentId) {
		return courseDao.deleteOptionalCourses(academicSessionId, standardId, studentId);
	}


	public Course getCourseDetails(UUID courseId) {
		if (courseId == null) {
			logger.error("course id cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "course id cannot be null"));
		}
		return courseDao.getCourseDetails(courseId);
	}

	public List<ClassCourseRowDetails> getClassCourseRowDetails(UUID courseId) {
		if (courseId == null) {
			logger.error("course id cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "course id cannot be null"));
		}
		return courseDao.getClassCourseRowDetails(courseId);
	}

    public boolean migrateOptionalCoursesDueToClassChangeOrPromotion(int instituteId, int currentSessionId, UUID currentStandardId, Integer currentSectionId,
																	 Set<UUID> studentIdSet, int nextSessionId, UUID newStandardId, UUID userId) {
		if (instituteId <= 0) {
			logger.error("institute id cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "institute id cannot be null"));
		}
		if (currentSessionId <= 0) {
			logger.error("current session id cannot be less than equal to 0");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "current session id cannot be less than equal to 0"));
		}
		if (currentStandardId == null) {
			logger.error("current standard id cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "current standard id cannot be null"));
		}
		if (CollectionUtils.isEmpty(studentIdSet)) {
			logger.error("student set cannot be empty");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "student set cannot be empty"));
		}
		if (nextSessionId <= 0) {
			logger.error("next session id cannot be less than equal to 0");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "next session id cannot be less than equal to 0"));
		}
		if (newStandardId == null) {
			logger.error("promotion standard id cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "promotion standard id cannot be null"));
		}
		if (userId == null) {
			logger.error("user id cannot be null");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "user id cannot be null"));
		}
		List<StudentCourses> studentCoursesList = getStudentCourses(instituteId, currentSessionId, currentStandardId,
				currentSectionId == null || currentSectionId <= 0 ? null : new HashSet<>(Collections.singletonList(currentSectionId)), studentIdSet);
		Map<UUID, String> currentSessionStudentAssignCourseNameMap = new HashMap<>();
		for(StudentCourses studentCourses : studentCoursesList) {
			List<Course> courseList = studentCourses.getCourses();
			for(Course course : courseList) {
				//if course is mandatory, skip
				if (course == null || course.isMandatory()) {
					continue;
				}
				currentSessionStudentAssignCourseNameMap.put(studentCourses.getStudentDetailedRow().getStudentId(), course.getCourseName().toLowerCase());
			}
		}

		if(CollectionUtils.isEmpty(currentSessionStudentAssignCourseNameMap)) {
			logger.info("No optional course assign in session {}, -----skipping course assignment-----", currentSessionId);
			return true;
		}

		logger.info("Optional courses assign in session {}, are {}", currentSessionId, currentSessionStudentAssignCourseNameMap);

		List<Course> nextSessionOptionalCourseList = getOptionalCoursesByStandardId(nextSessionId, newStandardId);
//		courseName, courseId
		Map<String, UUID> nextSessionOptionalStudentIdCourseIdMap = new HashMap<>();
		for(Course course : nextSessionOptionalCourseList) {
			//if course is mandatory, skip
			if(course == null || course.isMandatory()) {
				continue;
			}
			//if course is not present in last session optional courses, skip
			if(!currentSessionStudentAssignCourseNameMap.containsValue(course.getCourseName().toLowerCase())) {
				continue;
			}
			nextSessionOptionalStudentIdCourseIdMap.put(course.getCourseName().toLowerCase(), course.getCourseId());
		}

		if(CollectionUtils.isEmpty(nextSessionOptionalStudentIdCourseIdMap)) {
			logger.info("No optional course assign found in next session {}, -----skipping course assignment-----", nextSessionId);
			return true;
		}

		logger.info("Optional courses assign to student, in session {}, are {}", nextSessionId, nextSessionOptionalStudentIdCourseIdMap);

		Map<UUID, Set<UUID>> studentIdCourseIds = new HashMap<>();
		for(Map.Entry<UUID, String> currentSessionStudentAssignCourseNameEntry : currentSessionStudentAssignCourseNameMap.entrySet()) {
			UUID studentId = currentSessionStudentAssignCourseNameEntry.getKey();
			String courseName = currentSessionStudentAssignCourseNameEntry.getValue();
			UUID courseId = nextSessionOptionalStudentIdCourseIdMap.get(courseName);
			if(studentId == null || courseId == null) {
				continue;
			}
			if(!studentIdCourseIds.containsKey(studentId)) {
				studentIdCourseIds.put(studentId, new HashSet<>());
			}
			studentIdCourseIds.get(studentId).add(courseId);
		}

		return courseDao.assignBulkCourses(instituteId, nextSessionId, studentIdCourseIds);
    }

	public Map<UUID, List<String>> getStandardCourseIdWithError(int academicSessionId, int instituteId, UUID standardId) {

//		TODO : https://embrate.atlassian.net/jira/software/projects/PD/boards/1?selectedIssue=PD-3825

		List<Course> classCourseLists = getClassCoursesByStandardId(instituteId, standardId,
				academicSessionId, null, null, null);
		List<ExamCoursesData> examCoursesDataList = examinationDao.getExamCourses(academicSessionId, standardId);
		ExamCoursesData.sortExamCoursesDataByExamStartDate(examCoursesDataList);
		Map<Integer, List<DatesheetDetailRow>> dateSheetMap = examinationDao.getSortedDatesheetDetailRow(instituteId, academicSessionId, standardId, null, null);
		List<DatesheetDetailRow> datesheetDetailsRowList = getDateSheetDetailsList(dateSheetMap);
		List<HomeworkDetails> homeworkDetailsList = homeworkDao.getHomeworkDetailsByFilters(instituteId,
				academicSessionId, standardId, null, null, null, null, null, null);
		List<MarksFeedData> marksFeedDataList = examinationDao.getMarksByStandardId(academicSessionId, Arrays.asList(standardId));
		List<CoursesActivityDetails> coursesActivityDetailsList = timetableDao.getStaffEntityAssignment(instituteId,
				academicSessionId, standardId, null);
		List<StudentCourses> studentCoursesList = getStudentCourses(instituteId, academicSessionId, standardId, null, null);
		List<TimetableDetails> timetableDetailsList = timetableDao
				.getTimetableDetailsWithoutShiftId(instituteId, academicSessionId, standardId, null);
		StandardLectureDetails standardLectureDetails = lectureDao.getLectureDetailsByStandard(instituteId,
				academicSessionId, standardId, 0, null);
		Map<UUID, List<String>> coursesWithError = new HashMap<>();
		Map<UUID, String> examIdWithExamNameMap = new LinkedHashMap<>();
		Map<UUID, List<UUID>> examIdwithCourseIdsMap = getExamIdAndCourseIds(examCoursesDataList, examIdWithExamNameMap);

		for (Course course : classCourseLists) {
			List<String> errorStr = new ArrayList<>();

			checkMarksFeed(course, marksFeedDataList, examIdWithExamNameMap, errorStr);
			checkDateSheetError(course, datesheetDetailsRowList, errorStr);
			checkExamError(course, examCoursesDataList, examIdwithCourseIdsMap, errorStr);
			checkStaffAssignmentError(course, coursesActivityDetailsList, errorStr);
			checkTimetableCourseUsage(course, timetableDetailsList, errorStr);
			checkHomeworkError(course, homeworkDetailsList, errorStr);
			checkLectureError(course, standardLectureDetails, errorStr);
			checkStudentAssignedCourse(course, studentCoursesList, errorStr);

			coursesWithError.put(course.getCourseId(), errorStr);
		}
		return coursesWithError;
	}
}
