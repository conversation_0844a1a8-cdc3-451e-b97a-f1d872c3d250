package com.embrate.cloud.core.lib.courses;

import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.embrate.cloud.core.api.report.layout.MergeArea;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import com.embrate.cloud.core.utils.EListUtils;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.StudentCoursesDetailsLite;
import com.lernen.cloud.core.api.course.StudentInfoDetails;
import com.lernen.cloud.core.api.examination.report.CourseReportDetailsStore;
import com.lernen.cloud.core.api.examination.report.CourseReportPayload;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportHorizontalTextAlignment;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;

import org.apache.commons.collections.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Report builder for Student Optional Course Report
 */
public class OptionalCourseDetailsReportBuilder extends ReportBuilder<CourseReportPayload, CourseReportDetailsStore> {

    private final CourseManager courseManager;
    private final InstituteManager instituteManager;

    private static String SERIAL_NUMBER = "S.No";
    private static String ADMISSION_NUMBER = "Admission No.";
    private static String ROLL_NUMBER = "Roll No";
    private static String STUDENT_NAME = "Name";
    private static String REPORT_NAME = "Report Name";
    private static String CLASS_NAME = "Class Name";
    private static String SUBJECT = "Subject";
    private static String GENERATED_AT = "Generated At";
    private static String REPORT_HEADING = "Student Optional Course Report";
    private static String SECTION = "Section";
    private static String TOTAL = "Total";
    private static String REPORT_ID = "STUDENT_OPTIONAL_COURSE_REPORT";
    private static String REPORT_DISPLAY_NAME = "Student Optional Course Report";
    private static String DESCRIPTION = "Optional course assignments for students";


    public OptionalCourseDetailsReportBuilder(UserPermissionManager userPermissionManager,
                                              InstituteManager instituteManager,
                                              CourseManager courseManager) {
        super(userPermissionManager, instituteManager);
        this.instituteManager = instituteManager;
        this.courseManager = courseManager;
    }

    @Override
    public ReportMetadata getReportMetadata() {
        return new ReportMetadata(
                REPORT_ID,
                REPORT_DISPLAY_NAME,
                Module.COURSES,
                DESCRIPTION
        );
    }

    @Override
    public List<ReportHeaderAttribute> getStaticHeaders() {
        return Arrays.asList(
                ReportHeaderAttribute.STUDENT_ADMISSION_NUMBER,
                ReportHeaderAttribute.STUDENT_NAME
        );
    }

    @Override
    public CourseReportDetailsStore getStore(CourseReportPayload payload) {
        List<StudentCoursesDetailsLite> studentCoursesList = courseManager.getStudentCoursesDetails(
                payload.getInstituteId(),
                payload.getAcademicSessionId(),
                payload.getStandardId(),
                payload.getSectionIds()
        );

        Standard standard = instituteManager.getStandardByStandardId(
                payload.getInstituteId(),
                payload.getAcademicSessionId(),
                payload.getStandardId()
        );

        String className = StandardUtils.getClassName(payload.getSectionIds(), standard);

        List<Course> optionalCourses = courseManager.getClassCoursesByStandardId(
                payload.getInstituteId(),
                payload.getStandardId(),
                payload.getAcademicSessionId(),
                false,
                payload.getCourseId(),
                null
        );

        Map<UUID, List<UUID>> studentOptionalCourseMapping =
                buildStudentOptionalCourseMapping(studentCoursesList, optionalCourses);

        return new CourseReportDetailsStore(
                className,
                studentCoursesList,
                optionalCourses,
                studentOptionalCourseMapping,
                Collections.emptyMap()
        );
    }

    @Override
    public String getDownloadReportName(CourseReportPayload payload, CourseReportDetailsStore store) {
        return "Student_Optional_Course_Report";
    }

    @Override
    public ReportHeader getHeader(CourseReportPayload payload, CourseReportDetailsStore store) {
        List<Course> optionalCourses = store.getOptionalCourses();

        List<ReportRow> rows = new ArrayList<>();
        List<MergeArea> mergeAreas = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MMM/yyyy HH:mm:ss");
        String formattedDateTime = java.time.LocalDateTime.now().format(formatter);

        boolean includeSection = !(CollectionUtils.isEmpty(payload.getSectionIds()));

        // Build dynamic base header
        List<String> headerCols = new ArrayList<>(Arrays.asList(
                SERIAL_NUMBER, ADMISSION_NUMBER, ROLL_NUMBER, STUDENT_NAME
        ));
        if (includeSection) {
            headerCols.add(SECTION);
        }

        // Add optional course headers
        for (Course course : optionalCourses) {
            headerCols.add(course.getCourseName());
        }

        int totalCols = headerCols.size();
        int rowIndex = 0;

        addHeaderRow(rows, mergeAreas, rowIndex++, REPORT_NAME, REPORT_HEADING, totalCols, 2);
        addHeaderRow(rows, mergeAreas, rowIndex++, CLASS_NAME, store.getClassName(), totalCols, 2);
        addHeaderRow(rows, mergeAreas, rowIndex++, GENERATED_AT, formattedDateTime, totalCols, 2);
        addHeaderRow(rows, mergeAreas, rowIndex++, SUBJECT, getOptionalCourses(optionalCourses), totalCols, 2);

        // Column headers
        List<ReportCellDetails> headerRow = new ArrayList<>();
        for (String col : headerCols) {
            headerRow.add(createCell(col, true));
        }

        rows.add(new ReportRow(headerRow));
        return new ReportHeader(rows, mergeAreas);
    }

    @Override
    public ReportBody getBody(CourseReportPayload payload, CourseReportDetailsStore store) {
        List<Course> optionalCourses = store.getOptionalCourses();
        Map<UUID, List<UUID>> studentOptionalCourseMapping = store.getStudentOptionalCourseMapping();

        List<ReportRow> bodyReportRows = new ArrayList<>();
        Map<UUID, Integer> courseStudentCounts = new HashMap<>();
        int count = 1;

        boolean includeSection = !(CollectionUtils.isEmpty(payload.getSectionIds()));

        for (StudentCoursesDetailsLite studentCourses : store.getStudentCoursesList()) {
            StudentInfoDetails student = studentCourses.getStudentInfoDetails();
            List<UUID> studentOptionalCourses = studentOptionalCourseMapping.get(student.getStudentId());

            if (CollectionUtils.isEmpty(studentOptionalCourses)) {
                continue;
            }

            ReportRow row = new ReportRow();

            // Basic columns
            addColumns(row, ReportHorizontalTextAlignment.CENTER,
                    String.valueOf(count++),
                    student.getAdmissionNumber(),
                    student.getRollNumber(),
                    student.getName()
            );

            if (includeSection) {
                addColumns(row, ReportHorizontalTextAlignment.CENTER,
                        student.getSectionName()
                );
            }

            // Optional courses
            for (Course optionalCourse : optionalCourses) {
                if (studentOptionalCourses.contains(optionalCourse.getCourseId())) {
                    addColumns(row, ReportHorizontalTextAlignment.CENTER, "Yes");
                    courseStudentCounts.put(
                            optionalCourse.getCourseId(),
                            courseStudentCounts.getOrDefault(optionalCourse.getCourseId(), 0) + 1
                    );
                } else {
                    addColumns(row, ReportHorizontalTextAlignment.CENTER, "");
                }
            }

            bodyReportRows.add(row);
        }

        addTotalsRow(bodyReportRows, optionalCourses, courseStudentCounts, includeSection);
        return new ReportBody(bodyReportRows, Collections.emptyList());
    }

    private void addTotalsRow(List<ReportRow> bodyReportRows,
                              List<Course> optionalCourses,
                              Map<UUID, Integer> courseStudentCounts,
                              boolean includeSection) {

        ReportRow totalsRow = new ReportRow();

        // Base totals row cells
        if (includeSection) {
            addColumns(totalsRow, ReportHorizontalTextAlignment.CENTER, TOTAL, "", "", "", "");
        } else {
            addColumns(totalsRow, ReportHorizontalTextAlignment.CENTER, TOTAL, "", "", "");
        }

        // Add counts for each optional course
        for (Course optionalCourse : optionalCourses) {
            int count = courseStudentCounts.getOrDefault(optionalCourse.getCourseId(), 0);
            addColumns(totalsRow, ReportHorizontalTextAlignment.CENTER, String.valueOf(count));
        }

        bodyReportRows.add(totalsRow);
    }

    private void addHeaderRow(List<ReportRow> rows, List<MergeArea> mergeAreas,
                              int rowIndex, String key, String value, int totalCols, int keyCols) {

        List<ReportCellDetails> row = new ArrayList<>();

        row.add(createCell(key, true));
        addEmptyCells(row, keyCols - 1);

        row.add(createCell(value, false));
        addEmptyCells(row, totalCols - keyCols - 1);

        addMerge(mergeAreas, rowIndex, rowIndex, 0, keyCols - 1);
        addMerge(mergeAreas, rowIndex, rowIndex, keyCols, totalCols - 1);

        rows.add(new ReportRow(row));
    }

    private Map<UUID, List<UUID>> buildStudentOptionalCourseMapping(
            List<StudentCoursesDetailsLite> studentCoursesList,
            List<Course> optionalCourses) {

        Map<UUID, List<UUID>> mapping = new HashMap<>();
        for (StudentCoursesDetailsLite studentCourses : studentCoursesList) {
            List<UUID> studentOptionalCourses =
                    getStudentOptionalCourseIds(studentCourses.getCourses(), optionalCourses);
            mapping.put(studentCourses.getStudentInfoDetails().getStudentId(), studentOptionalCourses);
        }
        return mapping;
    }

    private List<UUID> getStudentOptionalCourseIds(List<Course> studentCourses, List<Course> optionalCourses) {
        List<UUID> courseIds = new ArrayList<>();
        Set<UUID> studentCourseIds = new HashSet<>();

        for (Course course : studentCourses) {
            studentCourseIds.add(course.getCourseId());
        }

        for (Course optionalCourse : optionalCourses) {
            if (studentCourseIds.contains(optionalCourse.getCourseId())) {
                courseIds.add(optionalCourse.getCourseId());
            }
        }
        return courseIds;
    }

    private String getOptionalCourses(List<Course> optionalCourses) {
        return EListUtils.convertListToString(optionalCourses, new EListUtils.ListToStringFunction<Course>() {
            @Override
            public String getString(Course course) {
                return course.getCourseName();
            }
        });
    }

    @Override
    public ReportConfigs getConfigs(CourseReportPayload payload, CourseReportDetailsStore store) {
        return null;
    }

    @Override
    public void verifyAuthorisation(CourseReportPayload payload) {
        userPermissionManager.verifyAuthorisation(payload.getInstituteId(), payload.getUserId(), AuthorisationRequiredAction.GENERATE_STUDENT_OPTIONAL_COURSE_DETAILS_REPORT);
    }

    @Override
    public void payloadVerification(CourseReportPayload payload) {
        if (payload.getStandardId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Standard is required")
            );
        }
    }
}
