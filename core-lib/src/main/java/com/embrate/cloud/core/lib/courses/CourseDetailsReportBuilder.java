package com.embrate.cloud.core.lib.courses;

import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.embrate.cloud.core.api.report.layout.MergeArea;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseCountDetails;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.course.StudentCoursesDetailsLite;
import com.lernen.cloud.core.api.course.StudentInfoDetails;
import com.lernen.cloud.core.api.examination.report.CourseReportDetailsStore;
import com.lernen.cloud.core.api.examination.report.CourseReportPayload;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportHorizontalTextAlignment;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.StudentLite;

import java.time.format.DateTimeFormatter;
import java.util.*;

import org.apache.commons.collections.CollectionUtils;
public class CourseDetailsReportBuilder extends ReportBuilder<CourseReportPayload, CourseReportDetailsStore> {

    private final CourseManager courseManager;
    private final InstituteManager instituteManager;

    private static String SERIAL_NUMBER = "S.No.";
    private static String ADMISSION_NUMBER = "Admission No.";
    private static String ROLL_NUMBER = "Roll No";
    private static String STUDENT_NAME = "Name";
    private static String FATHER_NAME = "Father's Name";
    private static String REPORT_NAME = "Report Name";
    private static String CLASS_NAME = "Class Name";
    private static String GENERATED_AT = "Generated At";
    private static String REPORT_HEADING = "Student Course Details Report";
    private static String SECTION = "Section";
    private static String SCHOLASTIC_SUBJECTS = "Scholastic Subjects";
    private static String NO_OF_SCHOLASTIC_SUBJECTS = "No of Scholastic Subjects";
    private static String NO_OF_COSCHOLASTIC_SUBJECTS = "No of Co-Scholastic Subjects";
    private static String COSCHOLASTIC_SUBJECTS = "Co-Scholastic Subjects";
    private static String REPORT_ID = "STUDENT_COURSE_DETAIL_REPORT";
    private static String REPORT_DISPLAY_NAME = "Student Course Detail Report";
    private static String DESCRIPTION = "Course details (Scholastic/Co-Scholastic)";


    public CourseDetailsReportBuilder(UserPermissionManager userPermissionManager, InstituteManager instituteManager, CourseManager courseManager) {
        super(userPermissionManager, instituteManager);
        this.instituteManager = instituteManager;
        this.courseManager = courseManager;
    }

    @Override
    public ReportMetadata getReportMetadata() {
        return new ReportMetadata(
                REPORT_ID,
                REPORT_DISPLAY_NAME,
                Module.COURSES,
                DESCRIPTION
        );
    }

    @Override
    public List<ReportHeaderAttribute> getStaticHeaders() {
        return Arrays.asList(
                ReportHeaderAttribute.STUDENT_ADMISSION_NUMBER,
                ReportHeaderAttribute.STUDENT_NAME,
                ReportHeaderAttribute.STUDENT_FATHER_NAME
        );
    }

    @Override
    public CourseReportDetailsStore getStore(CourseReportPayload payload) {
        List<StudentCoursesDetailsLite> studentCoursesList = courseManager.getStudentCoursesDetails(
            payload.getInstituteId(),
            payload.getAcademicSessionId(),
            payload.getStandardId(),
            payload.getSectionIds()
        );

        Standard standard = instituteManager.getStandardByStandardId(
            payload.getInstituteId(),
            payload.getAcademicSessionId(),
            payload.getStandardId()
        );

        String className = StandardUtils.getClassName(payload.getSectionIds(), standard);

        Map<UUID, Map<CourseType, CourseCountDetails>> studentCourseCountMap = new HashMap<>();
        Set<UUID> selectedCourseIdSet = payload.getCourseId();

        for (StudentCoursesDetailsLite studentCourses : studentCoursesList) {
            UUID studentId = studentCourses.getStudentInfoDetails().getStudentId();

            Map<CourseType, CourseCountDetails> courseTypeMap = new HashMap<>();
            courseTypeMap.put(CourseType.SCHOLASTIC, filterCourses(studentCourses.getScholasticCourses(), selectedCourseIdSet));
            courseTypeMap.put(CourseType.COSCHOLASTIC, filterCourses(studentCourses.getCoScholasticCourses(), selectedCourseIdSet));

            studentCourseCountMap.put(studentId, courseTypeMap);
        }

        return new CourseReportDetailsStore(className, studentCoursesList, Collections.emptyList(), Collections.emptyMap(), studentCourseCountMap);
    }


    @Override
    public String getDownloadReportName(CourseReportPayload payload, CourseReportDetailsStore store) {
        return "Student_Course_Detail_Report";
    }

    @Override
    public ReportHeader getHeader(CourseReportPayload payload, CourseReportDetailsStore store) {
        List<ReportRow> rows = new ArrayList<>();
        List<MergeArea> mergeAreas = new ArrayList<>();

        // check if section column needed
        boolean includeSection = !(CollectionUtils.isEmpty(payload.getSectionIds())); // 🔑 change

        // prepare header columns dynamically
        List<String> headerCols = new ArrayList<>(Arrays.asList(SERIAL_NUMBER, ADMISSION_NUMBER, ROLL_NUMBER, STUDENT_NAME, FATHER_NAME));

        if (includeSection) {
            headerCols.add(SECTION);
        }

        headerCols.addAll(Arrays.asList(NO_OF_SCHOLASTIC_SUBJECTS, SCHOLASTIC_SUBJECTS, NO_OF_COSCHOLASTIC_SUBJECTS, COSCHOLASTIC_SUBJECTS));

        int totalCols = headerCols.size();
        int rowIndex = 0;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MMM/yyyy HH:mm:ss");
        String formattedDateTime = java.time.LocalDateTime.now().format(formatter);

        addHeaderRow(rows, mergeAreas, rowIndex++, REPORT_NAME, REPORT_HEADING, totalCols, 2);
        addHeaderRow(rows, mergeAreas, rowIndex++, CLASS_NAME, store.getClassName(), totalCols, 2);
        addHeaderRow(rows, mergeAreas, rowIndex++, GENERATED_AT, formattedDateTime, totalCols, 2);

        List<ReportCellDetails> headerRow = new ArrayList<>();
        for (String col : headerCols) {
            headerRow.add(createCell(col, true));
        }
        rows.add(new ReportRow(headerRow));

        return new ReportHeader(rows, mergeAreas);
    }

    private void addHeaderRow(List<ReportRow> rows, List<MergeArea> mergeAreas, int rowIndex, String key, String value, int totalCols, int keyCols) {

        List<ReportCellDetails> row = new ArrayList<>();
    
        row.add(createCell(key, true));
        addEmptyCells(row, keyCols - 1);

        row.add(createCell(value, false));
        addEmptyCells(row, totalCols - keyCols - 1);

        addMerge(mergeAreas, rowIndex, rowIndex, 0, keyCols-1);
        addMerge(mergeAreas, rowIndex, rowIndex, keyCols, totalCols-1);

        rows.add(new ReportRow(row));
    }

    @Override
    public ReportBody getBody(CourseReportPayload payload, CourseReportDetailsStore store) {
        List<ReportRow> bodyReportRows = new ArrayList<>();
        int count = 1;

        boolean includeSection = !(CollectionUtils.isEmpty(payload.getSectionIds()));

        for (StudentCoursesDetailsLite studentCourses : store.getStudentCoursesList()) {
            StudentInfoDetails details = studentCourses.getStudentInfoDetails();

            ReportRow row = new ReportRow();

        // basic columns
            addColumns(row, ReportHorizontalTextAlignment.CENTER,
                String.valueOf(count++),
                details.getAdmissionNumber(),
                details.getRollNumber(),
                details.getName(),
                details.getFathersName()
            );

            if (includeSection) { 
                addColumns(row, ReportHorizontalTextAlignment.CENTER,
                    details.getSectionName()
                );
            }

            Map<CourseType, CourseCountDetails> courseTypeMap =store.getStudentCourseCountMap().get(details.getStudentId());

            CourseCountDetails scholasticDetails = courseTypeMap.get(CourseType.SCHOLASTIC);
            CourseCountDetails coScholasticDetails = courseTypeMap.get(CourseType.COSCHOLASTIC);

            addColumns(row, ReportHorizontalTextAlignment.CENTER,
                String.valueOf(scholasticDetails.getAssignedCourseCount()),
                scholasticDetails.getAssignedCoursesNameCSV()
            );

            addColumns(row, ReportHorizontalTextAlignment.CENTER,
                String.valueOf(coScholasticDetails.getAssignedCourseCount()),
                coScholasticDetails.getAssignedCoursesNameCSV()
            );

            bodyReportRows.add(row);
            }

        return new ReportBody(bodyReportRows, Collections.emptyList());
    }

    private CourseCountDetails filterCourses(List<Course> courseList, Set<UUID> selectedCourseIdSet) {
        if (CollectionUtils.isEmpty(courseList)) {
            return new CourseCountDetails(0, "-");
        }

        StringBuilder coursesNameStr = new StringBuilder();
        int courseCount = 0;

        for (Course course : courseList) {
            if (selectedCourseIdSet != null && !selectedCourseIdSet.isEmpty()
                    && !selectedCourseIdSet.contains(course.getCourseId())) {
                continue;
            }

            String courseName = course.getCourseName();
            if (coursesNameStr.length() == 0) {
                coursesNameStr.append(courseName);
            } else {
                coursesNameStr.append(", ").append(courseName);
            }
            courseCount++;
        }

        return new CourseCountDetails(
                courseCount,
                coursesNameStr.length() == 0 ? "-" : coursesNameStr.toString()
        );
    }

    @Override
    public ReportConfigs getConfigs(CourseReportPayload payload, CourseReportDetailsStore store) {
        return null;
    }

    @Override
    public void verifyAuthorisation(CourseReportPayload payload) {
        userPermissionManager.verifyAuthorisation(
                payload.getInstituteId(),
                payload.getUserId(),
                AuthorisationRequiredAction.GENERATE_STUDENT_COURSE_DETAILS_REPORT
        );
    }

    @Override
    public void payloadVerification(CourseReportPayload payload) {
        if (payload.getStandardId() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Standard is required")
            );
        }
    }
}