package com.lernen.cloud.core.lib.library.report;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.api.report.util.IReportPayload;
import com.embrate.cloud.core.api.report.util.IReportStore;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.embrate.cloud.core.lib.report.factory.ReportRegistry;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.library.report.LibraryReportFilterationCriteria;
import com.lernen.cloud.core.api.library.report.LibraryReportPayload;
import com.lernen.cloud.core.api.library.report.LibraryReportType;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.lib.managers.LibraryManager;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;

/**
 * Generator for library reports - handles both summary and detailed report flows
 */
public class LibraryReportGenerator {

    private static final Logger logger = LogManager.getLogger(LibraryReportGenerator.class);

    private final ReportRegistry reportRegistry;
    private final LibraryManager libraryManager;
    private final UserPermissionManager userPermissionManager;

    public LibraryReportGenerator(ReportRegistry reportRegistry, LibraryManager libraryManager,
                                 UserPermissionManager userPermissionManager) {
        this.reportRegistry = reportRegistry;
        this.libraryManager = libraryManager;
        this.userPermissionManager = userPermissionManager;
    }

    public ReportDetails generateReport(int instituteId, UUID userId, LibraryReportFilterationCriteria filterationCriteria,
                                       DownloadFormat downloadFormat) {
        
        // Validate inputs
        validateInputs(instituteId, userId);

        LibraryReportType libraryReportType = LibraryReportType.getLibraryReportType(filterationCriteria.reportTypeStr);

        // Check if accession number is enabled for this institute
        boolean accessionNumberEnabled = libraryManager.isAccessionNumberEnabled(instituteId);
        
        // FOR DETAILED REPORT ONLY: If accession number is not enabled, throw error
        if (libraryReportType == LibraryReportType.LIBRARY_DETAILED_REPORT && !accessionNumberEnabled) {
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_BOOK_DATA, 
                    "Accession number details are not enabled for this institute."));
        }

        if (downloadFormat == DownloadFormat.EXCEL) {
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DOWNLOAD_LIBRARY_EXCEL);

		} else if (downloadFormat == DownloadFormat.PDF) {
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DOWNLOAD_LIBRARY_PDF);
		}

        // Convert string IDs to UUIDs
        Set<UUID> genreIds = StringHelper.convertStringToSetUUID(filterationCriteria.genreIds);
        Set<UUID> publicationIds = StringHelper.convertStringToSetUUID(filterationCriteria.publicationIds);
        Set<UUID> publisherIds = StringHelper.convertStringToSetUUID(filterationCriteria.publisherIds);
        Set<UUID> authorIds = StringHelper.convertStringToSetUUID(filterationCriteria.authorIds);
        Set<UUID> libraryTypeIds = StringHelper.convertStringToSetUUID(filterationCriteria.libraryTypeIds);

        logger.info("Generating library report of type: {} for institute: {}", libraryReportType, instituteId);


        // Get the unified report builder
        ReportBuilder<IReportPayload, IReportStore> reportBuilder = reportRegistry.getReportBuilders(Module.LIBRARY_MANAGEMENT).get("LIBRARY_REPORT");

        if(reportBuilder == null){
            return null;
        }

        // Determine if accession details should be shown
        boolean showAccessionDetails = (libraryReportType == LibraryReportType.LIBRARY_DETAILED_REPORT);

        // Build and return the report
        return reportBuilder.build(new LibraryReportPayload(
            instituteId, userId, libraryReportType, 
            filterationCriteria.startDate, filterationCriteria.endDate, 
            genreIds, publicationIds, publisherIds, authorIds, libraryTypeIds,
            showAccessionDetails
        ));
    }

    private void validateInputs(int instituteId, UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_BOOK_DATA, "Invalid institute ID."));
        }
        if (userId == null) {
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_BOOK_DATA, "Invalid user ID."));
        }
    }
}
