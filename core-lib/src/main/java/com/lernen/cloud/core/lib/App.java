package com.lernen.cloud.core.lib;






import com.embrate.cloud.core.api.student.finance.ServiceExpenseResponseWithAdditionalData;
import com.embrate.cloud.core.api.student.finance.StudentFinanceTransactionStatus;


import com.embrate.cloud.core.lib.courses.CourseManager;
import com.embrate.cloud.core.lib.student.finance.StudentFinanceManager;

import com.lernen.cloud.core.api.examination.CourseDimensionSectionMarksStatusDetails;
import com.lernen.cloud.core.api.common.CursorPaginationResult;
import com.lernen.cloud.core.api.examination.MarksFeedDataExtended;
import com.lernen.cloud.core.api.examination.MarksFeedStatus;
import com.lernen.cloud.core.api.course.StudentCourses;
import com.lernen.cloud.core.api.diary.DiaryRemarkDetails;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.PersonalityTraitDeletionResponse;
import com.lernen.cloud.core.api.examination.config.CloneClassExamRequest;

import com.lernen.cloud.core.api.examination.config.DeleteExamStructureRequest;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;

import com.lernen.cloud.core.api.student.*;

import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.attendance.AttendanceManager;
import com.lernen.cloud.core.lib.attendance.staff.StaffAttendanceManager;
import com.lernen.cloud.core.lib.examination.ExamReportGenerator;
import com.lernen.cloud.core.lib.examination.ExaminationManager;
import com.lernen.cloud.core.lib.student.StudentAdmissionManager;
import com.lernen.cloud.core.lib.studytracker.StudyTrackerManager;
import com.lernen.cloud.dao.tier.fees.configuration.FeeConfigurationDao;
import com.embrate.cloud.core.lib.hostel.management.HostelManagementManager;
import com.embrate.cloud.core.lib.institute.onboarding.InstituteOnBoardingManager;
import com.embrate.cloud.core.lib.inventory.v2.InventoryReportGenerationManager;
import com.embrate.cloud.core.lib.salary.SalaryConfigurationManager;

import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.fees.FeesReportType;
import com.embrate.cloud.core.api.salary.PayHeadConfiguration;
import com.embrate.cloud.core.api.salary.PayHeadType;
import com.embrate.cloud.core.api.salary.v2.PayHeadTag;
import com.embrate.cloud.core.lib.frontdesk.FrontDeskManager;
import com.embrate.cloud.core.lib.hostel.management.HostelManagementManager;
import com.embrate.cloud.core.lib.salary.SalaryConfigurationManager;
import com.embrate.cloud.core.api.onboarding.institute.setup.*;
import com.embrate.cloud.core.lib.frontdesk.FrontDeskManager;
import com.embrate.cloud.core.lib.hostel.management.HostelManagementManager;
import com.embrate.cloud.core.lib.institute.onboarding.InstituteOnBoardingManager;
import com.lernen.cloud.core.api.assessment.AssessmentDetailsWithQuestions;
import com.lernen.cloud.core.api.assessment.AssessmentMetaDataResponse;
import com.lernen.cloud.core.api.attendance.AttendanceType;
import com.lernen.cloud.core.api.common.SearchResultWithPagination;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.library.Author;
import com.lernen.cloud.core.api.library.BookDetails;
import com.lernen.cloud.core.api.library.BookDetailsLite;
import com.lernen.cloud.core.api.library.Genre;
import com.lernen.cloud.core.api.library.Publication;
import com.lernen.cloud.core.api.library.Publisher;
import com.lernen.cloud.core.api.organisation.InstituteConfigurationPayload;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.report.ReportType;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffStatus;
import com.lernen.cloud.core.api.student.StudentEnrollmentAssignPayload;
import com.lernen.cloud.core.api.studytracker.report.StudyTrackerResponseDailyData;
import com.lernen.cloud.core.api.transport.Vehicle;
import com.lernen.cloud.core.api.transport.VehicleDocumentInfo;
import com.lernen.cloud.core.api.transport.VehicleDocumentType;
import com.lernen.cloud.core.api.transport.VehiclePayload;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.Gender;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.authentication.UserAuthenticationPayload;
import com.lernen.cloud.core.api.user.authentication.UserAuthenticationStatus;
import com.lernen.cloud.core.api.user.authentication.UserLoginView;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.api.visitor.VisitorDetailsPayload;
import com.lernen.cloud.core.api.visitor.VisitorDocumentType;
import com.lernen.cloud.core.api.visitor.VisitorStatus;
import com.lernen.cloud.core.lib.diary.StudentDiaryManager;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.fees.payment.FeeReportDetailsGenerator;
import com.lernen.cloud.core.lib.managers.LibraryManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.complainbox.ComplainBoxManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.library.report.LibraryReportGenerator;
import com.lernen.cloud.core.lib.reports.ExcelReportGenerator;
import com.lernen.cloud.core.lib.staff.StaffManager;


import com.lernen.cloud.core.lib.examination.ExaminationManager;

import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import static com.lernen.cloud.core.utils.NumberUtils.formatToRupees;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;


//
//import static com.embrate.cloud.core.api.institute.InstituteDocumentType.


/**
 * Hello world! 22
 */
public class App {
    public static final String DEST1 = "/Users/<USER>/Desktop/Embrate/OutputPDF/";
    public static final String DEST3 = "/Users/<USER>/Embrate/outputPdfHTML/";
    public static final String DEST2 = "C:\\Users\\<USER>\\OneDrive\\Desktop\\Embrate\\outputPdfHTML/";
    public static final String DEST5 = "C:\\Users\\<USER>\\Documents\\Embrate\\outputPdfHTML/";
    public static final String DEST4 = "/Users/<USER>/Embrate/outputPdfHTML/";
    public static final String DEST15 = "C:/Users/<USER>/OneDrive/Documents/GitHub/Embrate/outputPdfHTML/";
    private static final String MESSAGE = "प्रिय अभिभावक,\n आपके बच्चे की ऑनलाइन क्लास कल सुबह 10 बजे तक आपको मिल जाएगी। क्लास देखने के लिए इस लिंक पे जाएँ और एप्लीकेशन डाउनलोड करें - https://embrate.com/apk \n  आईडी : %s \n पासवर्ड : %s \n अपना पासवर्ड किसी से शेयर ना करें।\n \n - Bright Future";

    public static void main(String[] args) throws IOException {
        try {

            final ApplicationContext context = new ClassPathXmlApplicationContext("core-lib.xml");


            StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);

//            Map<String, String> studentEnrollmentMap = new HashMap<>();
////            studentEnrollmentMap.put("REG-563", "ADM-563");
//            // studentEnrollmentMap.put("REG-564", "ADM-564");
//            // final BulkEnrollStudentResponse response = studentAdmissionManager.bulkAdmitStudents(
//            //         110, 153,
//            //         new BulkEnrollStudentPayload(true, studentEnrollmentMap), UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));

//            StaffManager staffManager = context.getBean(StaffManager.class);
//		final List<FullStaffDetails> staff = staffManager.advanceSearchStaff(
//            101, "", StaffStatus.ONBOARD, "", "",
//				"", "");
//
//                if(staff.isEmpty() || staff.size() == 0) {
//                    System.out.println("No staff found");
//                    return;
//                } else {
//
//                    for (FullStaffDetails fullStaffDetails : staff) {
//                        System.out.println();
//                        System.out.println(fullStaffDetails.getStaffBasicDetailsWithCategoryDepartDesignation().getName());
//                        System.out.println(fullStaffDetails.getStaffVisitingDaysListString());
//                    }
//
//                }
            // ExaminationManager examinationManager = context.getBean(ExaminationManager.class);

            // MarksFeedDataExtended data = examinationManager.updateExamMarksFeedStatus(101, 256, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), MarksFeedStatus.SUBMITTED, UUID.fromString("e001a452-85f2-493a-8dda-fcdf7b295a71"), UUID.fromString("06466277-31ec-41ce-8c5e-8234094a3b4c"), 490, UUID.fromString("194de501-2a0f-4c3c-a55e-b429d12e6390"), 3459);
            
            // System.out.println("Marks Feed Data Extended: " + data.toString());

            // MarksFeedDataExtended dataWithoutSection = examinationManager.updateExamMarksFeedStatus(101, 317, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), MarksFeedStatus.SUBMITTED, UUID.fromString("a822bdfd-2df3-4be6-b796-305b528cce79"), UUID.fromString("28713170-b6fb-485b-8f90-15473cb2732e"), 490, UUID.fromString("11d7b478-6a83-422f-b2c8-dd3ffa5651dd"), null);

            // System.out.println("Marks Feed Data Extended without section : " + dataWithoutSection.toString());



            // final List<CourseDimensionSectionMarksStatusDetails> courseDimensionSectionMarksStatusDetailsList = examinationManager
			// 	.getCourseDimensionSectionMarksStatusDetails(101, 256, UUID.fromString("e6a1fc0c-78cc-4e43-afb2-da4243ec9d97"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
            //     System.out.println(courseDimensionSectionMarksStatusDetailsList.toString());

                // + flag + courseId + sectionId + dimensionId (3467 / 3468) 
                //  final List<CourseDimensionSectionMarksStatusDetails> courseDimensionSectionMarksStatusDetailsListUnique = examinationManager
				// .getCourseDimensionSectionMarksStatusDetails(101, 256, UUID.fromString("e6a1fc0c-78cc-4e43-afb2-da4243ec9d97"), UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), true, UUID.fromString("4ae817ee-34f6-4e27-aadc-808504b94e0c"), 490, 3467);
                // System.out.println(courseDimensionSectionMarksStatusDetailsListUnique.toString());


        //     StaffManager staffManager = context.getBean(StaffManager.class);
		// final List<FullStaffDetails> staff = staffManager.advanceSearchStaff(
        //     101, "", StaffStatus.ONBOARD, "", "",
		// 		"", "");

        //         if(staff.isEmpty() || staff.size() == 0) {
        //             System.out.println("No staff found");
        //             return;
        //         } else {
                    
        //             for (FullStaffDetails fullStaffDetails : staff) {
        //                 System.out.println();
        //                 System.out.println(fullStaffDetails.getStaffBasicDetailsWithCategoryDepartDesignation().getName());
        //                 System.out.println(fullStaffDetails.getStaffVisitingDaysListString());
        //             }

        //         }
            // StaffAttendanceManager staffAttendanceManager = context.getBean(StaffAttendanceManager.class);
            // final List<StaffAttendance> staffAttendanceList = staffAttendanceManager.getStaffAttendanceList(110, 255, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), AttendanceType.PRESENT);
            // System.out.println(staffAttendanceList);

            // ExaminationManager examinationManager = context.getBean(ExaminationManager.class);
            // final List<ExamDimensionValuesPayload> examDimensionValuesPayloads = examinationManager.getExamDimensionValuesPayloads(110, 255, UUID.fromString("36a283c3-ed54-48cc-bba8-40af76a12e9f"));
            // System.out.println(examDimensionValuesPayloads);

//[09/Jun/2025 10:33:21] "GET /a0a3ec22-dbf8-11ec-b8bf-120cd1ce09b5/examination/exam-details/36a283c3-ed54-48cc-bba8-40af76a12e9f HTTP/1.1" 200 54395
//                    /2.0/examination/36a283c3-ed54-48cc-bba8-40af76a12e9f/assign-courses/COSCHOLASTIC?institute_id=110&user_id=74cd36ca-180d-4360-8fe5-f95472bf4e51 [{'courseId': 'f41086be-0571-42e3-a29f-30aed191cf75', 'dimensionValuesPayloads': [{'dimensionId': '142', 'maxMarks': '100', 'minMarks': '0'}, {'dimensionId': '487', 'maxMarks': '100', 'minMarks': ''}]}, {'courseId': '4da350ff-782b-4a36-a8b7-2e31f64a9feb', 'dimensionValuesPayloads': [{'dimensionId': '142', 'maxMarks': '100', 'minMarks': '0'}, {'dimensionId': '487', 'maxMarks': '100', 'minMarks': ''}]}]
//            AttendanceManager attendanceManager = context.getBean(AttendanceManager.class);
//            [{'dimensionId': '142', 'maxMarks': '200', 'minMarks': '0'}, {'dimensionId': '147', 'maxMarks': '100', 'minMarks': ''}, {'dimensionId': '487', 'maxMarks': '100', 'minMarks': ''}]

            // StudentFinanceManager   studentFinanceManager = context.getBean(StudentFinanceManager.class);
//            ServiceExpenseResponse serviceExpenseResponse = new ServiceExpenseResponse(UUID.fromString("76c8d830-e873-4bbd-a8ad-c7c4f31e9f74"), "SS-2", UUID.fromString("c9160949-445a-4da0-9607-60f8400834ef"), UUID.fromString("e2c9d5d2-faa3-4a5d-980e-32f5fa6a9f9a"), "Checkup", ServiceCategory.MEDICAL, TransactionMode.WALLET, 500.0, 500.0, null, "Fever", StudentFinanceTransactionStatus.ACTIVE, **********,  null, **********);
//            boolean cancel = studentFinanceManager.cancelServiceExpenseTransaction(110, 295, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), UUID.fromString("76c8d830-e873-4bbd-a8ad-c7c4f31e9f74"), serviceExpenseResponse);
//            System.out.println(cancel);
//            CursorPaginationResult<StudentFinanceSummary> studentFinanceSummary = studentFinanceManager.getStudentFinanceSummary(110, 255, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
//            CursorPaginationResult<ServiceExpenseResponseWithAdditionalData> serviceExpenseResponseWithOtherDataList = studentFinanceManager.getServiceExpenses(110, 295, StudentFinanceTransactionStatus.ACTIVE, "eyJ0cmFuc2FjdGlvbl9kYXRlIjoiMTc1MTM5NDYwMCIsImNyZWF0ZWRfdGltZXN0YW1wIjoiMTc1MjU4MDQ3NSIsImRpcmVjdGlvbiI6InByZXZpb3VzIn0=", 9, "", "", null);
//            System.out.println(serviceExpenseResponseWithOtherDataList.getPreviousCursor() + "    " + serviceExpenseResponseWithOtherDataList.getNextCursor());
//            for(ServiceExpenseResponseWithAdditionalData serviceExpenseResponseWithAdditionalData : serviceExpenseResponseWithOtherDataList.getItems()) {
//                System.out.println(serviceExpenseResponseWithAdditionalData.getServiceExpenseResponse());
//            }
//            // // boolean cancel = studentFinanceManager.cancelServiceExpenseTransaction(110, 295, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), UUID.fromString("4bdb93b5-2ade-4d97-9f5f-572a1c6c5071"));
//            // // System.out.println(cancel);

//            ExamDimensionValuesPayload examDimensionValuesPayload = new ExamDimensionValuesPayload(142, 500.0, 0.0, null, null);
//                    ExamDimensionValuesPayload examDimensionValuesPayload1 = new ExamDimensionValuesPayload(487, 200.0, 0.0, null, null);
//                    ExamDimensionValuesPayload examDimensionValuesPayload2 = new ExamDimensionValuesPayload(147, 400.0, 0.0, null, null);
//                    List<ExamDimensionValuesPayload> examDimensionValuesPayloads = new ArrayList<>();
//                    examDimensionValuesPayloads.add(examDimensionValuesPayload);
//                    examDimensionValuesPayloads.add(examDimensionValuesPayload1);
//                    examDimensionValuesPayloads.add(examDimensionValuesPayload2);
//            ExamCoursesPayload examCoursesPayload = new ExamCoursesPayload(UUID.fromString("f41086be-0571-42e3-a29f-30aed191cf75"), examDimensionValuesPayloads);
//            ExamCoursesPayload examCoursesPayload1 = new ExamCoursesPayload(UUID.fromString("4da350ff-782b-4a36-a8b7-2e31f64a9feb"), examDimensionValuesPayloads);
//            List<ExamCoursesPayload> examCoursesPayloadList = new ArrayList<>();
//            examCoursesPayloadList.add(examCoursesPayload);
//            examCoursesPayloadList.add(examCoursesPayload1);
//            ExaminationManager examinationManager = context.getBean(ExaminationManager.class);
////            final ExamDetails examDetails = examinationManager.getExamDetails(UUID.fromString("36a283c3-ed54-48cc-bba8-40af76a12e9f"), 110);
////            System.out.println(examDetails.getExamCourses());
//            boolean success = examinationManager.addCoursesInExam(110, UUID.fromString("36a283c3-ed54-48cc-bba8-40af76a12e9f"), CourseType.COSCHOLASTIC, examCoursesPayloadList, true, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), false);
//            System.out.println(success);

            // ExamDimensionValuesPayload examDimensionValuesPayload = new ExamDimensionValuesPayload(142, 500.0, 0.0, null, null);
            //         ExamDimensionValuesPayload examDimensionValuesPayload1 = new ExamDimensionValuesPayload(487, 200.0, 0.0, null, null);
            //         ExamDimensionValuesPayload examDimensionValuesPayload2 = new ExamDimensionValuesPayload(147, 400.0, 0.0, null, null);
            //         List<ExamDimensionValuesPayload> examDimensionValuesPayloads = new ArrayList<>();
            //         examDimensionValuesPayloads.add(examDimensionValuesPayload);
            //         examDimensionValuesPayloads.add(examDimensionValuesPayload1);
            //         examDimensionValuesPayloads.add(examDimensionValuesPayload2);
            // ExamCoursesPayload examCoursesPayload = new ExamCoursesPayload(UUID.fromString("f41086be-0571-42e3-a29f-30aed191cf75"), examDimensionValuesPayloads);
            // ExamCoursesPayload examCoursesPayload1 = new ExamCoursesPayload(UUID.fromString("4da350ff-782b-4a36-a8b7-2e31f64a9feb"), examDimensionValuesPayloads);
            // List<ExamCoursesPayload> examCoursesPayloadList = new ArrayList<>();
            // examCoursesPayloadList.add(examCoursesPayload);
            // examCoursesPayloadList.add(examCoursesPayload1);
            // ExaminationManager examinationManager = context.getBean(ExaminationManager.class);
//            final ExamDetails examDetails = examinationManager.getExamDetails(UUID.fromString("36a283c3-ed54-48cc-bba8-40af76a12e9f"), 110);
//            System.out.println(examDetails.getExamCourses());
            // boolean success = examinationManager.addCoursesInExam(110, UUID.fromString("36a283c3-ed54-48cc-bba8-40af76a12e9f"), CourseType.COSCHOLASTIC, examCoursesPayloadList, true, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), false);
            // System.out.println(success);

//            final StudentSessionAttendanceDetails studentSessionAttendanceDetails = attendanceManager.getStudentSessionAttendanceDetails(110, 255,
//                    UUID.fromString("a32decce-2d67-4fdc-a09f-da319931fcb5"));
////            System.out.println(studentSessionAttendanceDetails);
//            CalendarManager calendarManager = context.getBean(CalendarManager.class);
//            final UserCalendarDetails userCalendarDetails = calendarManager.getUserCalendarDetails(110, 295,
//                    UUID.fromString("7791d02d-e923-4c1b-a49d-45f26d5e1687"), UserType.STUDENT);
//            System.out.println(userCalendarDetails);
//            for (UserMonthCalendarSummary monthlySummary : userCalendarDetails.getUserMonthCalendarSummaryList()) {
//                System.out.println("Month: " + monthlySummary.getMonth() + ", Year: " + monthlySummary.getYear());
//                List<Calendar> dailyList = monthlySummary.getCalendarList();
//System.out.println(dailyList);





//            final PersonalityTraitDeletionResponse deletionResponse = examinationManager.deletePersonalityTraitsWithValidation(
//                    110, 153, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//                    Arrays.asList(UUID.fromString("8471c300-9f09-41bf-b3a0-67a34f9c95cd"),
//                            UUID.fromString("4f3a8bbd-5b57-40d9-8616-a1e62039a02c")), Arrays.asList("Cleanliness"), false);
//
//            System.out.println(deletionResponse.toString());


//            CalendarManager calendarManager = context.getBean(CalendarManager.class);
//             Map<String, Object> metaData = new HashMap<>();
//             List<String> string  = new ArrayList<>();
//             string.add("aed09712-719b-4377-bda6-d3622e3c2432:5161");
//             string.add("c4a4b585-bff9-4435-be80-8a4f14434c2e");
//             List<String> stringList = new ArrayList<>();
//             stringList.add("d75a27d9-002f-431e-b580-9c0b11d31abb");
//             metaData.put("STANDARD", string);
//             metaData.put("INCHARGE", stringList);
//            final ExamConfigManager examConfigManager = context.getBean(ExamConfigManager.class);
//
//            ExamStructureCoursesResponse updated = examConfigManager.cloneClassExamStructure(new CloneClassExamRequest(10275, 10275 ,204,171,"XI-PCB", new ArrayList<>(Arrays.asList("XI-PCB")), true, true));
//
//            DeleteExamStructureRequest deleteExamStructureRequest = new DeleteExamStructureRequest(204, "XI-PCB");
//
//            boolean delete = examConfigManager.deleteClassExamStructure(10275, deleteExamStructureRequest);
//            System.out.println(delete);


//            EventDetails eventDetails = new EventDetails(295, UUID.fromString("2f3eb4a1-e3ac-4eb8-ac85-d688afab4634"), "event 999th", "", 1751259409, null, EventCategorexamey.FESTIVAL, true, null, "", null, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//            boolean success = calendarManager.updateEvent(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), eventDetails);
//            System.out.println(success);
//            UserCalendarDetails userCalendarDetails = calendarManager.getUserCalendarDetails(110, 295, UUID.fromString("5e1122c0-8afd-4f65-8538-bc4314467aa3"), UserType.STUDENT);
//            List<UserMonthCalendarSummary> summaryList = userCalendarDetails.getUserMonthCalendarSummaryList();

//            for (UserMonthCalendarSummary summary : summaryList) {
//                System.out.println("---- Calendar Summary ----");
//                System.out.println("Month: " + summary.getMonth());
//                System.out.println("Year: " + summary.getYear());
//                System.out.println("Start Day: " + summary.getStartDay());
//                System.out.println("End Day: " + summary.getEndDay());
//
//                // Print calendarList details (basic example)
//                List<Calendar> calendarList = summary.getCalendarList();
//                System.out.println("Number of Days in calendarList: " + calendarList.size());
//                for(Calendar calendar : calendarList){
//                    System.out.println(" momthly calnendar " + calendar);
//                }
//
//                // Print monthlyCalendarDetails if needed
//                List<CalendarDetails> calendarDetailsList = summary.getMonthlyCalenderDetails();
//                System.out.println("Number of Entries in monthlyCalendarDetails: " + calendarDetailsList.size());
//
//                // Optional: Loop through calendarDetails
//                for (CalendarDetails details : calendarDetailsList) {
//                    System.out.println(" - Detail: " + details.toString()); // Customize based on fields
//                }
//
//                System.out.println();
//            }


//            InventoryReportGenerationManager inventoryReportGenerationManager = context.getBean(InventoryReportGenerationManager.class);
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//            int instituteId = 101;
//            Integer start = 1751481000;
//            Integer end = 1752172199;
//            UUID userId = UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693");
//            ReportDetails reportDetails = inventoryReportGenerationManager.generateReport(instituteId, ReportType.ISSUE_DETAILS_REPORT, userId, start, end, DownloadFormat.EXCEL);
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST15 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);

//            InventoryReportGenerationManager inventoryReportGenerationManager = context.getBean(InventoryReportGenerationManager.class);
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//            int instituteId = 101;
//            Integer start = 1751481000;
//            Integer end = 1752172199;
//            UUID userId = UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693");
//            ReportDetails reportDetails = inventoryReportGenerationManager.generateReport(instituteId, ReportType.ISSUE_DETAILS_REPORT, userId, start, end, DownloadFormat.EXCEL);
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST15 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);
//            final ExamConfigManager examConfigManager = context.getBean(ExamConfigManager.class);
//
//            ExamStructureCoursesResponse updated = examConfigManager.cloneClassExamStructure(new CloneClassExamRequest(10275, 10275 ,204,279,"XI-PCB", new ArrayList<>(Arrays.asList("XI-PCB")), false, false));
//
//            DeleteExamStructureRequest deleteExamStructureRequest = new DeleteExamStructureRequest(279, "XI-PCB");
//
//            boolean delete = examConfigManager.deleteClassExamStructure(10275, deleteExamStructureRequest);
//            System.out.println(delete);

           /*  CalendarManager calendarManager = context.getBean(CalendarManager.class);
>>>>>>> 506ff79707cf27d7e181e9d39db70791c6e37c73
             Map<String, Object> metaData = new HashMap<>();
             List<String> string  = new ArrayList<>();
             string.add("aed09712-719b-4377-bda6-d3622e3c2432:5161");
             string.add("c4a4b585-bff9-4435-be80-8a4f14434c2e");
             List<String> stringList = new ArrayList<>();
             stringList.add("d75a27d9-002f-431e-b580-9c0b11d31abb");
             metaData.put("STANDARD", string);
             metaData.put("INCHARGE", stringList);

            EventDetails eventDetails = new EventDetails(295, UUID.fromString("2f3eb4a1-e3ac-4eb8-ac85-d688afab4634"), "event 999th", "", 1751259409, null, EventCategory.FESTIVAL, true, null, "", null, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51")); */
//            boolean success = calendarManager.updateEvent(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), eventDetails);
//            System.out.println(success);
            /* UserCalendarDetails userCalendarDetails = calendarManager.getUserCalendarDetails(110, 295, UUID.fromString("5e1122c0-8afd-4f65-8538-bc4314467aa3"), UserType.STUDENT);
            List<UserMonthCalendarSummary> summaryList = userCalendarDetails.getUserMonthCalendarSummaryList();

            for (UserMonthCalendarSummary summary : summaryList) {
                System.out.println("---- Calendar Summary ----");
                System.out.println("Month: " + summary.getMonth());
                System.out.println("Year: " + summary.getYear());
                System.out.println("Start Day: " + summary.getStartDay());
                System.out.println("End Day: " + summary.getEndDay());

                // Print calendarList details (basic example)
                List<Calendar> calendarList = summary.getCalendarList();
                System.out.println("Number of Days in calendarList: " + calendarList.size());
                for(Calendar calendar : calendarList){
                    System.out.println(" momthly calnendar " + calendar);
                }

                // Print monthlyCalendarDetails if needed
                List<CalendarDetails> calendarDetailsList = summary.getMonthlyCalenderDetails();
                System.out.println("Number of Entries in monthlyCalendarDetails: " + calendarDetailsList.size());

                // Optional: Loop through calendarDetails
                for (CalendarDetails details : calendarDetailsList) {
                    System.out.println(" - Detail: " + details.toString()); // Customize based on fields
                }

                System.out.println();
            } */



//            FeeConfigurationManager feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
//            FeeHeadConfiguration feeHeadConfiguration  = new FeeHeadConfiguration(110, 0, 298, "DairYfees", true, "", null, null);
//[18/Jun/2025 10:35:51] "GET /a0a3ec22-dbf8-11ec-b8bf-120cd1ce09b5/student-diary/show-list-remarks?searchText=&academic_session_id=255&categoryIds=&userIds=&offset=0&limit=10 HTTP/1.1" 200 948113
//            StudentDiaryManager studentDiaryManager = context.getBean(StudentDiaryManager.class);
//            Set<UUID> uuidList = new HashSet<>(Arrays.asList(UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51")));
//
//            final SearchResultWithPagination<DiaryRemarkDetails> remarkDetails = studentDiaryManager.getRemarkDetailsWithPagination(
//                    110, 255, "", null, null, 0, 10);
//            for(DiaryRemarkDetails remarkDetails1 : remarkDetails.getResult()){
//                System.out.println(remarkDetails1.getDate() + " " + remarkDetails1.getTitle());
//            }
//            CalendarManager calendarManager = context.getBean(CalendarManager.class);
//            boolean success = calendarManager.deleteHoliday(110, 255, 316);
//            System.out.println(success);

//            final boolean savedFeeHeadConfiguration = feeConfigurationManager.addFeeHeadConfigurations(110,
//                    Arrays.asList(feeHeadConfiguration), null);

            /* InstituteSetupManager instituteSetupManager = context.getBean(InstituteSetupManager.class);
            InstituteManager instituteManager = context.getBean(InstituteManager.class);
            Institute existingInstitute = instituteManager.getInstitute(10600);
            System.out.println(existingInstitute); */
           /*  DashboardManager dashboardManager = context.getBean(DashboardManager.class);
            final InstituteStats instituteStats = dashboardManager.getInstituteStats(10225, 194, 1749548558);
            System.out.println(instituteStats.getRelievedStudentsCount()); */
            /* LessonPlanManager lessonPlanManager = context.getBean(LessonPlanManager.class);
            List<LessonPlanStatusWithCourseAndStaffDetails> lessonPlanStatusWithCourseAndStaffDetails = lessonPlanManager.getCourseAndStaffDetails(101, 256, UUID.fromString("05db9f24-2b70-41d4-98ef-982f682af5ca"), null, null);
            System.out.println(lessonPlanStatusWithCourseAndStaffDetails); */
            /* List<LessonPlanChapterDetails> lessonPlanChapterDetails = lessonPlanManager.getChapterTopicDetails(UUID.fromString("1722913a-49e6-40f3-8cac-99416aacf03a"), LessonPlanStatus.ACTIVE);
            List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataWithChapterAndTopicPayloads = new ArrayList<>();
            LessonPlanMetaDataPayload lessonPlanMetaDataPayload = new LessonPlanMetaDataPayload(UUID.fromString("1722913a-49e6-40f3-8cac-99416aacf03a"), "", "",  UUID.fromString("10523d7c-2c13-4d56-8dc8-187c0a3f5b1f"), new ArrayList<>(3473), UUID.fromString("455d5117-692b-4aea-8d0d-595cdb189f22"), LessonPlanStatus.ACTIVE);
            List<LessonPlanChapterPayload> lessonPlanChapterPayloads = new ArrayList<>();
            List<ChapterTopicPayload> topicList = Arrays.asList(
                new ChapterTopicPayload(
                    UUID.fromString("505e76c1-2d5c-43dd-9be2-467e2a413d58"),
                    "Basic Expressions",
                    "Learn to form and simplify basic expressions",
                    1716921000
                ),
                new ChapterTopicPayload(
                    UUID.fromString("5e38f64d-ee65-4e36-9c1c-03079d188ab1"),
                    "Variables and Constants",
                    "Understanding the difference between variables and constants",
                    1716921000
                )
            );
>>>>>>> 0fd7821dfe9e3ff51e00e2a6fd6b9c0e50ac970e

            LessonPlanChapterPayload lessonPlanChapterPayload = new LessonPlanChapterPayload(UUID.fromString("190257c9-ab7f-4fc5-9351-9b0d7b212eb2"), "Chapter 1: Introduction to Algebra", "Introduction and foundational concepts", topicList, 1717093800);
            List<ChapterTopicPayload> topicList2 = Arrays.asList(
                new ChapterTopicPayload(
                    UUID.fromString("aac2264e-c114-41d3-a5d8-b3781081777e"),
                    "Solving Simple Equations",
                    "Solving equations with one variable",
                    1717957800
                ),
                new ChapterTopicPayload(
                    null,
                    "Solving Simple Equations",
                    "Solving equations with one variable",
                    1746469800
                )
            );
             LessonPlanChapterPayload lessonPlanChapterPayload2 = new LessonPlanChapterPayload(UUID.fromString("b8ccea46-5f15-4876-adeb-3114fa472dd4"), "Chapter 2: Linear Equations", "Basic concepts of linear equations", topicList2, 1718217000);
             LessonPlanChapterPayload lessonPlanChapterPayload3 = new LessonPlanChapterPayload(null, "chapter 3: Geometry", "", null, null);
             lessonPlanChapterPayloads.add(lessonPlanChapterPayload);
             lessonPlanChapterPayloads.add(lessonPlanChapterPayload2);
             lessonPlanChapterPayloads.add(lessonPlanChapterPayload3);
             LessonPlanMetaDataWithChapterAndTopicPayload lessonPlanMetaDataWithChapterAndTopicPayload = new LessonPlanMetaDataWithChapterAndTopicPayload(lessonPlanMetaDataPayload, lessonPlanChapterPayloads);
             lessonPlanMetaDataWithChapterAndTopicPayloads.add(lessonPlanMetaDataWithChapterAndTopicPayload);
            boolean result = lessonPlanManager.lessonPlanFunctionClassification(101, 256, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), UUID.fromString("455d5117-692b-4aea-8d0d-595cdb189f22"), lessonPlanMetaDataWithChapterAndTopicPayloads);

//                for (EventAndHoliday daily : dailyList) {
//                    List<EventDetails> events = daily.getEvents();
////                    if (events == null || events.isEmpty()) {
////                        continue;
////                    }
//
//                    System.out.println("  Day (epoch): " + daily.getAttendanceDay());
//
//                    // Print holidays
//                    List<StaticHoliday> holidays = daily.getHolidays();
//                    if (holidays == null || holidays.isEmpty()) {
//                        System.out.println("    Holidays: None");
//                    } else {
//                        System.out.print("    Holidays: ");
//                        for (int i = 0; i < holidays.size(); i++) {
//                            StaticHoliday holiday = holidays.get(i);
//                            System.out.print(holiday.getName()); // adjust method as needed
//                            if (i < holidays.size() - 1) System.out.print(", ");
//                        }
//                        System.out.println();
//                    }
//
//                    // Print events
//
//                    if (events == null || events.isEmpty()) {
//                        System.out.println("    Events: None");
//                    } else {
//                        System.out.print("    Events: ");
//                        for (int i = 0; i < events.size(); i++) {
//                            EventDetails event = events.get(i);
//                            System.out.print(event.getTitle());
//                            if (i < events.size() - 1) System.out.print(", ");
//                        }
//                        System.out.println();
//                    }
//                }
//            }
//            HostelManagementManager hostelManagementManager = context.getBean(HostelManagementManager.class);
//            HostelDetails hostelDetails = new HostelDetails(110, null, "Hostel 3", "H3", null, null, null, null);
//            final boolean success = hostelManagementManager.deleteHostelDetails(110, UUID.fromString("dad00d31-fef8-4b12-b9e3-290fefc7501d"), null);
//            System.out.println(success);
//            StudentManager studentManager = context.getBean(StudentManager.class);
//            StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);

//            EnquiryDetailsManager enquiryDetailsManager= context.getBean(EnquiryDetailsManager.class);
//            Set<EnquiryStatus> enquiryStatusSet = new HashSet<>();
//            enquiryStatusSet.add(EnquiryStatus.ACCEPTED);
//
//            final SearchResultWithPagination<EnquiryDetailsWithFollowup> enquiryDetailsWithPagination = enquiryDetailsManager.getEnquiryDetailsWithPagination(110,UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), 295, enquiryStatusSet, 10, 0, 0);
//            for(EnquiryDetailsWithFollowup enquiryDetailsWithFollowupList : enquiryDetailsWithPagination.getResult()){
//                System.out.println(enquiryDetailsWithFollowupList);
//            }
//            System.out.println(enquiryDetailsWithPagination);
//
//            [23/May/2025 13:51:25] "GET /a0a3ec22-dbf8-11ec-b8bf-120cd1ce09b5/student-management/promotion-configuration/295/6cbe3865-decd-42cb-9e55-243a93e374e4 HTTP/1.1" 200 1942
//            {'instituteId': '110', 'currentSessionId': 255, 'standardId': '6cbe3865-decd-42cb-9e55-243a93e374e4', 'sectionId': '5173', 'promotingStudentList': ['390f7f2b-e150-48db-bec9-4e81c43b8718'], 'feeStructureIdsList': [], 'carryForwardDiscount': True, 'discountStructureIdsList': None, 'carryForwardTransport': True}
//            StudentPromotionDetails studentPromotionDetails = new StudentPromotionDetails(110, 255, UUID.fromString("6cbe3865-decd-42cb-9e55-243a93e374e"), 5173, Arrays.asList(UUID.fromString("390f7f2b-e150-48db-bec9-4e81c43b8718")), null, true, null, true);
//            final boolean promoted = studentAdmissionManager.promoteStudents(110, 255,
//                    studentPromotionDetails, UUID.fromString("dad00d31-fef8-4b12-b9e3-290fefc7501d"));
//            System.out.println(promoted);
//            CalendarManager calendarManager = context.getBean(CalendarManager.class);
//            EventDetails eventManagementPayload = new EventDetails(255, UUID.fromString("d9338d76-a5b8-44ad-b42a-6931a3ad793d"), "new event", "funny games",  1747996176, EventCategory.FESTIVAL, true, null, null, null);
//            boolean s = calendarManager.updateEvent(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), eventManagementPayload);
////            boolean s = calendarManager.deleteEvent(110, 255, UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
////            System.out.println(s);
//            List<EventDetails> eventDetails = calendarManager.getEventDetails(110, 255);
//            System.out.println(eventDetails);

//            Student s = studentManager.getStudentByLatestAcademicSession(UUID.fromString("390f7f2b-e150-48db-bec9-4e81c43b8718"), Arrays.asList(StudentStatus.ENROLLED));
//            System.out.println(s.getStudentAcademicSessionInfoResponse().getHostelDetails());
//            final StudentManagementFieldDetails studentManagementFieldDetails = studentManager.getClassStudentsFieldData(110, 255,
//                  UUID.fromString("f6f78728-93af-42dd-82eb-0a1e934a427e")  , "3724", "HOSTEL", null);
//            for(StudentManagementOutputFieldStudentData studentManagementOutputFieldStudentData : studentManagementFieldDetails.getStudentDataList()) {
//                System.out.println(studentManagementOutputFieldStudentData.getStudent());
//            }

//            StudentManagementUpdateFieldValue studentManagementUpdateFieldValue = new StudentManagementUpdateFieldValue(StudentManagementField.HOSTEL, "");
//            List<StudentManagementUpdateFieldValue> studentManagementUpdateFieldValueList = new ArrayList<>();
//            studentManagementUpdateFieldValueList.add(studentManagementUpdateFieldValue);
//            StudentManagementUpdateFieldStudentData studentManagementUpdateFieldStudentData = new StudentManagementUpdateFieldStudentData(UUID.fromString("7894963d-03bb-4da0-947e-adf5ac3c220c"), studentManagementUpdateFieldValueList);
//            List<StudentManagementUpdateFieldStudentData> studentDataList = new ArrayList<>();
//            studentDataList.add(studentManagementUpdateFieldStudentData);
//            List<StudentManagementField> a = new ArrayList<>();
//            a.add(StudentManagementField.HOSTEL);
//            StudentManagementUpdateFieldPayload payload = new StudentManagementUpdateFieldPayload((StudentManagementDataType.SESSION_DATA), a, studentDataList);

//            ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//
//            StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//
//
//            final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(110,
//                    "STAFF_ATTENDANCE_DETAILS_IN_A_DAY", null, null,
//                    "ONBOARD", 1747333800, 1747420200, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//                    DownloadFormat.PDF);
//
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST3 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);
//            HostelManagementManager hostelManagementManager = context.getBean(HostelManagementManager.class);
////            HostelDetails hostelDetails = new HostelDetails(110, null, "Hostel 3", "H3", null, null, null, null);
////            final boolean success = hostelManagementManager.deleteHostelDetails(110, UUID.fromString("dad00d31-fef8-4b12-b9e3-290fefc7501d"), null);
////            System.out.println(success);

//            StudentManager studentManager = context.getBean(StudentManager.class);
//
//            final StudentManagementFieldDetails studentManagementFieldDetails = studentManager.getClassStudentsFieldData(110, 255,
//                  UUID.fromString("f6f78728-93af-42dd-82eb-0a1e934a427e")  , "3724", "HOSTEL", null);
//            for(StudentManagementOutputFieldStudentData studentManagementOutputFieldStudentData : studentManagementFieldDetails.getStudentDataList()) {
//                System.out.println(studentManagementOutputFieldStudentData.getStudent());
//            }
            System.out.println(result); */

            /* AttendanceManager attendanceManager = context.getBean(AttendanceManager.class);
            final StudentSessionAttendanceDetails studentSessionAttendanceDetails = attendanceManager.getStudentSessionAttendanceDetails(110, 255,
                    UUID.fromString("a32decce-2d67-4fdc-a09f-da319931fcb5"));
//            System.out.println(studentSessionAttendanceDetails);
            CalendarManager calendarManager = context.getBean(CalendarManager.class);
            final UserCalendarDetails userCalendarDetails = calendarManager.getUserCalendarDetails(110, 295,
                    UUID.fromString("7791d02d-e923-4c1b-a49d-45f26d5e1687"), UserType.STUDENT);
            System.out.println(userCalendarDetails);
            for (UserMonthCalendarSummary monthlySummary : userCalendarDetails.getUserMonthCalendarSummaryList()) {
                System.out.println("Month: " + monthlySummary.getMonth() + ", Year: " + monthlySummary.getYear());
                List<Calendar> dailyList = monthlySummary.getCalendarList();
System.out.println(dailyList); */

            /* FeeConfigurationDao feeConfigurationDao = context.getBean(FeeConfigurationDao.class);
            Set<UUID> studentId = new HashSet<>();
            studentId.add(UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"));
            System.out.println(feeConfigurationDao.getFeeAssignmentByStudentIds(101, studentId, 256, null)); */

            /* FeeConfigurationManager feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
            System.out.println(feeConfigurationManager.checkStudentFeeStructureChangeEligibilityV2(101, 256, UUID.fromString("e4ff3e07-9197-46ee-9584-887b1e8924d5"), UUID.fromString("10523d7c-2c13-4d56-8dc8-187c0a3f5b1f"))); */

//                for (EventAndHoliday daily : dailyList) {
//                    List<EventDetails> events = daily.getEvents();
////                    if (events == null || events.isEmpty()) {
////                        continue;
////                    }
//
//                    System.out.println("  Day (epoch): " + daily.getAttendanceDay());
//
//                    // Print holidays
//                    List<StaticHoliday> holidays = daily.getHolidays();
//                    if (holidays == null || holidays.isEmpty()) {
//                        System.out.println("    Holidays: None");
//                    } else {
//                        System.out.print("    Holidays: ");
//                        for (int i = 0; i < holidays.size(); i++) {
//                            StaticHoliday holiday = holidays.get(i);
//                            System.out.print(holiday.getName()); // adjust method as needed
//                            if (i < holidays.size() - 1) System.out.print(", ");
//                        }
//                        System.out.println();
//                    }
//
//                    // Print events
//
//                    if (events == null || events.isEmpty()) {
//                        System.out.println("    Events: None");
//                    } else {
//                        System.out.print("    Events: ");
//                        for (int i = 0; i < events.size(); i++) {
//                            EventDetails event = events.get(i);
//                            System.out.print(event.getTitle());
//                            if (i < events.size() - 1) System.out.print(", ");
//                        }
//                        System.out.println();
//                    }
//                }
            /* }
            HostelManagementManager hostelManagementManager = context.getBean(HostelManagementManager.class); */
//            HostelDetails hostelDetails = new HostelDetails(110, null, "Hostel 3", "H3", null, null, null, null);
//            final boolean success = hostelManagementManager.deleteHostelDetails(110, UUID.fromString("dad00d31-fef8-4b12-b9e3-290fefc7501d"), null);
//            System.out.println(success);
//            StudentManager studentManager = context.getBean(StudentManager.class);
//            StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);

//            EnquiryDetailsManager enquiryDetailsManager= context.getBean(EnquiryDetailsManager.class);
//            Set<EnquiryStatus> enquiryStatusSet = new HashSet<>();
//            enquiryStatusSet.add(EnquiryStatus.ACCEPTED);
//
//            final SearchResultWithPagination<EnquiryDetailsWithFollowup> enquiryDetailsWithPagination = enquiryDetailsManager.getEnquiryDetailsWithPagination(110,UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), 295, enquiryStatusSet, 10, 0, 0);
//            for(EnquiryDetailsWithFollowup enquiryDetailsWithFollowupList : enquiryDetailsWithPagination.getResult()){
//                System.out.println(enquiryDetailsWithFollowupList);
//            }
//            System.out.println(enquiryDetailsWithPagination);
//
//            [23/May/2025 13:51:25] "GET /a0a3ec22-dbf8-11ec-b8bf-120cd1ce09b5/student-management/promotion-configuration/295/6cbe3865-decd-42cb-9e55-243a93e374e4 HTTP/1.1" 200 1942
//            {'instituteId': '110', 'currentSessionId': 255, 'standardId': '6cbe3865-decd-42cb-9e55-243a93e374e4', 'sectionId': '5173', 'promotingStudentList': ['390f7f2b-e150-48db-bec9-4e81c43b8718'], 'feeStructureIdsList': [], 'carryForwardDiscount': True, 'discountStructureIdsList': None, 'carryForwardTransport': True}
//            StudentPromotionDetails studentPromotionDetails = new StudentPromotionDetails(110, 255, UUID.fromString("6cbe3865-decd-42cb-9e55-243a93e374e"), 5173, Arrays.asList(UUID.fromString("390f7f2b-e150-48db-bec9-4e81c43b8718")), null, true, null, true);
//            final boolean promoted = studentAdmissionManager.promoteStudents(110, 255,
//                    studentPromotionDetails, UUID.fromString("dad00d31-fef8-4b12-b9e3-290fefc7501d"));
//            System.out.println(promoted);
//            CalendarManager calendarManager = context.getBean(CalendarManager.class);
//            EventDetails eventManagementPayload = new EventDetails(255, UUID.fromString("d9338d76-a5b8-44ad-b42a-6931a3ad793d"), "new event", "funny games",  1747996176, EventCategory.FESTIVAL, true, null, null, null);
//            boolean s = calendarManager.updateEvent(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), eventManagementPayload);
////            boolean s = calendarManager.deleteEvent(110, 255, UUID.fromString("18639832-77d7-4b5d-8ee2-f3cf38b52385"), UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
////            System.out.println(s);
//            List<EventDetails> eventDetails = calendarManager.getEventDetails(110, 255);
//            System.out.println(eventDetails);

//            Student s = studentManager.getStudentByLatestAcademicSession(UUID.fromString("390f7f2b-e150-48db-bec9-4e81c43b8718"), Arrays.asList(StudentStatus.ENROLLED));
//            System.out.println(s.getStudentAcademicSessionInfoResponse().getHostelDetails());
//            final StudentManagementFieldDetails studentManagementFieldDetails = studentManager.getClassStudentsFieldData(110, 255,
//                  UUID.fromString("f6f78728-93af-42dd-82eb-0a1e934a427e")  , "3724", "HOSTEL", null);
//            for(StudentManagementOutputFieldStudentData studentManagementOutputFieldStudentData : studentManagementFieldDetails.getStudentDataList()) {
//                System.out.println(studentManagementOutputFieldStudentData.getStudent());
//            }

//            StudentManagementUpdateFieldValue studentManagementUpdateFieldValue = new StudentManagementUpdateFieldValue(StudentManagementField.HOSTEL, "");
//            List<StudentManagementUpdateFieldValue> studentManagementUpdateFieldValueList = new ArrayList<>();
//            studentManagementUpdateFieldValueList.add(studentManagementUpdateFieldValue);
//            StudentManagementUpdateFieldStudentData studentManagementUpdateFieldStudentData = new StudentManagementUpdateFieldStudentData(UUID.fromString("7894963d-03bb-4da0-947e-adf5ac3c220c"), studentManagementUpdateFieldValueList);
//            List<StudentManagementUpdateFieldStudentData> studentDataList = new ArrayList<>();
//            studentDataList.add(studentManagementUpdateFieldStudentData);
//            List<StudentManagementField> a = new ArrayList<>();
//            a.add(StudentManagementField.HOSTEL);
//            StudentManagementUpdateFieldPayload payload = new StudentManagementUpdateFieldPayload((StudentManagementDataType.SESSION_DATA), a, studentDataList);

//            ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//
//            StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//
//
//            final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(110,
//                    "STAFF_ATTENDANCE_DETAILS_IN_A_DAY", null, null,
//                    "ONBOARD", 1747333800, 1747420200, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//                    DownloadFormat.PDF);
//
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST3 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);
//            HostelManagementManager hostelManagementManager = context.getBean(HostelManagementManager.class);
////            HostelDetails hostelDetails = new HostelDetails(110, null, "Hostel 3", "H3", null, null, null, null);
////            final boolean success = hostelManagementManager.deleteHostelDetails(110, UUID.fromString("dad00d31-fef8-4b12-b9e3-290fefc7501d"), null);
////            System.out.println(success);

//            StudentManager studentManager = context.getBean(StudentManager.class);
//
//            final StudentManagementFieldDetails studentManagementFieldDetails = studentManager.getClassStudentsFieldData(110, 255,
//                  UUID.fromString("f6f78728-93af-42dd-82eb-0a1e934a427e")  , "3724", "HOSTEL", null);
//            for(StudentManagementOutputFieldStudentData studentManagementOutputFieldStudentData : studentManagementFieldDetails.getStudentDataList()) {
//                System.out.println(studentManagementOutputFieldStudentData.getStudent());
//            }


//            StudentManagementUpdateFieldValue studentManagementUpdateFieldValue = new StudentManagementUpdateFieldValue(StudentManagementField.IS_SPECIALLY_ABLED, "Yes");
//            List<StudentManagementUpdateFieldValue> studentManagementUpdateFieldValueList = new ArrayList<>();
//            studentManagementUpdateFieldValueList.add(studentManagementUpdateFieldValue);
//            StudentManagementUpdateFieldStudentData studentManagementUpdateFieldStudentData = new StudentManagementUpdateFieldStudentData(UUID.fromString("17bae370-4ad2-4223-b332-3c1cf1bc5308"), studentManagementUpdateFieldValueList);
//            List<StudentManagementUpdateFieldStudentData> studentDataList = new ArrayList<>();
//            studentDataList.add(studentManagementUpdateFieldStudentData);
//            List<StudentManagementField> a = new ArrayList<>();
//            a.add(StudentManagementField.IS_SPECIALLY_ABLED);
//            StudentManagementUpdateFieldPayload payload = new StudentManagementUpdateFieldPayload((StudentManagementDataType.BASIC_DATA), a, studentDataList);
//            StudentManager studentManager= context.getBean(StudentManager.class);
//            final boolean success = studentManager.updateClassStudentsFieldData(10355, 237,
//                    UUID.fromString("c63a883c-05a7-4f97-ae10-a9a0b7e5b5c4"), null, UUID.fromString("1755b257-3ee5-4bd2-858e-7962dbd2144d"), payload, true);
//            System.out.println(success);
//            PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
 //           ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
 //           int instituteId = 110;
 /*           int academicSessionId = 255;
            UUID userId = UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51");
            StudentReportType studentReportType = StudentReportType.STUDENT_DETAILS_REPORT;
            DownloadFormat downloadFormat = DownloadFormat.EXCEL;
            String requiredStandards = "f6f78728-93af-42dd-82eb-0a1e934a427e";
            String studentStatus = null;
            String documentTypes = null;
            StudentDocumentStatus documentStatus = null;
//            String requiredHeaders = "serial_number,student_admission_number,student_session_status,student_name,student_father_name,student_mother_name,student_class_name,student_house_name,student_admission_date,student_dob,student_gender,student_primary_contact_number,student_category,student_religion,student_caste,student_rte,student_specially_abled,student_specially_abled_type,student_bpl,student_registration_number,student_relieve_date,student_birth_place,student_aadhar_number,student_nationality,student_mother_tongue,student_area_type,student_permanent_address,student_present_address,student_city,student_state,student_zipcode,student_present_city,student_present_state,student_present_zipcode,student_primary_email,student_sponsored,student_whatsapp_number,student_admission_in_class,student_present_post_office,student_permanent_post_office,student_present_police_station,student_permanent_police_station,student_academic_session";
            String requiredHeaders = "serial_number,tc_student_name,remarks,student_tc_number,student_affiliation_number,student_dise_code,student_book_number,student_school_code,student_rte_affiliation_number,tc_student_admission_number,tc_student_admission_date,tc_student_admission_class,tc_student_father_or_guardian_name,tc_student_mother_name,tc_student_dob,student_dob_proof,tc_student_category,tc_student_nationality,student_pen,student_apaar_id,last_active_session_class,last_exam_taken_result,failed_status,subjects_last_session,qualified_for_promotion,last_fees_paid,discount_details,total_working_days,total_attended_days,ncc_scout_guide_details,co_curricular_activities,code_of_conduct,tc_generation_date,relieve_date,relieve_reason,certificate_application_date,name_struck_off_date";

            FilterationCriteria filterationCriteria = new FilterationCriteria();
            boolean newAdmission = false;
            final StudentReportsGenerator studentReportsGenerator = context.getBean(StudentReportsGenerator.class);
            final ReportDetails reportDetails = studentReportsGenerator.generateReport(instituteId,
                    academicSessionId, userId, studentReportType, downloadFormat, requiredStandards,
                    studentStatus, studentStatus, documentTypes, documentStatus, requiredHeaders, filterationCriteria, newAdmission);
            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
                        final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST5 + reportOutput.getReportName()));
            reportOutput.getReportContent().writeTo(outputStream);*/
//            InstituteManager instituteManager = context.getBean(InstituteManager.class);
//            Institute existingInstitute = instituteManager.getInstitute(10600);
//            System.out.println(existingInstitute);

//            "bd84ccff-c119-49fd-8f02-34ece1707334,69aec7cb-6d30-4778-8f64-ee04a6487a76,e9d4f686-3e24-420d-bd51-b5aa19e9c34d,bcbfff78-1751-4703-b825-d04e21806014"
//            final ReportDetails reportDetails = homeworkReportGenerator.generateReport(101, 256, null, null, null, DownloadFormat.EXCEL, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), HomeworkReportType.HOMEWORK_CONSOLIDATED_REPORT,  1745692200, 1746383400);
//            System.out.println(reportDetails);
//            if(reportDetails == null){
//                throw new ApplicationException(
//                        new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to render report"));
//            }
//            HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//            System.out.println(htmlReportTable);

//            Set<Integer> sectionIdSet = StringHelper.convertStringToSetInteger("3473,3474");
//            Set<UUID> courseIdSet = StringHelper.convertStringToSetUUID("bd84ccff-c119-49fd-8f02-34ece1707334,69aec7cb-6d30-4778-8f64-ee04a6487a76,e9d4f686-3e24-420d-bd51-b5aa19e9c34d,bcbfff78-1751-4703-b825-d04e21806014");
//            HomeworkManager homeworkManager = context.getBean(HomeworkManager.class);
//            final List<HomeworkDetails> HomeworkDetailsList = homeworkManager.getHomeworkDetailsByFilters(101,
//                    256, UUID.fromString("10523d7c-2c13-4d56-8dc8-187c0a3f5b1f"), sectionIdSet, 1746210600, 1746210600, courseIdSet, null, null, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//            System.out.println(HomeworkDetailsList);
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//                        final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST1 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);

//            final ApplicationContext context = new ClassPathXmlApplicationContext("core-lib.xml");
//            InstituteSetupManager instituteSetupManager = context.getBean(InstituteSetupManager.class);
//            InstituteManager instituteManager = context.getBean(InstituteManager.class);
//            Institute existingInstitute = instituteManager.getInstitute(10600);
//            System.out.println(existingInstitute);
//            InstituteBankAccountDetails instituteBankAccountDetailsPayload = new InstituteBankAccountDetails();
//            instituteBankAccountDetailsPayload.setInstituteId(101);
//            instituteBankAccountDetailsPayload.setAccountType(AccountType.SAVINGS);
//            instituteBankAccountDetailsPayload.setAccountId(UUID.fromString("73b1f7ac-fa54-40cd-9119-716392ddaf3e"));
//            instituteBankAccountDetailsPayload.setAccountHolderName("noor");
//            instituteBankAccountDetailsPayload.setAccountId(UUID.randomUUID());
//            instituteBankAccountDetailsPayload.setBankName("fghj");
//            instituteBankAccountDetailsPayload.setBranchName("brash market");
//            instituteBankAccountDetailsPayload.setAccountNumber("**********");
//            instituteBankAccountDetailsPayload.setPrimary(false);
//            instituteBankAccountDetailsPayload.setActive(true);
//
//            final UUID accountId = instituteManager.addInstituteBankAccount(101, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), instituteBankAccountDetailsPayload);
//            final List<InstituteBankAccountDetails> instituteBankAccountDetailsList = instituteManager.getInstituteBankAccounts(101);
//            final InstituteBankAccountDetails instituteBankAccountDetails = instituteManager.getInstituteBankAccount(101, UUID.fromString("73b1f7ac-fa54-40cd-9119-716392ddaf3e"));
//            final boolean updateInstituteBankAccountDetails = instituteManager.updateInstituteBankAccountDetails(101, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), instituteBankAccountDetailsPayload);
//            final boolean deleteBankAccount = instituteManager.deleteInstituteBankAccount(101, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), UUID.fromString("73b1f7ac-fa54-40cd-9119-716392ddaf3e"));

//            System.out.println(accountId);

//final ConfigurationManager configurationManager = context.getBean(ConfigurationManager.class);
//List<String> string = configurationManager.configValueList("institute_unique_code", Arrays.asList("sesomu", "bjcs10", "avm", "aic", "bfss"));
//System.out.println(string);
//instituteSetupManager.validateInstituteUniqueCode(Arrays.asList("sesomu", "bjcs10", "avm", "aic", "bfss"));

			/* final StaffManager staffManager = context.getBean(StaffManager.class); */
//            final AssessmentDetailsManager assessmentDetailsManager = context.getBean(AssessmentDetailsManager.class);
//            //AssessmentDetailsWithQuestions assessmentDetailsForStudent = assessmentDetailsManager.getAssessmentDetailsForStudent(101, 256, UUID.fromString("52cbd4d5-f0f3-460d-a3e9-09c39d9e854b"), UUID.fromString("0849f0d1-83c4-4500-97cd-9a63a4b40566"));
//            boolean assessmentAttempted = assessmentDetailsManager.getStudentAssessmentAttempted(101, 256, UUID.fromString("0849f0d1-83c4-4500-97cd-9a63a4b40566"), UUID.fromString("52cbd4d5-f0f3-460d-a3e9-09c39d9e854b"));
//            System.out.println(assessmentAttempted);


//            final UserPermissionManager userPermissionManager = context.getBean(UserPermissionManager.class);
//            List<User> adminUserList = userPermissionManager.getUserByUserPermission(110, new HashSet<>(Arrays.asList(AuthorisationRequiredAction.ACCESS_ALL_CLASSES_STUDENT_LEAVE_DATA)));
//

//            FeeConfigurationManager feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
//            FeeConfigurationBasicInfo feeConfigurationBasicInfo = new FeeConfigurationBasicInfo();
//            feeConfigurationBasicInfo.setFeeName("Annual function");
//            feeConfigurationBasicInfo.setDueDate(1744543159);
//            feeConfigurationBasicInfo.setInstituteId(101);
//            feeConfigurationBasicInfo.setAcademicSessionId(256);
//            feeConfigurationBasicInfo.setAllowPendingEnrollment(false);
//            feeConfigurationBasicInfo.setFineApplicable(false);
//            feeConfigurationBasicInfo.setTransferToWallet(true);
//            final Boolean addedOneTimeFeeConfiguration = feeConfigurationManager
//                    .addOneTimeFeeConfiguration(feeConfigurationBasicInfo, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//            System.out.println(addedOneTimeFeeConfiguration);
//            FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
//            FeeCancelledPayload feeCancelledPayload = new FeeCancelledPayload();
//            feeCancelledPayload.setTransactionId(UUID.fromString("ac7e25f6-28a1-4513-a2c8-a01b2eb7e87b"));
//            feeCancelledPayload.setInstituteId(101);
//            feeCancelledPayload.setCancelBy(UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"));
//            final boolean cancelSuccess = feePaymentManager.cancelFeePaymentTransactionWithPayload(101, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), feeCancelledPayload);

//            FeePaymentPayload feePaymentPayload = new FeePaymentPayload();
//            FeePaymentTransactionMetaData feePaymentTransactionMetaData = new FeePaymentTransactionMetaData();
//            feePaymentTransactionMetaData.setFeePaymentTransactionStatus(FeePaymentTransactionStatus.ACTIVE);
//            feePaymentTransactionMetaData.setAcademicSessionId(256);
//            feePaymentTransactionMetaData.setInstituteId(101);
//            feePaymentTransactionMetaData.setStudentId(UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"));
//            feePaymentTransactionMetaData.setTransactionMode(TransactionMode.CARD);
//
//            List<FeeIdFeeHeadTransaction> feeIdFeeHeadTransactionList = new ArrayList<>();
//            FeeIdFeeHeadTransaction feeIdFeeHeadTransaction = new FeeIdFeeHeadTransaction();
//            feeIdFeeHeadTransaction.setFeeId(UUID.fromString("163a9209-4fe2-4bf5-896b-3e918bfe863b"));
//            List<FeeHeadTransactionAmounts> feeHeadTransactionAmounts = new ArrayList<>();
//            FeeHeadTransactionAmounts feeHeadTransactionAmounts1 = new FeeHeadTransactionAmounts();
//            feeHeadTransactionAmounts1.setFeeHeadId(680);
//            feeHeadTransactionAmounts1.setPaidAmount(2200);
//            feeHeadTransactionAmounts.add(feeHeadTransactionAmounts1);
//
//            feeIdFeeHeadTransaction.setFeeHeadTransactionAmounts(feeHeadTransactionAmounts);
//            feeIdFeeHeadTransactionList.add(feeIdFeeHeadTransaction);
//            feePaymentPayload.setFeePaymentTransactionMetaData(feePaymentTransactionMetaData);
//            feePaymentPayload.setFeeIdFeeHeadTransactionList(feeIdFeeHeadTransactionList);
//
//            final FeePaymentResponse feePaymentResponse = feePaymentManager.addFeePayment(feePaymentPayload, false, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), false);
//            System.out.println(feePaymentResponse);

//            final FeePaymentResponse addSuccess = feePaymentManager.addFeePaymentWithoutTransaction(feePaymentPayload, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), false, Module.FEES);
//            UserWalletManager userWalletManager = context.getBean(UserWalletManager.class);
//            WalletTransactionPayload walletTransactionPayload = new WalletTransactionPayload(101, UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e"), UserType.STUDENT, WalletTransactionCategory.FEE_PAYMENT, TransactionMode.CASH,
//                    null, FeePaymentTransactionStatus.ACTIVE, 1000, UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693"), null, null);
//
//            /**
//             * Add amount to the user's wallet and update its transaction history.
//             */
//            UUID response = userWalletManager.addUserWalletTransactionNonAtomic(walletTransactionPayload);
//
//            InstituteManager instituteManager = context.getBean(InstituteManager.class);
//            final boolean updated = instituteManager.updateInstituteActiveStatus(1010, null, true, true);
//
//            System.out.println(updated);

            //            /2.0/examination/4955b990-5e80-4b85-8207-906ee1c8b3b5/course/92686790-d441-4850-bc05-239bf16c317d/effective-marks?institute_id=10365&section_id=3330&add_relieved_students=false&user_id=72b07226-3011-4ee0-a19a-c39650ffeb23&academic_session_id=243&standard_id=1c483652-520a-49d5-a58e-77c27429ced0

//            ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
////            PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
//            int academicSessionId = 261;
//            UUID standardId = UUID.fromString("b8cb7fd3-99ec-4008-a941-f2d6a8054640");
//            Integer sectionId = null;
//            UUID examId = UUID.fromString("69599288-ea0d-48c0-b392-829d574ead6a");
//            UUID courseId = null;
//            CourseType courseType = null;
//            DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//            String reportCardType = null;
//            UUID staffId = null;
//
//            // Use Collections.emptySet() for empty sets
//            Set<Integer> sectionIdSet = Collections.emptySet();
//            Set<UUID> examIdSet = new HashSet<>(Arrays.asList(examId));
//            Set<UUID> courseIdSet = Collections.emptySet();
//            Set<UUID> compareCummulativeWithExamIdSet = Collections.emptySet();
//            Set<UUID> additionalCoursesSet = Collections.emptySet();
//            Set<MarksDisplayType> scholasticMarksDisplayTypeSet = Collections.singleton(MarksDisplayType.MARKS);
//            Set<MarksDisplayType> coScholasticMarksDisplayTypeSet = Collections.singleton(MarksDisplayType.MARKS);
//o
//            Integer rankTill = null;
//            boolean excludeCoScholasticSubjects = false;
//            String requiredHeaders = "sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks";
//            boolean isSortStudentOnRank = false;
//            boolean showClassAverageDetails = false;
//            boolean showStaffDetails = false;
//
//            // Creating an instance of ExamReportFiltrationCriteria
//            ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//                    academicSessionId, standardId, sectionId, examId, courseId, courseType,
//                    downloadFormat, reportCardType, staffId, sectionIdSet, examIdSet,
//                    courseIdSet, compareCummulativeWithExamIdSet, rankTill, excludeCoScholasticSubjects,
//                    requiredHeaders, isSortStudentOnRank, additionalCoursesSet,
//                    scholasticMarksDisplayTypeSet, coScholasticMarksDisplayTypeSet,
//                    showClassAverageDetails, showStaffDetails
//            );
//
//            final ReportDetails reportDetails = examReportGenerator.generateReport(10385,
//                    ExamReportType.MULTIPLE_EXAM_MARKS_REPORT, UUID.fromString("be7e6db1-ac47-4d0c-b51c-027cc7daadbc"),
//                    examReportFiltrationCriteria);
//            ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//
//
//            final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST5 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);
//            System.out.println(adminUserList.size());
//            HPCFormManager hpcFormManager = context.getBean(HPCFormManager.class);
//
//            boolean present = hpcFormManager.getFormByUserType(110, 153,
//                    UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d"), HPCUserType.PARENT);
//
//            System.out.println(present);
//            String string = "XI-SCIENCE(BIO), XI-SCIENCE(MATHS), XI-COMMERCE(MATHS), XI-COMMERCE, XI-HUMANITIES";
//            Set<String> stringSet =  StringHelper.convertStringToSetString(string);
//            System.out.println(stringSet);


           /*  final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
            final ExamGraphicalReportGenerator examGraphicalReportGenerator = context.getBean(ExamGraphicalReportGenerator.class);

            ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
                    194,
                    UUID.fromString("de0f2f1a-86f3-4a03-aa9c-b20ffa70d1f2"),
                    null,
                    UUID.fromString("2ed0aaf2-e0f5-4c9d-a4a8-d63b70c40dc5"),
                    null,
                    null,
                    null,
                    "",
                    null,
                    null,
                    new HashSet<>(Arrays.asList(UUID.fromString("2ed0aaf2-e0f5-4c9d-a4a8-d63b70c40dc5"))),
                    null,
                    null,
                    100,
                    false,
                    "",
                    false,
                    null,
                    null,
                    null,
                    false,
                    false
            ); */

//            final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//            final ExamGraphicalReportGenerator examGraphicalReportGenerator = context.getBean(ExamGraphicalReportGenerator.class);
//
//            ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//                    194,
//                    UUID.fromString("de0f2f1a-86f3-4a03-aa9c-b20ffa70d1f2"),
//                    null,
//                    UUID.fromString("2ed0aaf2-e0f5-4c9d-a4a8-d63b70c40dc5"),
//                    null,
//                    null,
//                    null,
//                    "",
//                    null,
//                    null,
//                    new HashSet<>(Arrays.asList(UUID.fromString("2ed0aaf2-e0f5-4c9d-a4a8-d63b70c40dc5"))),
//                    null,
//                    null,
//                    100,
//                    false,
//                    "",
//                    false,
//                    null,
//                    null,
//                    null,
//                    false,
//                    false
//            );

//            ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//                    243,
//                    UUID.fromString("1c483652-520a-49d5-a58e-77c27429ced0"),
//                    null,
//                    UUID.fromString("8eebb0d7-b5c9-4392-9d14-a89ffcd53a01"),
//                    null,
//                    null,
//                    null,
//                    "",
//                    null,
//                    null,
//                    new HashSet<>(Arrays.asList(UUID.fromString("8eebb0d7-b5c9-4392-9d14-a89ffcd53a01"))),
//                    null,
//                    null,
//                    100,
//                    false,
//                    "",
//                    false,
//                    null,
//                    null,
//                    null,
//                    false,
//                    false

//            );

          /*   final GraphicalReportDetails graphicalReportDetails = examGraphicalReportGenerator.generateGraphicalReport(10225,
                    ExamReportType.GROWTH_REPORT, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), examReportFiltrationCriteria);
//            final ReportDetails graphicalReportDetails = examReportGenerator.generateReport(10225,
//                    ExamReportType.SUBJECT_WISE_RANK_REPORT, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), examReportFiltrationCriteria);
System.out.println(graphicalReportDetails); */


//            final GraphicalReportDetails graphicalReportDetails = examGraphicalReportGenerator.generateGraphicalReport(10225,
//                    ExamReportType.GROWTH_REPORT, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), examReportFiltrationCriteria);
//            final ReportDetails graphicalReportDetails = examReportGenerator.generateReport(10225,
//                    ExamReportType.SUBJECT_WISE_RANK_REPORT, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), examReportFiltrationCriteria);
//System.out.println(graphicalReportDetails);

//            ReportOutput reportOutput = excelReportGenerator.generateReport(graphicalReportDetails);
//

//            List<Standard> standardList = studentRegistrationManager.getFilteredStandardsWithoutSection(10390, 0);
//            for(Standard standard : standardList){
//                System.out.println(standard);
//            }
//            final StudentManager studentManager = context.getBean(StudentManager.class);
//            List<StudentSearchEntry> studentSearchEntryList = studentManager.liteV2SearchStudentsInAcademicSession(10225, 110, "A", Arrays.asList(StudentStatus.ENROLLED));
//            for(StudentSearchEntry studentSearchEntry : studentSearchEntryList) {
//                System.out.println(studentSearchEntry.toString());
//            }

//            final TransportConfigurationManager transportConfigurationManager = context.getBean(TransportConfigurationManager.class);
//            List<VehicleDocumentRenewalNotificationInfo> vehicleDocumentRenewalNotificationInfoList = transportConfigurationManager.getVehicleWithRenewalDate(30);
//            if(CollectionUtils.isEmpty(vehicleDocumentRenewalNotificationInfoList)) {
//                System.out.println("No vehicle with renewal date in next 30 days");
//            }
//
//            System.out.println(vehicleDocumentRenewalNotificationInfoList.size());

//
//
//            final OutputStream outputStream = Files.newOutputStream(Paths.get(DEST5 + reportOutput.getReportName()));
//            reportOutput.getReportContent().writeTo(outputStream);
//            TransportSetupUtilityManager transportSetupUtilityManager = context.getBean(TransportSetupUtilityManager.class);
//            Set<String> set = new HashSet<>();
////            set.add("TOGARA MARG");
////            set.add("Good Morning");
//            Set<RouteType> routeTypeSet = new HashSet<>();
//            routeTypeSet.add(RouteType.DROP_OFF);
//            routeTypeSet.add(RouteType.PICK_UP);
//            CloneTransportServiceRoutes cloneTransportServiceRoutes = new CloneTransportServiceRoutes(10225, 10226, 194, 195, set, routeTypeSet);
//            boolean status = transportSetupUtilityManager.cloneTransportRoutesAcrossSession(cloneTransportServiceRoutes);
//            System.out.println(status);


//            List<User> receiveNotificationUsers = userPermissionManager.getUserByUserPermission(null,
//                    new HashSet<>(Collections.singletonList(AuthorisationRequiredAction.RECEIVE_VEHICLE_DOCUMENT_RENEWAL_REMINDER)));
//
//            Map<Integer, List<User>> filteredInstituteWiseUsers = new HashMap<>();
//            for(User user : receiveNotificationUsers) {
//                if (user == null || user.getInstituteId() <= 0 || !user.getUserStatus().equals(UserStatus.ENABLED)) {
//                    continue;
//                }
//                if(filteredInstituteWiseUsers.containsKey(user.getInstituteId())) {
//                    filteredInstituteWiseUsers.put(user.getInstituteId(), new ArrayList<>());
//                }
//                filteredInstituteWiseUsers.get(user.getInstituteId()).add(user);
//            }

//            final SearchResultWithPagination<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
//                    .searchFeePaymentTransactions(10160, null, 198, FeePaymentTransactionStatus.ACTIVE,
//                            0, 10, true);

//            final List<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
//                    .getLastNTransactions(10160, 198, FeePaymentTransactionStatus.ACTIVE, 10);

//            List<FeePaymentTransactionStatus> feePaymentTransactionStatusList = new ArrayList<FeePaymentTransactionStatus>();
//            feePaymentTransactionStatusList.add(FeePaymentTransactionStatus.ACTIVE);
//            feePaymentTransactionStatusList.add(FeePaymentTransactionStatus.CANCELLED);
//            final List<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
//                    .getFeePaymentTransactions(10160, UUID.fromString("c46a847e-03d9-40f8-9378-8a950ba1b183"), feePaymentTransactionStatusList);

            /* final List<Staff> staff = staffManager.getStaff(101);

            System.out.println(staff); */

//            final List<Staff> staff = staffManager.getStaff(101);

//
//            final List<VisitorDetails> visitorDetailsList = visitorDetailsManager.getVisitorDetails(101,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, null, 100, 0);
//            final boolean updateVisitorStatus = visitorDetailsManager.updateVisitorStatus(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, UUID.fromString("657b1e61-ea53-499e-a484-2349c837ea79"), VisitorStatus.APPROVED, null, null);
//
//            final ExamConfigManager examConfigManager = context.getBean(ExamConfigManager.class);
//
//            ExamStructureCoursesResponse updated = examConfigManager.cloneClassExamStructure(new CloneClassExamRequest(101,10390,190,228,"III", new ArrayList<>(Arrays.asList("VI")), false, false));
//
//           System.out.println(updated);
//            final boolean updateVisitorStatus = visitorDetailsManager.updateVisitorStatus(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, UUID.fromString("657b1e61-ea53-499e-a484-2349c837ea79"), VisitorStatus.APPROVED, null, null);
//
//            final StudentManager studentManager = context.getBean(StudentManager.class);

//        String json = "{\"instituteId\": 110, \"academicSessionId\": 255, \"standardId\": \"4f3a8bbd-5b57-40d9-8616-a1e62039a02c\", \"studentAcademisSessionParametersList\": [\"ROLL_NUMBER\"], \"studentAcademicDetailsList\": [{\"studentId\": \"00afd901-01f4-450e-aef3-31ec45099d02\", \"rollNumber\": \"STUID1\"}]}";
//
//        String json = "{\"instituteId\": 110, \"academicSessionId\": 255, \"studentAcademisSessionParametersList\": [\"ROLL_NUMBER\"], \"studentAcademicDetailsList\": [{\"studentId\": \"00afd901-01f4-450e-aef3-31ec45099d02\", \"rollNumber\": \"STUID1\"}]}";
//
//        , {"studentId": "01821281-8b6b-4c1c-af7f-dba5b01ae4ba", "rollNumber": "STUID2"}, {"studentId": "06a02299-013f-4dfc-93b5-342ba87f5c78", "rollNumber": "STUID3"}, {"studentId": "06ca73c8-0663-4cae-b01b-9c0779dee9d8", "rollNumber": "STUID4"}, {"studentId": "07f56df2-f04e-43b9-a453-cbbccb6a84e2", "rollNumber": "STUID5"}, {"studentId": "08eb830c-45dc-43a2-bc96-d14334d07977", "rollNumber": "STUID6"}, {"studentId": "09b9d973-cf66-4a67-834c-51a96aadd33e", "rollNumber": "STUID7"}, {"studentId": "0ab83126-47bc-43e0-8210-5648be2353cc", "rollNumber": "STUID8"}, {"studentId": "0b3f0f4c-86cc-426c-88e0-0c4cae8a8793", "rollNumber": "STUID9"}, {"studentId": "0f6a88fc-3b0d-4013-ad5d-e0a884f86f41", "rollNumber": "STUID10"}, {"studentId": "101cebf8-ab78-4d0b-a4d7-da8eaf683b7e", "rollNumber": "STUID11"}, {"studentId": "104d7ca0-4287-4c27-9e6f-0c044d00fa49", "rollNumber": "STUID12"}, {"studentId": "10fd11b9-a95a-4275-be68-9c51300617f9", "rollNumber": "STUID13"}, {"studentId": "1164295c-14d5-4cf6-bf60-a166463ed758", "rollNumber": "STUID14"}, {"studentId": "132df7b6-db87-4ca2-8cf9-1ff314ba26a0", "rollNumber": "STUID15"}, {"studentId": "13ad413e-8c2c-4584-82bd-a966807b5d58", "rollNumber": "STUID16"}, {"studentId": "1525cd68-fcba-45b7-858b-785cb80ec1f9", "rollNumber": "STUID17"}, {"studentId": "1762e0dc-285f-4e06-b602-ff725dbad86d", "rollNumber": "STUID18"}, {"studentId": "18ca658e-c395-4614-bf2e-f22186983e72", "rollNumber": "STUID19"}, {"studentId": "18fd7074-7bc5-442a-a530-e59e99762759", "rollNumber": "STUID20"}, {"studentId": "19d25716-79c5-4026-8229-0637d6fdaf98", "rollNumber": "STUID21"}, {"studentId": "1fc19128-08b2-4349-bd14-47092d31ee24", "rollNumber": "STUID22"}, {"studentId": "20e97db7-dd2c-41fe-b1da-5244b16747f2", "rollNumber": "STUID23"}, {"studentId": "223b832c-bb8b-4303-a633-9521d2c9643c", "rollNumber": "STUID24"}, {"studentId": "22cd0927-74bb-41f5-9a3d-065e428e6042", "rollNumber": "STUID25"}, {"studentId": "283ca52f-a238-435a-8511-9f957fb85b98", "rollNumber": "STUID26"}, {"studentId": "2bf15a17-502a-4380-be19-4b92cd6a95d5", "rollNumber": "STUID27"}, {"studentId": "309f0487-4700-4309-91e6-ad156b0db664", "rollNumber": "STUID28"}, {"studentId": "325bb1e6-826c-49b7-96cc-dd4f7275829f", "rollNumber": "STUID29"}, {"studentId": "37bb5c06-37d8-4378-b273-32a40b76ec57", "rollNumber": "STUID30"}, {"studentId": "390f7f2b-e150-48db-bec9-4e81c43b8718", "rollNumber": "STUID31"}, {"studentId": "3da28293-3def-4221-94ff-cc967333efcf", "rollNumber": "STUID32"}, {"studentId": "414d2699-092c-4cdb-b189-2ea68b4ca666", "rollNumber": "STUID33"}, {"studentId": "44fcef60-6b25-4098-abb2-78a2721f3a78", "rollNumber": "STUID34"}, {"studentId": "48ff1961-6d14-4ee1-885d-30560d373b07", "rollNumber": "STUID35"}, {"studentId": "49f102b9-8e26-468f-a991-95143f3823ab", "rollNumber": "STUID36"}, {"studentId": "501dabcb-f307-47b0-9e29-cad879cd29c5", "rollNumber": "STUID37"}, {"studentId": "5051d2a6-e9b3-41e5-9852-67e567e56888", "rollNumber": "STUID38"}, {"studentId": "50ed95c9-c24e-4787-813a-d77a884e2203", "rollNumber": "STUID39"}, {"studentId": "53730b37-0217-427f-8ecc-68e12a0380b9", "rollNumber": "STUID40"}, {"studentId": "55fbc861-2d01-4e9e-b6ed-a13f6ed532cc", "rollNumber": "STUID41"}, {"studentId": "5746bbaa-cfaa-4813-ac70-44b28899de32", "rollNumber": "STUID42"}, {"studentId": "5e1122c0-8afd-4f65-8538-bc4314467aa3", "rollNumber": "STUID43"}, {"studentId": "61f614c6-5dd5-4ba2-a173-68efac1ce8f6", "rollNumber": "STUID44"}, {"studentId": "628911d3-1beb-442b-a83b-c05dba91f8c7", "rollNumber": "STUID45"}, {"studentId": "654f7bf3-f7a0-4ba7-b7e0-0320fc921d3b", "rollNumber": "STUID46"}, {"studentId": "6668a630-ec80-4f2c-bd35-b5ef68417697", "rollNumber": "STUID47"}, {"studentId": "*************-46ef-952f-26aba43ac05c", "rollNumber": "STUID48"}, {"studentId": "6b602762-c7e7-4a1a-a7af-ddf682576172", "rollNumber": "STUID49"}, {"studentId": "6bdff490-53e8-41ae-b332-c16606021ab7", "rollNumber": "STUID50"}, {"studentId": "6bf02553-980b-449d-95f9-bb6c261f77b3", "rollNumber": "STUID51"}, {"studentId": "7216d433-56f2-4784-89e5-79b4d415f32a", "rollNumber": "STUID52"}, {"studentId": "735021d1-b706-418e-998b-9f5d774bac89", "rollNumber": "STUID53"}, {"studentId": "77753aa6-7d08-4456-897c-d6033e956042", "rollNumber": "STUID54"}, {"studentId": "77ea3aef-e2c2-48e9-b2cb-eef52e4ddeac", "rollNumber": "STUID55"}, {"studentId": "7894963d-03bb-4da0-947e-adf5ac3c220c", "rollNumber": "STUID56"}, {"studentId": "78eeed97-f00a-46b6-ab23-325abdb64cab", "rollNumber": "STUID57"}, {"studentId": "78f1e0ba-d495-48f8-a593-01a7a3d2bdc0", "rollNumber": "STUID58"}, {"studentId": "79574598-9dfb-4d47-924c-8168c9a1f6e4", "rollNumber": "STUID59"}, {"studentId": "7af49516-ebff-43f2-83c1-dbdbe32169a5", "rollNumber": "STUID60"}, {"studentId": "7dd15326-3354-4c8c-8ac2-4873e6a362bd", "rollNumber": "STUID61"}, {"studentId": "83e51b02-e9c0-42bf-8e7a-55dc182f35be", "rollNumber": "STUID62"}, {"studentId": "87f40b38-d429-4fd8-912d-1ca08377e978", "rollNumber": "STUID63"}, {"studentId": "8a9a6d9a-667a-42cb-8c3f-2f25ba3183a3", "rollNumber": "STUID64"}, {"studentId": "8bd5abcf-5885-4328-9c3c-11811ce2f6b4", "rollNumber": "STUID65"}, {"studentId": "9020b508-23c5-487f-86ba-143a74302108", "rollNumber": "STUID66"}, {"studentId": "90dc84c2-5cc6-4384-93ed-3674f44bf044", "rollNumber": "STUID67"}, {"studentId": "915682b0-97b0-438b-8a62-c9834013749e", "rollNumber": "STUID68"}, {"studentId": "919325df-81e8-4468-b366-10ee384186d9", "rollNumber": "STUID69"}, {"studentId": "92927897-300f-4e45-9837-6d8ac112a253", "rollNumber": "STUID70"}, {"studentId": "976a9689-85ee-4483-b936-346e1b4e69c7", "rollNumber": "STUID71"}, {"studentId": "9af3dc00-4d3f-4448-bd91-e98319c04dc6", "rollNumber": "STUID72"}, {"studentId": "9b31db18-6aab-4872-9b27-9b888450396d", "rollNumber": "STUID73"}, {"studentId": "9b6647aa-f200-4034-81a0-199a4048a599", "rollNumber": "STUID74"}, {"studentId": "9bab5185-027c-4089-925b-667fc775909b", "rollNumber": "STUID75"}, {"studentId": "9c4c492b-0ef5-4848-a79a-58e836bdd229", "rollNumber": "STUID76"}, {"studentId": "9fe349d1-edbd-4fb7-936e-1683e6625148", "rollNumber": "STUID77"}, {"studentId": "a05b99c1-8f2c-4e47-9351-a87ce4f8b759", "rollNumber": "STUID78"}, {"studentId": "a32decce-2d67-4fdc-a09f-da319931fcb5", "rollNumber": "STUID79"}, {"studentId": "a353d78a-8538-4006-b8af-6ea09849b3e7", "rollNumber": "STUID80"}, {"studentId": "a4e88874-dabb-4d45-8e97-7522ab4287b5", "rollNumber": "STUID81"}, {"studentId": "a574db85-1b28-49ab-b0bb-df4c1dfc5cfd", "rollNumber": "STUID82"}, {"studentId": "a59c0f40-c150-41ca-b6ad-74e4e8439c17", "rollNumber": "STUID83"}, {"studentId": "a70fc122-e0d2-4742-a3ff-654d7404e05e", "rollNumber": "STUID84"}, {"studentId": "ab162b72-1f4c-444c-b2fd-90c1c3fe5a82", "rollNumber": "STUID85"}, {"studentId": "ab25c913-8217-4e76-ad34-a1810fa7ca43", "rollNumber": "STUID86"}, {"studentId": "acbe2b02-3a68-4818-a0a8-727c73e31495", "rollNumber": "STUID87"}, {"studentId": "adfcd386-b00f-4642-af43-9b40bb302619", "rollNumber": "STUID88"}, {"studentId": "b106d637-22ae-460f-ac30-ca21da954017", "rollNumber": "STUID89"}, {"studentId": "b64ceed5-177f-4678-88bc-db50e4d9fe71", "rollNumber": "STUID90"}, {"studentId": "c2d2dbc4-77dc-4bf7-a865-b40285f40858", "rollNumber": "STUID91"}, {"studentId": "c494172b-4207-4df8-b6e2-1365b67ac96e", "rollNumber": "STUID92"}, {"studentId": "cd9bdfd7-d425-4a70-865f-d653e2cd895e", "rollNumber": "STUID93"}, {"studentId": "d154fb7d-5fc6-44ea-921a-fbb4b5be6ace", "rollNumber": "STUID94"}, {"studentId": "d1902c2d-bee3-48b2-b428-6ce91ffd6de2", "rollNumber": "STUID95"}, {"studentId": "d2591346-4bba-404c-8dbb-af467944ff0f", "rollNumber": "STUID96"}, {"studentId": "d2cc06fb-b6b2-43c7-9158-6f5793dba530", "rollNumber": "STUID97"}, {"studentId": "d344eef9-e3eb-4e12-a518-6808714a701c", "rollNumber": "STUID98"}, {"studentId": "d688a8ad-e113-49f5-84d7-756e324e11da", "rollNumber": "STUID99"}, {"studentId": "d6b819ec-cb89-4867-ad9a-acedb2d84043", "rollNumber": "STUID100"}, {"studentId": "da48418b-b6f0-4278-a7a4-edf61d207cc4", "rollNumber": "STUID101"}, {"studentId": "da5e5f67-943e-4baa-83e0-19049c0f5439", "rollNumber": "STUID102"}, {"studentId": "dc5006a4-93e1-4f06-9795-a333fb86989c", "rollNumber": "STUID103"}, {"studentId": "e1db36ea-836b-40f6-a254-2a8cc32efa60", "rollNumber": "STUID104"}, {"studentId": "e46a0ab2-fddf-4a4c-a084-d4fbbd0aeccb", "rollNumber": "STUID105"}, {"studentId": "e5359742-bd95-4e27-ae61-3fae44a272e3", "rollNumber": "STUID106"}, {"studentId": "e9430db9-9de0-4f39-86b8-2269f32a6fd3", "rollNumber": "STUID107"}, {"studentId": "ed5a059c-3912-487c-9168-bbf48eb36a67", "rollNumber": "STUID108"}, {"studentId": "ef93fa58-078e-495b-b8dd-93708c201cbc", "rollNumber": "STUID109"}, {"studentId": "efe3a5cf-24d3-42fa-baf3-4a2d976f01cb", "rollNumber": "STUID110"}, {"studentId": "f1da0630-0875-4d1c-abdd-73f6f8eac17b", "rollNumber": "STUID111"}, {"studentId": "f1de810a-50fb-45a4-945c-5fa40908f905", "rollNumber": "STUID112"}, {"studentId": "f25a23b6-3e2e-4b87-9744-97d49a685d1d", "rollNumber": "STUID113"}, {"studentId": "f48510b7-ee86-4f37-97c8-22375ffab41a", "rollNumber": "STUID114"}, {"studentId": "f4ba5e6e-e7ff-4777-a835-bb30202c79d6", "rollNumber": "STUID115"}, {"studentId": "f7438410-2e6b-4808-8574-2bdb7620ef17", "rollNumber": "STUID116"}, {"studentId": "f90bc090-a032-426b-9061-c950dcdde73e", "rollNumber": "STUID117"}, {"studentId": "fb333b8d-ac33-42b5-a9d1-900ce70e2d79", "rollNumber": "STUID118"}, {"studentId": "fbd1de83-c909-4da3-b41b-223804faca4a", "rollNumber": "STUID119"}, {"studentId": "fcfc8aca-6db6-4de9-bb3a-8ef58cc029f2", "rollNumber": "STUID120"}, {"studentId": "ff5d3bd7-32cb-4697-898a-fbaf71a436d6", "rollNumber": "STUID121"}

//        String json = "{\"instituteId\": 110, \"academicSessionId\": 255, \"standardId\": \"4f3a8bbd-5b57-40d9-8616-a1e62039a02c\", \"studentAcademisSessionParametersList\": [\"ROLL_NUMBER\"], \"studentAcademicDetailsList\": [{\"studentId\": \"00afd901-01f4-450e-aef3-31ec45099d02\", \"rollNumber\": \"STUID1\"}]}";

//        String json = "{\"instituteId\": 110, \"academicSessionId\": 255, \"studentAcademisSessionParametersList\": [\"ROLL_NUMBER\"], \"studentAcademicDetailsList\": [{\"studentId\": \"00afd901-01f4-450e-aef3-31ec45099d02\", \"rollNumber\": \"STUID1\"}]}";

//        , {"studentId": "01821281-8b6b-4c1c-af7f-dba5b01ae4ba", "rollNumber": "STUID2"}, {"studentId": "06a02299-013f-4dfc-93b5-342ba87f5c78", "rollNumber": "STUID3"}, {"studentId": "06ca73c8-0663-4cae-b01b-9c0779dee9d8", "rollNumber": "STUID4"}, {"studentId": "07f56df2-f04e-43b9-a453-cbbccb6a84e2", "rollNumber": "STUID5"}, {"studentId": "08eb830c-45dc-43a2-bc96-d14334d07977", "rollNumber": "STUID6"}, {"studentId": "09b9d973-cf66-4a67-834c-51a96aadd33e", "rollNumber": "STUID7"}, {"studentId": "0ab83126-47bc-43e0-8210-5648be2353cc", "rollNumber": "STUID8"}, {"studentId": "0b3f0f4c-86cc-426c-88e0-0c4cae8a8793", "rollNumber": "STUID9"}, {"studentId": "0f6a88fc-3b0d-4013-ad5d-e0a884f86f41", "rollNumber": "STUID10"}, {"studentId": "101cebf8-ab78-4d0b-a4d7-da8eaf683b7e", "rollNumber": "STUID11"}, {"studentId": "104d7ca0-4287-4c27-9e6f-0c044d00fa49", "rollNumber": "STUID12"}, {"studentId": "10fd11b9-a95a-4275-be68-9c51300617f9", "rollNumber": "STUID13"}, {"studentId": "1164295c-14d5-4cf6-bf60-a166463ed758", "rollNumber": "STUID14"}, {"studentId": "132df7b6-db87-4ca2-8cf9-1ff314ba26a0", "rollNumber": "STUID15"}, {"studentId": "13ad413e-8c2c-4584-82bd-a966807b5d58", "rollNumber": "STUID16"}, {"studentId": "1525cd68-fcba-45b7-858b-785cb80ec1f9", "rollNumber": "STUID17"}, {"studentId": "1762e0dc-285f-4e06-b602-ff725dbad86d", "rollNumber": "STUID18"}, {"studentId": "18ca658e-c395-4614-bf2e-f22186983e72", "rollNumber": "STUID19"}, {"studentId": "18fd7074-7bc5-442a-a530-e59e99762759", "rollNumber": "STUID20"}, {"studentId": "19d25716-79c5-4026-8229-0637d6fdaf98", "rollNumber": "STUID21"}, {"studentId": "1fc19128-08b2-4349-bd14-47092d31ee24", "rollNumber": "STUID22"}, {"studentId": "20e97db7-dd2c-41fe-b1da-5244b16747f2", "rollNumber": "STUID23"}, {"studentId": "223b832c-bb8b-4303-a633-9521d2c9643c", "rollNumber": "STUID24"}, {"studentId": "22cd0927-74bb-41f5-9a3d-065e428e6042", "rollNumber": "STUID25"}, {"studentId": "283ca52f-a238-435a-8511-9f957fb85b98", "rollNumber": "STUID26"}, {"studentId": "2bf15a17-502a-4380-be19-4b92cd6a95d5", "rollNumber": "STUID27"}, {"studentId": "309f0487-4700-4309-91e6-ad156b0db664", "rollNumber": "STUID28"}, {"studentId": "325bb1e6-826c-49b7-96cc-dd4f7275829f", "rollNumber": "STUID29"}, {"studentId": "37bb5c06-37d8-4378-b273-32a40b76ec57", "rollNumber": "STUID30"}, {"studentId": "390f7f2b-e150-48db-bec9-4e81c43b8718", "rollNumber": "STUID31"}, {"studentId": "3da28293-3def-4221-94ff-cc967333efcf", "rollNumber": "STUID32"}, {"studentId": "414d2699-092c-4cdb-b189-2ea68b4ca666", "rollNumber": "STUID33"}, {"studentId": "44fcef60-6b25-4098-abb2-78a2721f3a78", "rollNumber": "STUID34"}, {"studentId": "48ff1961-6d14-4ee1-885d-30560d373b07", "rollNumber": "STUID35"}, {"studentId": "49f102b9-8e26-468f-a991-95143f3823ab", "rollNumber": "STUID36"}, {"studentId": "501dabcb-f307-47b0-9e29-cad879cd29c5", "rollNumber": "STUID37"}, {"studentId": "5051d2a6-e9b3-41e5-9852-67e567e56888", "rollNumber": "STUID38"}, {"studentId": "50ed95c9-c24e-4787-813a-d77a884e2203", "rollNumber": "STUID39"}, {"studentId": "53730b37-0217-427f-8ecc-68e12a0380b9", "rollNumber": "STUID40"}, {"studentId": "55fbc861-2d01-4e9e-b6ed-a13f6ed532cc", "rollNumber": "STUID41"}, {"studentId": "5746bbaa-cfaa-4813-ac70-44b28899de32", "rollNumber": "STUID42"}, {"studentId": "5e1122c0-8afd-4f65-8538-bc4314467aa3", "rollNumber": "STUID43"}, {"studentId": "61f614c6-5dd5-4ba2-a173-68efac1ce8f6", "rollNumber": "STUID44"}, {"studentId": "628911d3-1beb-442b-a83b-c05dba91f8c7", "rollNumber": "STUID45"}, {"studentId": "654f7bf3-f7a0-4ba7-b7e0-0320fc921d3b", "rollNumber": "STUID46"}, {"studentId": "6668a630-ec80-4f2c-bd35-b5ef68417697", "rollNumber": "STUID47"}, {"studentId": "*************-46ef-952f-26aba43ac05c", "rollNumber": "STUID48"}, {"studentId": "6b602762-c7e7-4a1a-a7af-ddf682576172", "rollNumber": "STUID49"}, {"studentId": "6bdff490-53e8-41ae-b332-c16606021ab7", "rollNumber": "STUID50"}, {"studentId": "6bf02553-980b-449d-95f9-bb6c261f77b3", "rollNumber": "STUID51"}, {"studentId": "7216d433-56f2-4784-89e5-79b4d415f32a", "rollNumber": "STUID52"}, {"studentId": "735021d1-b706-418e-998b-9f5d774bac89", "rollNumber": "STUID53"}, {"studentId": "77753aa6-7d08-4456-897c-d6033e956042", "rollNumber": "STUID54"}, {"studentId": "77ea3aef-e2c2-48e9-b2cb-eef52e4ddeac", "rollNumber": "STUID55"}, {"studentId": "7894963d-03bb-4da0-947e-adf5ac3c220c", "rollNumber": "STUID56"}, {"studentId": "78eeed97-f00a-46b6-ab23-325abdb64cab", "rollNumber": "STUID57"}, {"studentId": "78f1e0ba-d495-48f8-a593-01a7a3d2bdc0", "rollNumber": "STUID58"}, {"studentId": "79574598-9dfb-4d47-924c-8168c9a1f6e4", "rollNumber": "STUID59"}, {"studentId": "7af49516-ebff-43f2-83c1-dbdbe32169a5", "rollNumber": "STUID60"}, {"studentId": "7dd15326-3354-4c8c-8ac2-4873e6a362bd", "rollNumber": "STUID61"}, {"studentId": "83e51b02-e9c0-42bf-8e7a-55dc182f35be", "rollNumber": "STUID62"}, {"studentId": "87f40b38-d429-4fd8-912d-1ca08377e978", "rollNumber": "STUID63"}, {"studentId": "8a9a6d9a-667a-42cb-8c3f-2f25ba3183a3", "rollNumber": "STUID64"}, {"studentId": "8bd5abcf-5885-4328-9c3c-11811ce2f6b4", "rollNumber": "STUID65"}, {"studentId": "9020b508-23c5-487f-86ba-143a74302108", "rollNumber": "STUID66"}, {"studentId": "90dc84c2-5cc6-4384-93ed-3674f44bf044", "rollNumber": "STUID67"}, {"studentId": "915682b0-97b0-438b-8a62-c9834013749e", "rollNumber": "STUID68"}, {"studentId": "919325df-81e8-4468-b366-10ee384186d9", "rollNumber": "STUID69"}, {"studentId": "92927897-300f-4e45-9837-6d8ac112a253", "rollNumber": "STUID70"}, {"studentId": "976a9689-85ee-4483-b936-346e1b4e69c7", "rollNumber": "STUID71"}, {"studentId": "9af3dc00-4d3f-4448-bd91-e98319c04dc6", "rollNumber": "STUID72"}, {"studentId": "9b31db18-6aab-4872-9b27-9b888450396d", "rollNumber": "STUID73"}, {"studentId": "9b6647aa-f200-4034-81a0-199a4048a599", "rollNumber": "STUID74"}, {"studentId": "9bab5185-027c-4089-925b-667fc775909b", "rollNumber": "STUID75"}, {"studentId": "9c4c492b-0ef5-4848-a79a-58e836bdd229", "rollNumber": "STUID76"}, {"studentId": "9fe349d1-edbd-4fb7-936e-1683e6625148", "rollNumber": "STUID77"}, {"studentId": "a05b99c1-8f2c-4e47-9351-a87ce4f8b759", "rollNumber": "STUID78"}, {"studentId": "a32decce-2d67-4fdc-a09f-da319931fcb5", "rollNumber": "STUID79"}, {"studentId": "a353d78a-8538-4006-b8af-6ea09849b3e7", "rollNumber": "STUID80"}, {"studentId": "a4e88874-dabb-4d45-8e97-7522ab4287b5", "rollNumber": "STUID81"}, {"studentId": "a574db85-1b28-49ab-b0bb-df4c1dfc5cfd", "rollNumber": "STUID82"}, {"studentId": "a59c0f40-c150-41ca-b6ad-74e4e8439c17", "rollNumber": "STUID83"}, {"studentId": "a70fc122-e0d2-4742-a3ff-654d7404e05e", "rollNumber": "STUID84"}, {"studentId": "ab162b72-1f4c-444c-b2fd-90c1c3fe5a82", "rollNumber": "STUID85"}, {"studentId": "ab25c913-8217-4e76-ad34-a1810fa7ca43", "rollNumber": "STUID86"}, {"studentId": "acbe2b02-3a68-4818-a0a8-727c73e31495", "rollNumber": "STUID87"}, {"studentId": "adfcd386-b00f-4642-af43-9b40bb302619", "rollNumber": "STUID88"}, {"studentId": "b106d637-22ae-460f-ac30-ca21da954017", "rollNumber": "STUID89"}, {"studentId": "b64ceed5-177f-4678-88bc-db50e4d9fe71", "rollNumber": "STUID90"}, {"studentId": "c2d2dbc4-77dc-4bf7-a865-b40285f40858", "rollNumber": "STUID91"}, {"studentId": "c494172b-4207-4df8-b6e2-1365b67ac96e", "rollNumber": "STUID92"}, {"studentId": "cd9bdfd7-d425-4a70-865f-d653e2cd895e", "rollNumber": "STUID93"}, {"studentId": "d154fb7d-5fc6-44ea-921a-fbb4b5be6ace", "rollNumber": "STUID94"}, {"studentId": "d1902c2d-bee3-48b2-b428-6ce91ffd6de2", "rollNumber": "STUID95"}, {"studentId": "d2591346-4bba-404c-8dbb-af467944ff0f", "rollNumber": "STUID96"}, {"studentId": "d2cc06fb-b6b2-43c7-9158-6f5793dba530", "rollNumber": "STUID97"}, {"studentId": "d344eef9-e3eb-4e12-a518-6808714a701c", "rollNumber": "STUID98"}, {"studentId": "d688a8ad-e113-49f5-84d7-756e324e11da", "rollNumber": "STUID99"}, {"studentId": "d6b819ec-cb89-4867-ad9a-acedb2d84043", "rollNumber": "STUID100"}, {"studentId": "da48418b-b6f0-4278-a7a4-edf61d207cc4", "rollNumber": "STUID101"}, {"studentId": "da5e5f67-943e-4baa-83e0-19049c0f5439", "rollNumber": "STUID102"}, {"studentId": "dc5006a4-93e1-4f06-9795-a333fb86989c", "rollNumber": "STUID103"}, {"studentId": "e1db36ea-836b-40f6-a254-2a8cc32efa60", "rollNumber": "STUID104"}, {"studentId": "e46a0ab2-fddf-4a4c-a084-d4fbbd0aeccb", "rollNumber": "STUID105"}, {"studentId": "e5359742-bd95-4e27-ae61-3fae44a272e3", "rollNumber": "STUID106"}, {"studentId": "e9430db9-9de0-4f39-86b8-2269f32a6fd3", "rollNumber": "STUID107"}, {"studentId": "ed5a059c-3912-487c-9168-bbf48eb36a67", "rollNumber": "STUID108"}, {"studentId": "ef93fa58-078e-495b-b8dd-93708c201cbc", "rollNumber": "STUID109"}, {"studentId": "efe3a5cf-24d3-42fa-baf3-4a2d976f01cb", "rollNumber": "STUID110"}, {"studentId": "f1da0630-0875-4d1c-abdd-73f6f8eac17b", "rollNumber": "STUID111"}, {"studentId": "f1de810a-50fb-45a4-945c-5fa40908f905", "rollNumber": "STUID112"}, {"studentId": "f25a23b6-3e2e-4b87-9744-97d49a685d1d", "rollNumber": "STUID113"}, {"studentId": "f48510b7-ee86-4f37-97c8-22375ffab41a", "rollNumber": "STUID114"}, {"studentId": "f4ba5e6e-e7ff-4777-a835-bb30202c79d6", "rollNumber": "STUID115"}, {"studentId": "f7438410-2e6b-4808-8574-2bdb7620ef17", "rollNumber": "STUID116"}, {"studentId": "f90bc090-a032-426b-9061-c950dcdde73e", "rollNumber": "STUID117"}, {"studentId": "fb333b8d-ac33-42b5-a9d1-900ce70e2d79", "rollNumber": "STUID118"}, {"studentId": "fbd1de83-c909-4da3-b41b-223804faca4a", "rollNumber": "STUID119"}, {"studentId": "fcfc8aca-6db6-4de9-bb3a-8ef58cc029f2", "rollNumber": "STUID120"}, {"studentId": "ff5d3bd7-32cb-4697-898a-fbaf71a436d6", "rollNumber": "STUID121"}

//            String json = "{\"instituteId\": 110, \"academicSessionId\": 70, \"studentParametersSet\": [\"REGISTRATION_NUMBER\"], \"bulkStudentInfoList\": [{\"studentId\": \"d79b3f48-f429-4bd7-a817-8caaff9a4d90\", \"registrationNumber\": \"2411001629\"}]}";
//		BulkStudentPayload bulkStudentPayload = null;
//		try {
//			ObjectMapper objectMapper = new ObjectMapper();
//
//			// Deserialize JSON into ExamReportFiltrationCriteria instance
//            bulkStudentPayload = objectMapper.readValue(json, BulkStudentPayload.class);
//
//			// Output the created instance
//			System.out.println(bulkStudentPayload);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

//            final boolean updated = studentManager.updateBulkStudentDetails(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//                    bulkStudentPayload);
//
//        System.out.println(updated);

//final TransportVehicleTransactionManager transportVehicleTransactionManager = context.getBean(TransportVehicleTransactionManager.class);
//List<VehicleExpenseDetails> vehicleExpenseDetailsList = transportVehicleTransactionManager.getTransportVehicleExpenseByDate(10225, 194, 1739952709);
//for(VehicleExpenseDetails vehicleExpenseDetails : vehicleExpenseDetailsList) {
//    System.out.println("vehicle edtails " + vehicleExpenseDetails);
//}
//            List<VehicleLogDetails> vehicleExpenseDetailsList = transportVehicleTransactionManager.getTransportVehicleLogByDate(10225, 194, 1739855748);
//            for(VehicleLogDetails vehicleExpenseDetails : vehicleExpenseDetailsList) {
//                System.out.println("vehicle edtails " + vehicleExpenseDetails);
//            }
//}

////            VehicleExpensePayload vehicleExpensePayload1 =new VehicleExpensePayload(null, 262, TransactionMode.CASH, null, "FUEL", 10.0, 1739765544, VehicleLogCategory.FUEL, UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), null, 1.0, 10.0, null);
//            List<VehicleExpensePayload> vehicleExpensePayloadList = new ArrayList<>();
//            vehicleExpensePayloadList.add(vehicleExpensePayload);
////            vehicleExpensePayloadList.add(vehicleExpensePayload1);
//            UUID status = transportVehicleTransactionManager.addOtherExpense(10225, UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), vehicleExpensePayload);
//            System.out.println(status);
//            boolean status = transportVehicleTransactionManager.updateOtherExpense(10225, UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), vehicleExpensePayload);
//            System.out.println(status);
//            boolean status = transportVehicleTransactionManager.deleteVehicleExpense(10225, 194, UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), UUID.fromString("23f37a19-cc4f-4594-8601-9f52218ff56b"));
//            System.out.println(status);

//            VehicleLogPayload vehicleLogPayload = new VehicleLogPayload(null, 77 , null, 10.0, 1739765544, UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), UUID.fromString("f0484d4b-9608-4d5a-a97b-6ef919022b10"), null, null, null, null);
//            List<VehicleLogPayload> vehicleLogPayloadList = new ArrayList<>();
//            vehicleLogPayloadList.add(vehicleLogPayload);
//            boolean status = transportVehicleTransactionManager.addVehicleLogs(10225, 194, vehicleLogPayloadList);
//            System.out.println(status);

//            final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//
//            final StudentTransferCertificateDetails studentTransferCertificateDetails =
//                    studentAdmissionManager.getStudentTransferCertificateDetails(101, UUID.fromString("e2608d93-b99c-435e-add6-b08dbbb113f0"), true);
//            Set<String> set = new HashSet<>();
//            set.add("february 2027");
//            StructureCloneRequest request = new StructureCloneRequest(101, 190, 208, StructureCloneType.CUSTOM, set);
//            final boolean status = feeSetupUtilityManager.cloneFeeStructureAcrossSessions(request);
//            System.out.println(status);
//            final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
//            final SearchResultWithPagination<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
//                    .searchFeePaymentTransactions(101, "ja", 190, FeePaymentTransactionStatus.ACTIVE, 0, 0, false);
//            System.out.println(visitorDetailsList);
//
//            System.out.println(studentTransferCertificateDetails);
//            127.0.0.1 - - [23/Jan/2025:11:32:55 +0530] "GET /data-server/2.0/examination-reports/0a7504f4-546e-43fc-bde3-bca68e1be6ca/course/a2ffeffa-b85a-4112-bda3-3ad57fa445c0?section_id=2410&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&institute_id=10295&academic_session_id=190&user_id=9e19e819-ba37-4e12-b2e3-ff1e05269b2d&download_format=EXCEL HTTP/1.1" 200 26112


//            final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//
//            int instituteId = 10295;
//            int academicSessionId = 190;
//            UUID standardId = UUID.fromString("0a7504f4-546e-43fc-bde3-bca68e1be6ca");
//            Integer sectionId = 2410;
////            UUID examId = UUID.fromString("a2ffeffa-b85a-4112-bda3-3ad57fa445c0");
//            UUID examId = UUID.fromString("37a856ab-2f09-47d6-89da-c1a495a46b4c");
//            boolean excludeCoScholasticSubjects = false;
//            boolean showDimensions = true;
//            boolean showTotalColumnDimension = false;
//            DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//            UUID userId = UUID.fromString("9e19e819-ba37-4e12-b2e3-ff1e05269b2d");
//            final ReportDetails reportDetails = examReportGenerator.generateExamConsolidatedReport(instituteId, academicSessionId, standardId, sectionId,
//                    examId, excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, downloadFormat, userId);
//
//            ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//
//
//            final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//            reportOutput.getReportContent().writeTo(outputStream);


//            final TransportConfigurationManager transportConfigurationManager = context.getBean(TransportConfigurationManager.class);

//            final PaymentGatewayManager paymentGatewayManager = context.getBean(PaymentGatewayManager.class);
//
//            final FeeSetupUtilityManager feeSetupUtilityManager = context.getBean(FeeSetupUtilityManager.class);
////            Set<String> set = new HashSet<>();
////            set.add("february 2027");
////            StructureCloneRequest request = new StructureCloneRequest(101, 190, 208, StructureCloneType.CUSTOM, set);
////            final boolean status = feeSetupUtilityManager.cloneFeeStructureAcrossSessions(request);
////            System.out.println(status);
//            final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
//            final SearchResultWithPagination<FeePaymentTransactionMetaData> feePaymentTransactionMetaDatas = feePaymentManager
//                    .searchFeePaymentTransactions(101, "ja", 190, FeePaymentTransactionStatus.ACTIVE, 0, 0, false);
//            System.out.println(feePaymentTransactionMetaDatas);

//            StudentManager studentManager = context.getBean(StudentManager.class);
//            StudentAdmissionStats studentAdmissionStats = studentManager.getStudentAdmissionStats(101, 190);
//            System.out.println(studentAdmissionStats);
//            DashboardManager dashboardManager = context.getBean(DashboardManager.class);
//            Set<Integer> set = new HashSet<>();
//            set.add(101);
//////            4f11399b-fd98-4af3-81f0-df5ac5652063/08dc0556-b8a1-4acd-a6fa-759ca864df9e?date=1737177317&selected_academic_session_id=190
//            final List<InstituteStats> instituteStatsList = dashboardManager.getOrganizationStats(UUID.fromString("4f11399b-fd98-4af3-81f0-df5ac5652063"), UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),  1711954831, 1738130663, 190, set);
//            System.out.println("starts");
//            for(InstituteStats instituteStats : instituteStatsList){
////                System.out.println(instituteStats.getInstitute().getInstituteId() + " : " + instituteStats);
//            }
//            System.out.println("ends");
//            StaffManager staffManager = context.getBean(StaffManager.class);
//            int today = DateUtils.now();
//            final StaffStats staffStats = staffManager.getStaffStats(101, false, today, today);
//            System.out.println(staffStats);


//            AttendanceManager attendanceManager = context.getBean(AttendanceManager.class);
//
//            Map<Integer, StudentDateAttendanceDetails> studentDateAttendanceDetailsMap = attendanceManager.getStudentDateAttendanceDetails(
//                    101, 190, 1737523455, 1737523455, null);
//            System.out.println(studentDateAttendanceDetailsMap);

//            FeeConfigurationCloneRequest feeConfigurationCloneRequest = new FeeConfigurationCloneRequest(101, 190, 208);
//            boolean status = feeSetupUtilityManager.cloneFeeConfigurationAcrossSession(feeConfigurationCloneRequest);
//System.out.println(status);
//            final InstituteDao instituteDao = context.getBean(InstituteDao.class);
//            Organisation activeOrganisationData = instituteDao.getOrganization(101);
//            System.out.println(activeOrganisationData);

//            final NoticeBoardManager noticeBoardManager = context.getBean(NoticeBoardManager.class);
//
//            List<UserNoticeDetails> userNoticeDetailsList = noticeBoardManager.getNoticesListLimitOffset(110, UserType.STUDENT,
//                    UUID.fromString("00afd901-01f4-450e-aef3-31ec45099d02"), 255, 80, 0);
//
//
//            System.out.println(userNoticeDetailsList.size());

////            VehiclePayload vehiclePayload = new VehiclePayload();
// //           vehiclePayload.setInstituteId(101);
// //           vehiclePayload.setBatteryNumber("1011");
////            vehiclePayload.setVehicleNumber("HR1D0101");
////            vehiclePayload.setVehicleCode("987df123");
////            vehiclePayload.setRegistrationNumber("1234456323434");
////            vehiclePayload.setCapacity(35);
////            vehiclePayload.setVehicleTypeId(245);
//
////            List<VehicleDocumentInfo> vehicleDocumentInfoList = new ArrayList<>();
////            VehicleDocumentInfo vehicleDocumentInfo = new VehicleDocumentInfo();
////            vehicleDocumentInfo.setDocumentName("123");
////            vehicleDocumentInfo.setRenewDate(1734633000);
////            vehicleDocumentInfo.setDocumentId(UUID.fromString("1a339c95-8b01-4fd4-942c-23d9561e5aec"));
////            vehicleDocumentInfoList.add(vehicleDocumentInfo);
//
////            VehicleDocumentInfo vehicleDocumentInfo2 = new VehicleDocumentInfo();
////            vehicleDocumentInfo2.setDocumentName("456");
////            vehicleDocumentInfo2.setExpiryDate(1734633000);
////            vehicleDocumentInfo2.setDocumentId(UUID.fromString("8a090e6a-567e-46e6-9bf3-7af9b6d28eff"));
// //           vehicleDocumentInfoList.add(vehicleDocumentInfo2);
////            byte[] content = { 1, 0, 1, 0, 0, 1, 1, 1};
////            FileData fileData = new FileData(content,"Insurance.pdf");
////            FileData fileData2 = new FileData(content,"Puc.pdf");
////            final List<FileData> files = new ArrayList<>();
//
// //           files.add(fileData);
////            files.add(fileData2);

//            boolean vehicleAdded = transportConfigurationManager.updateVehicle(vehiclePayload, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//            boolean vehicleAdded = transportConfigurationManager.addVehicle(vehiclePayload, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//            final List<Document<VehicleDocumentType>> updatedDocuments = transportConfigurationManager.uploadDocument(instituteId,
//                    vehicleId, vehicleDocumentFiles, vehicleDocumentInfoList);
//            final List<Document<VehicleDocumentType>> updatedDocuments = transportConfigurationManager.uploadDocument(101,
//                    165, files, vehicleDocumentInfoList, null);
//            List<Vehicle> vehicleList = transportConfigurationManager.getVehicles(101);
//            List<UUID>  userIds = new ArrayList<>();
//            userIds.add(UUID.fromString("108764fb-20e7-45d9-8a85-fa015cf2876f"));
//            final List<Document<VehicleDocumentType>> updatedDocuments = transportConfigurationManager.deleteDocument(101,
//                    170, userIds);
//            List<Vehicle> vehicleList = transportConfigurationManager.getVehicles(101);
//
//            System.out.println(vehicleList);
//            PGInitiateTransactionPayload initiateTransactionPayload = new PGInitiateTransactionPayload(
//                    UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d"),
//                    PGTransactionType.STUDENT_FEE_PAYMENT,
//                    4.0, walletAmount, instantDiscountAmount, fineAmount, currency, PaymentGatewayStage.PROD,
//            description, studentFeePaymentMetadata, callbackUrl);
//
//
//            PGInitiateTransactionResponse initiateTransactionResponse = paymentGatewayManager.initiatePaymentTransaction(
//                    110, initiateTransactionPayload, PGPlatform.GOOGLE);

//            JsonObject{_jsonMap: {paymentGatewayStage: PROD, userId: 2c2e8bc2-d850-4677-b731-1be53edf733d, transactionType: STUDENT_FEE_PAYMENT, paymentGatewayAmount: 4.0, description: , instantDiscountAmount: 0.0, fineAmount: 0.0, studentFeePaymentMetadata: {academicSessionId: 153, dueFeeIds: [c7ada8e6-05f2-47f0-a3dc-586acfc91a70, 41f9b986-fa66-4009-9a9a-e8b32ecd19c6]}, currency: INR, walletAmount: 0.0}}

//            /data-server/1.0/hpc/form/5aed1447-60fd-4e63-8ff5-6b27e38e1eaa/exams/TERM1?institute_id=110&academic_session_id=268

//            HPCWebForm form = hpcFormManager.getHPCWebForm(110,  268,
//                    UUID.fromString("5aed1447-60fd-4e63-8ff5-6b27e38e1eaa"), HPCExamType.TERM1);
//            System.out.println(form);


//            HPCFormWithStudent hpcFormWithStudent = hpcFormManager.getHPCFormWithStudent(
//                    110, 268, UUID.fromString("d12f3fa4-71e6-487e-bb71-646f64265e0f"),
//                    HPCExamType.TERM2);



//            final StaffAttendanceManager staffAttendanceManager = context.getBean(StaffAttendanceManager.class);
//            final List<StaffAttendanceDateData> staffAttendanceDateDataList = staffAttendanceManager.
//                    getStaffAttendanceDateDataList(
//                    110, 1735324200, null, null);
//

//            System.out.println(hpcFormWithStudent.toString());


//           final LibraryManager libraryManager = context.getBean(LibraryManager.class);
//           //SearchResultWithPagination<BookDetail> bookDetails = libraryManager.getBookDetailsWithPagination(101, null, "", null, null);
//           List<BookDetailsLite> bookDetailLites = libraryManager.getSearchBookDetailLite(101, 256, null, "h");
//           System.out.println(bookDetailLites);
//            final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//            ExamReportFiltrationCriteria examReportFiltrationCriteria = new ExamReportFiltrationCriteria(
//                    262, UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), null,
//                    UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"), null,
//                    null, DownloadFormat.PDF, "PRE_BOARD_1",
//                    UUID.fromString("0aaa8d43-4209-4f19-b10f-5d7a34a14676"), new HashSet<>(Arrays.asList(3542)),
//                    new HashSet<>(Arrays.asList(UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"))),
//                    null, null, null, false,
//                    "sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks",
//                    false, null,
//                    new HashSet<>(Arrays.asList(MarksDisplayType.MARKS, MarksDisplayType.GRADE, MarksDisplayType.PERCENTAGE)),
//                    new HashSet<>(Arrays.asList(MarksDisplayType.MARKS, MarksDisplayType.GRADE, MarksDisplayType.PERCENTAGE)),
//                    true, false);
//            final ReportDetails reportDetails = examReportGenerator.generateReport(10390,
//                    ExamReportType.TEACHER_WISE_DIVISION_RESULT_ANALYSIS_REPORT,
//                    UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"), examReportFiltrationCriteria);

//            final ReportDetails reportDetails = examReportGenerator.generateExamConsolidatedReport(10390, 262,
//                    UUID.fromString("4b463d0a-920e-4db2-8d2c-8427a80ae15c"), 3542,
//                    UUID.fromString("bb446c51-e7fe-4c07-9e73-aa5a03551f10"), true, false, false, DownloadFormat.EXCEL,
//                    UUID.fromString("09bc56a2-8076-4d5c-a5c5-9429b24a85ff"));
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);


//            final InstituteManagementManager instituteManagementManager = context.getBean(InstituteManagementManager.class);
////            boolean status = instituteManagementManager.deleteStandardStaffAssignment(
////                    110, 255, UUID.fromString("8471c300-9f09-41bf-b3a0-67a34f9c95cd"),
////                    null, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
////
////            System.out.println(status);

//
//            FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
//            final List<StudentFeePaymentAggregatedData> studentFeePaymentAggregatedDataList =
//                    feePaymentManager.getStudentSiblingsDueAmounts(101, 190, UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"));
//            System.out.println(studentFeePaymentAggregatedDataList);
//            final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(110, 255,
//                    FeesReportType.STUDENT_AGGREGATED_MULTI_SESSION_DUE_PAYMENT, -1, -1, null, null, null,
//                    null,
//                    "sr_no,registration_no,admission_no,name,status,class,father_name,primary_contact,whatsapp_number,father_contact,mother_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount",
//                    UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), DownloadFormat.EXCEL,
//                    null, null, "268,255,153", null, null, false, null);

//            UserAuthenticationPayload userAuthenticationPayload = new UserAuthenticationPayload();
////            userAuthenticationPayload.setUserName("admind110@embrate");
////            userAuthenticationPayload.setPassword("lernen#324");
//            userAuthenticationPayload.setUserName("1442@eis110");
//            userAuthenticationPayload.setPassword("12345678");
//            final UserLoginView userLoginView = userManager.authenticate(userAuthenticationPayload, null,
//                    null);
//
//            System.out.println(userLoginView.getOauthTokenData().getAccessToken());
//            System.out.println(userLoginView.getUserAuthenticationStatus().toString());

//            boolean status = instituteManagementManager.deleteStandardStaffAssignment(
//                    110, 255, UUID.fromString("8471c300-9f09-41bf-b3a0-67a34f9c95cd"),
//                    null, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//
//            System.out.println(status);


//            boolean updated = instituteManagementManager.addUpdateStandardStaffAssignment(110, 255,
//                    UUID.fromString("263a6452-9a4f-44b0-8b2f-3ae38534e50a"),
//                    UUID.fromString("aed09712-719b-4377-bda6-d3622e3c2432"), 3668,
//                    UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//

//            StaffManager staffManager = context.getBean(StaffManager.class);
//            final List<Staff> staff = staffManager.getStaff(101);
//            for(Staff staff1 : staff) {
////               if(staff1.getStaffBasicInfo().getStaffNatureOfAppointment() != null){
////                   System.out.println(staff1.getStaffBasicInfo().getStaffNatureOfAppointment().getStaffNature);
////               } else{
//                   System.out.println(staff1);
////               }
//            }

//            int start = 1725129000;
//            int end = 1727720999;
//            DateTime startDateTime = new DateTime(DateUtils.getDayStart(start, DateUtils.DEFAULT_TIMEZONE) * DateUtils.TO_LONG, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
//            DateTime endDateTime = new DateTime(DateUtils.getDayEnd(end, DateUtils.DEFAULT_TIMEZONE) * DateUtils.TO_LONG, DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE));
//
//            System.out.println("startDateTime : " + startDateTime);
//            System.out.println("endDateTime : " + endDateTime);
//
//            int duration = Days.daysBetween(startDateTime, endDateTime.plusDays(1)).getDays();
//
//            System.out.println("duration : " + duration);
//final EnquiryDetailsManager enquiryDetailsManager = context.getBean(EnquiryDetailsManager.class);
//            final StudentTagManager studentTagManager = context.getBean(StudentTagManager.class);
//            StudentManager studentManager = context.getBean(StudentManager.class);
//            StudentTaggedDetails studentTaggedDetails = new StudentTaggedDetails(TaggedActions.INVENTORY_SALE_RESTRICTED, "disobedient", 1734519286, UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"));
//            StudentTaggedDetails studentTaggedDetails1 = new StudentTaggedDetails(TaggedActions.FEE_PAYMENT_RESTRICTED, "disobedient", 1734519286, UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"));
//            List<StudentTaggedDetails> studentTaggedDetailsList = new ArrayList<>();
//            studentTaggedDetailsList.add(studentTaggedDetails);
//            studentTaggedDetailsList.add(studentTaggedDetails1);
//            List<UUID> uuidList = new ArrayList<>();
//            uuidList.add(UUID.fromString("1c90974b-9481-4484-97b0-9d41929b21b0"));
//            StudentTaggedPayload studentTaggedPayload = new StudentTaggedPayload(uuidList, studentTaggedDetailsList);
//            final boolean successful = studentManager.updateStudentTag(101, studentTaggedPayload);
////            final boolean issuccessful = studentTagManager.deleteStudentTag(101,190, UUID.fromString("00f7461d-68d0-4307-ac2a-83c72c3a9bde"));
//System.out.println(successful);

//            final Student student = studentManager.getStudentByAcademicSessionStudentId(101, 190,
//                    UUID.fromString("00f7461d-68d0-4307-ac2a-83c72c3a9bde"), false);
//            System.out.println(student.getStudentTaggedDetailsList());
//            System.out.println(student.getStudentTaggedDetailsList().size());
//            EnquiryDetailsPayload enquiryDetailsPayload= new EnquiryDetailsPayload(UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"), EnquiryStatus.SUBMITTED, "admission start date", 0, "VII",  "ramesh", null, "7585567889", "efgh", null, null );
//final String trackingId= enquiryDetailsManager.addEnquiryDetails(null, enquiryDetailsPayload);
//
//System.out.println(trackingId);
//            final EnquiryDetailsWithFollowup enquiryDetailsWithFollowup = enquiryDetailsManager.getEnquiryDetailsByEnquiryId(101, UUID.fromString("18a984ad-e7bb-43d0-a293-b015c0fdd2c8"), 190, UUID.fromString("1f156764-f05c-4ac8-b0a6-a6fd242611dc"));
//            final boolean updateEnquiryStatus = enquiryDetailsManager.updateEnquiryStatus(101, UUID.fromString("18a984ad-e7bb-43d0-a293-b015c0fdd2c8"), 190, UUID.fromString("7eb01442-1632-4e0b-8044-4f368c3f6fe8"),
//                    null, EnquiryStatus.ACCEPTED, "thank you for admission", FALSE);
//            System.out.println(updateEnquiryStatus);
//            FollowUpManager followUpManager = context.getBean(FollowUpManager.class);
//            FollowUpPayload followUpPayload = new FollowUpPayload(null, UUID.fromString("1c3c86ea-e587-4396-a4be-120e5c4f6839"), FollowUpEntity.ENQUIRY, 1732174926, FollowUpMode.MANUAL, "1234512345", "I WILl KNOW", 1732261326, null, FollowUpType.ADMISSION_ENQUIRY, FollowUpStatus.ACTIVE,  UUID.fromString("18a984ad-e7bb-43d0-a293-b015c0fdd2c8"), 0, null);
//            boolean isFollowUpTrailAdded = followUpManager.addFollowUpTrail(followUpPayload,101,UUID.fromString("18a984ad-e7bb-43d0-a293-b015c0fdd2c8"));
//            System.out.println(isFollowUpTrailAdded);
//            final EnquiryDetailsWithFollowup enquiryDetailsWithFollowup = enquiryDetailsManager.getEnquiryDetailsWithTrackingId(UUID.fromString("b1812075-526a-4d5c-817b-d8eb7953f6bc"), "EMJ5TDTU");
//            System.out.println(enquiryDetailsWithFollowup);
//            Set<EnquiryStatus> enquiryStatusSet = new HashSet<>();

//                   for (EnquiryDetailsWithFollowup enquiryDetailsWithFollowup : enquiryDetailsWithPagination.getResult())
//                   {
//                       System.out.println(enquiryDetailsWithFollowup.getUpdateDate());
//                   }
//            final List<EnquiryDetailsWithFollowup> enquiryDetailsWithFollowupList = enquiryDetailsManager.getEnquiryDetails(101, UUID.fromString("18a984ad-e7bb-43d0-a293-b015c0fdd2c8"), 190, enquiryStatusSet, 2, 0);
//System.out.println(enquiryDetailsWithFollowupList);


//            List<AcademicSession> academicSessionList = instituteManager.getAcademicSessionList(101);
//            int academicSessionId = academicSessionList.get(0).getAcademicSessionId();
//            final SalaryConfigurationManager salaryConfigurationManager = context.getBean(SalaryConfigurationManager.class);
//            HashMap<Integer, String> monthsMap = new HashMap<>();
//            monthsMap.put(1, "JANUARY");
//            monthsMap.put(2, "FEBRUARY");
//            monthsMap.put(3, "MARCH");
//            monthsMap.put(4, "APRIL");
//            monthsMap.put(5, "MAY");
//            monthsMap.put(6, "JUNE");
//            monthsMap.put(7, "JULY");
//            monthsMap.put(8, "AUGUST");
//            monthsMap.put(9, "SEPTEMBER");
//            monthsMap.put(10, "OCTOBER");
//            monthsMap.put(11, "NOVEMBER");
//            monthsMap.put(12, "DECEMBER");
//            List<SalaryCycleDetailsPayload> salaryCycleDetailsPayloadList = new ArrayList<>();
//            for( int i = academicSessionList.get(0).getStartMonth().getValue() ; i <= 12 ; i++){
//                String month =  String.valueOf(i);
//                if (i < 10){
//                    month = "0"+i;
//                }
//                int year = academicSessionList.get(0).getStartYear();
//                try {
//                    String startDate = "01/" + month +"/" + year;
//                    String endDate = "30/" + month +"/" + year;
//
//                    if ( i == 1 || i == 3 || i == 5 || i == 7 || i == 8 || i == 10 || i == 12) {
//                        endDate = "31/" + month +"/" + year;
//                    } else if (i == 2) {
//                        if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
//                            endDate = "29/" + month + "/" + year;
//                        } else {
//                            endDate = "28/" + month + "/" + year;
//                        }
//                    }
//                    String cycleName = monthsMap.get(i) + " " + academicSessionList.get(0).getStartYear();
//                    long startDateTimeStamp = new SimpleDateFormat("dd/MM/yyyy").parse(startDate).getTime() / 1000;
//                    long endDateTimeStamp = new SimpleDateFormat("dd/MM/yyyy").parse(endDate).getTime() / 1000;
//                    System.out.println(startDate+"-"+startDateTimeStamp +"  "+endDate+"-"+endDateTimeStamp+"  "+cycleName);
//                    salaryCycleDetailsPayloadList.add(new SalaryCycleDetailsPayload(startDateTimeStamp, endDateTimeStamp, cycleName));
//                } catch (ParseException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//
//            for( int i = 1 ; i <= academicSessionList.get(0).getEndMonth().getValue() ; i++){
//                String month =  String.valueOf(i);
//                if (i < 10){
//                    month = "0"+i;
//                }
//                int year = academicSessionList.get(0).getStartYear();
//                try {
//                    String startDate = "01/" + month +"/" + year;
//                    String endDate = "30/" + month +"/" + year;
//
//                    if ( i == 1 || i == 3 || i == 5 || i == 7 || i == 8 || i == 10 || i == 12) {
//                        endDate = "31/" + month +"/" + year;
//                    } else if (i == 2) {
//                        if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
//                            endDate = "29/" + month + "/" + year;
//                        } else {
//                            endDate = "28/" + month + "/" + year;
//                        }
//                    }
//                    String cycleName = monthsMap.get(i) + " " + academicSessionList.get(0).getEndYear();
//                    long startDateTimeStamp = new SimpleDateFormat("dd/MM/yyyy").parse(startDate).getTime() / 1000;
//                    long endDateTimeStamp = new SimpleDateFormat("dd/MM/yyyy").parse(endDate).getTime() / 1000;
//                    System.out.println(startDate+"-"+startDateTimeStamp +"  "+endDate+"-"+endDateTimeStamp+"  "+cycleName);
//                    salaryCycleDetailsPayloadList.add(new SalaryCycleDetailsPayload(startDateTimeStamp, endDateTimeStamp, cycleName));
//                } catch (ParseException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//            boolean result = salaryConfigurationManager.addBulkSalaryCycles(101, academicSessionId, salaryCycleDetailsPayloadList);
//            System.out.println(result);

//            final InstituteOnBoardingManager instituteOnBoardingManager = context.getBean(InstituteOnBoardingManager.class);
//            InstituteConfigurationPayload instituteConfigurationPayload = new InstituteConfigurationPayload();
//            String org = "ABC";
//            List<InstituteSetupInput> instituteSetupInputList =new ArrayList<>();
//            InstituteSetupInput instituteSetupInput = new InstituteSetupInput();
//            InstitutePayload institute = new InstitutePayload();

//            institute.setInstituteId(17);
//            institute.setInstituteName("DPS Delhi");

//            List<InstituteSessionPayload> instituteSessionPayloadList = new ArrayList<>();
//            InstituteSessionPayload instituteSessionPayload = new InstituteSessionPayload();
//            instituteSessionPayload.setStartMonth(4);
//            instituteSessionPayload.setEndMonth(3);
//            instituteSessionPayload.setStartYear(2025);
//            instituteSessionPayload.setEndYear(2026);
//            instituteSessionPayloadList.add(instituteSessionPayload);
//            ConfigInput configs = new ConfigInput();
//            List<InstituteStandardPayload> standards=new ArrayList<>();
//            InstituteStandardPayload instituteStandardPayload = new InstituteStandardPayload();
//            instituteStandardPayload.setStandardName("1st");
//            instituteStandardPayload.setLevel(1);
//            instituteStandardPayload.setSections(null);
//            instituteStandardPayload.setStream(null);
//            List<Module> authorizedModules = Arrays.asList(
//                    Module.DEFAULT,
//                    Module.STORE,
//                    Module.FEES,
//                    Module.ADMISSION,
//                    Module.TRANSPORT,
//                    Module.COURSES,
//                    Module.EXAMINATION,
//                    Module.ATTENDANCE,
//                    Module.STAFF_MANAGEMENT,
//                    Module.INCOME_EXPENSE,
//                    Module.USER_MANAGEMENT,
//                    Module.LECTURE_MANAGEMENT,
//                    Module.HOMEWORK_MANAGEMENT,
//                    Module.SALARY_MANAGEMENT,
//                    Module.NOTICE_BOARD_MANAGEMENT,
//                    Module.STUDENT_MANAGEMENT,
//                    Module.AUDIT_LOGS,
//                    Module.COMMUNICATION,
//                    Module.HOLIDAY_CALENDAR,
//                    Module.TIMETABLE_MANAGEMENT,
//                    Module.STAFF_ATTENDANCE,
//                    Module.FRONT_DESK,
//                    Module.MOBILE_APPLICATION_MANAGEMENT,
//                    Module.INSTITUTE_MANAGEMENT,
//                    Module.LEAVE_MANAGEMENT,
//                    Module.STUDENT_DIARY,
//                    Module.COMPLAINT_BOX,
//                    Module.PARENTS_APPOINTMENT,
//                    Module.VISITORS_DESK
//            );
//            instituteSetupInput.setInstitute(institute);
//            instituteSetupInput.setInstituteSessionPayloadList(instituteSessionPayloadList);
//            instituteSetupInput.setConfigs(configs);
//            instituteSetupInput.setStandards(standards);
//            instituteSetupInput.setAuthorizedModules(authorizedModules);
//            instituteSetupInputList.add(instituteSetupInput);
//            instituteConfigurationPayload.setInstituteSetupInputList(instituteSetupInputList);
//            instituteConfigurationPayload.setOrganisationName(org);
//            final InstituteSetupResult instituteSetupResult = instituteOnBoardingManager.setupInstitute(instituteConfigurationPayload);
//            System.out.println(instituteSetupResult);
//            final InstituteManager instituteManager = context.getBean(InstituteManager.class);
//            AcademicSession academicSession = new AcademicSession();
//            academicSession.setInstituteId(10355);
//            academicSession.setStartMonth(Month.APRIL);
//            academicSession.setEndMonth(Month.MARCH);
//            academicSession.setStartYear(2026);
//            academicSession.setEndYear(2027);
//            academicSession.setPayrollEndYear(2027);
//            academicSession.setPayrollStartYear(2026);
//            academicSession.setPayrollStartMonth(Month.APRIL);
//            academicSession.setPayrollEndMonth(Month.MARCH);
//
//            final List<String> added = instituteManager.addInstituteSession(academicSession, 199, false);
//            System.out.println(added);


//            List<PayHeadConfiguration> payHeadConfigurationList = new ArrayList<>();
//            payHeadConfigurationList.add(new PayHeadConfiguration(0, "ESIC", PayHeadType.DEDUCTION, true, PayHeadTag.ESIC, false,false,true, null));
//            payHeadConfigurationList.add(new PayHeadConfiguration(0, "EPF", PayHeadType.DEDUCTION, true, PayHeadTag.EPF, false,false,true, null));
//            boolean result = salaryConfigurationManager.addBulkSalaryCycles(101, academicSessionId,
//                    null, salaryCycleDetailsReadableList, false);
//            System.out.println(result);

//            final GatePassResponse gatePassResponse = hostelManagementManager.addGatePassDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new GatePassPayload(101,190,null,null,1732970400,null,"**********","personal work", GatePassStatus.CANCELLED, new ArrayList<>(Collections.singleton(new GatePassStudentPayload(UUID.fromString("060f848e-59cd-4657-a25f-f667c9ae4522"), null))),false, 1732939200, 1733025600, true));
//            System.out.println(gatePassResponse);
//
////            SearchResultWithPagination<StudentLite> studentsInAcademicSession = studentManager.liteSearchStudentsInAcademicSesison(101, "sa",
////                    190, null, 0, 20, null, false);




//            final HostelManagementManager hostelManagementManager = context.getBean(HostelManagementManager.class);

//            final SalaryConfigurationManager salaryConfigurationManager = context.getBean(SalaryConfigurationManager.class);
//            List<PayHeadConfiguration> payHeadConfigurationList = new ArrayList<>();
//            payHeadConfigurationList.add(new PayHeadConfiguration(0, "ESIC", PayHeadType.DEDUCTION, true, PayHeadTag.ESIC, false,false,true, null));
//            payHeadConfigurationList.add(new PayHeadConfiguration(0, "EPF", PayHeadType.DEDUCTION, true, PayHeadTag.EPF, false,false,true, null));
//            boolean result = salaryConfigurationManager.addPayHeadConfigurations(101, payHeadConfigurationList);
//            System.out.println(result);

//            final GatePassResponse gatePassResponse = hostelManagementManager.addGatePassDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new GatePassPayload(101,190,null,null,1732970400,null,"**********","personal work", GatePassStatus.CANCELLED, new ArrayList<>(Collections.singleton(new GatePassStudentPayload(UUID.fromString("060f848e-59cd-4657-a25f-f667c9ae4522"), null))),false, 1732939200, 1733025600, true));
//            System.out.println(gatePassResponse);
//
////            SearchResultWithPagination<StudentLite> studentsInAcademicSession = studentManager.liteSearchStudentsInAcademicSesison(101, "sa",
////                    190, null, 0, 20, null, false);
//         boolean is =transportAttendanceManager.addTransportAttendance(101, 191, attendanceTypeList);
//         System.out.println(is);
//            final HostelManagementManager hostelManagementManager = context.getBean(HostelManagementManager.class);
//            final GatePassResponse gatePassResponse = hostelManagementManager.addGatePassDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new GatePassPayload(101,190,null,null,1732970400,null,"**********","personal work", GatePassStatus.CANCELLED, new ArrayList<>(Collections.singleton(new GatePassStudentPayload(UUID.fromString("060f848e-59cd-4657-a25f-f667c9ae4522"), null))),false, 1732939200, 1733025600, true));
//            System.out.println(gatePassResponse);

//            SearchResultWithPagination<StudentLite> studentsInAcademicSession = studentManager.liteSearchStudentsInAcademicSesison(101, "sa",
//                    190, null, 0, 20, null, false);
////            System.out.println(studentsInAcademicSession);
//            final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//            UUID student_id = UUID.fromString("e4b83461-dc70-44f0-bf18-f6081e7044b9");
//            StudentEnrollmentAssignPayload studentEnrollmentAssignPayload = studentAdmissionManager.getStudentEnrollmentAssignPayloadDetails(101, 256, student_id);
            //System.out.println(studentEnrollmentAssignPayload);
//            final VisitorDetailsManager visitorDetailsManager = context.getBean(VisitorDetailsManager.class);
//            Set<VisitorStatus> set = new HashSet<>();
//            set.add(VisitorStatus.APPROVED);
//             final UUID VisitorId = visitorDetailsManager.addVisitorDetails(101,190, new VisitorDetailsPayload(101, 190, null, "jatin", 972259200, Gender.getGender("MALE"), "**********", "<EMAIL>",1729641600, "Business Meeting", UUID.fromString("3f071d88-14ae-439a-bfa4-3439a8c18fbf"), VisitorStatus.getVisitorStatus("PENDING")), null, null,null,null,null);




//            final VisitorDetailsManager visitorDetailsManager = context.getBean(VisitorDetailsManager.class);
//            Set<VisitorStatus> set = new HashSet<>();
//            set.add(VisitorStatus.APPROVED);
////             final UUID VisitorId = visitorDetailsManager.addVisitorDetails(101,190, new VisitorDetailsPayload(101, 190, null, "jatin", 972259200, Gender.getGender("MALE"), "**********", "<EMAIL>",1729641600, "Business Meeting", UUID.fromString("3f071d88-14ae-439a-bfa4-3439a8c18fbf"), VisitorStatus.getVisitorStatus("PENDING")), null, null,null,null,null);

////             final boolean updateVisitorDetails = visitorDetailsManager.updateVisitorDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new VisitorDetailsPayload(101, 190, UUID.fromString("40ee11ce-a363-4ebf-9f25-10352a5a6b0f"), "Jony Doe", 972259200, Gender.getGender("MALE"), "**********", "<EMAIL>",1729641600, "Business Meeting",null, VisitorStatus.getVisitorStatus("PENDING")),null,null,null,null);
////            final List<VisitorDetails> visitorDetailsList = visitorDetailsManager.getVisitorDetails(101,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, set , 100, 0);
////            final VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(101, 190, UUID.fromString("30c869d2-1954-4f29-baa9-9e79eacd9d64"));
////                boolean visitorDetails = visitorDetailsManager.updateVisitorAssignedStaff(101,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190,  UUID.fromString("30c869d2-1954-4f29-baa9-9e79eacd9d64"), UUID.fromString("72fa6adf-5383-4614-a528-abf9c2e04f67"));
//            final boolean updateVisitorStatus = visitorDetailsManager.updateVisitorStatus(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190,  UUID.fromString("12b2a177-50ab-45bc-a4b3-d321924c5df0"),
//                    VisitorStatus.APPROVED, "first visit", null);
//

//             final boolean updateVisitorDetails = visitorDetailsManager.updateVisitorDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new VisitorDetailsPayload(101, 190, UUID.fromString("40ee11ce-a363-4ebf-9f25-10352a5a6b0f"), "Jony Doe", 972259200, Gender.getGender("MALE"), "**********", "<EMAIL>",1729641600, "Business Meeting",null, VisitorStatus.getVisitorStatus("PENDING")),null,null,null,null);
//            final List<VisitorDetails> visitorDetailsList = visitorDetailsManager.getVisitorDetails(101,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, set , 100, 0);
//            final VisitorDetails visitorDetails = visitorDetailsManager.getVisitorDetailsByVisitorId(101, 190, UUID.fromString("30c869d2-1954-4f29-baa9-9e79eacd9d64"));
//                boolean visitorDetails = visitorDetailsManager.updateVisitorAssignedStaff(101,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190,  UUID.fromString("30c869d2-1954-4f29-baa9-9e79eacd9d64"), UUID.fromString("72fa6adf-5383-4614-a528-abf9c2e04f67"));
//            final boolean updateVisitorStatus = visitorDetailsManager.updateVisitorStatus(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190,  UUID.fromString("12b2a177-50ab-45bc-a4b3-d321924c5df0"),
//                    VisitorStatus.APPROVED, "first visit", null);
//
//                    VisitorStatus.APPROVED, "first visit", null);    public GatePassPayload(int instituteId, int academicSessionId, UUID gatePassId, String gatePassNumber, Integer gatePassDate, String name, String reason, GatePassStatus gatePassStatus, List<GatePassStudentPayload> gatePassStudentPayloadList, boolean sendEarlyDepartureMessage, boolean sendMobileAppNotification, Integer departureDate, Integer expectedReturnDate) {
//            final GatePassResponse gatePassResponse = hostelManagementManager.addGatePassDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new GatePassPayload(101,190,null,null,null,null,"personal work", GatePassStatus.CANCELLED, new ArrayList<>(Collections.singleton(new GatePassStudentPayload(UUID.fromString("060f848e-59cd-4657-a25f-f667c9ae4522"), null))),false, false, null, null));
//            final FrontDeskManager frontDeskManager = context.getBean(FrontDeskManager.class);
//            final GatePassResponse gatePassResponse = frontDeskManager.addGatePassDetails(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), new GatePassPayload(101,190,null,"G-10", 1732625813,"aman","**********","<EMAIL>",null,"personal work",null, GatePassStatus.ACTIVE, new ArrayList<>(Collections.singleton(new GatePassStudentPayload(UUID.fromString("060f848e-59cd-4657-a25f-f667c9ae4522"), null))),false, false));
//            System.out.println(gatePassResponse);
//            final boolean updateGatePassStatus = hostelManagementManager.updateGatePassStatus(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), UUID.fromString("1fc039fb-7ced-415e-aa03-57d5737240df"),
//                    GatePassStatus.CANCELLED);
//                    System.out.println(updateGatePassStatus);
//                    VisitorStatus.APPROVED, "first visit", null);


//                    System.out.println(updateVisitorStatus);
            /* String razorpayPaymentWebhookResponseString = "{'entity':'event','account_id':'acc_Ofc5Oo5it3xvUC','event':'payment.failed','contains':['payment'],'payload':{'payment':{'entity':{'id':'pay_OwIK4ZpOCyACgG','entity':'payment','amount':200,'currency':'INR','status':'failed','order_id':'order_OwIJyzZBWTdVX3','invoice_id':null,'international':false,'method':'upi','amount_refunded':0,'refund_status':null,'captured':false,'description':null,'card_id':null,'bank':null,'wallet':null,'vpa':null,'email':'<EMAIL>','contact':'+************','notes':[],'fee':null,'tax':null,'error_code':'BAD_REQUEST_ERROR','error_description':'You may have cancelled the payment or there was a delay in response from the UPI app.','error_source':'customer','error_step':'payment_authentication','error_reason':'payment_cancelled','acquirer_data':{'rrn':null},'created_at':**********,'provider':null,'upi':{'vpa':null},'reward':null}}},'created_at':**********}";



//            /data-server/2.0/transport/reports/10030/view-report/STUDENT_TRANSPORT_FEES_LEVEL_DUES?academic_session_id=32&requiredHeaders=serial_number,student_admission_number,student_name,student_status,student_class_name,student_father_name,student_primary_contact_number,father_contact_number,mother_contact_number,transport_pickup_route_name,transport_drop_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_fees_name,transport_route_area_assigned_amount,transport_route_area_amount_collected,transport_route_area_given_discount,transport_route_area_discount_to_be_given,transport_route_area_due_amount,transport_paid_fine_amount,transport_due_fine_amount&requiredStandards=&fee_ids=&only_enrolled_students=false&parent_area_ids=&area_ids=&pickup_ids=&drop_ids=&user_id=2a3a725b-163a-4dcc-a1f6-8fe5c1377262


//            final PushNotificationManager pushNotificationManager = context.getBean(PushNotificationManager.class);
//
//            Map<String, String> dataPayload = new HashMap<>();
//            dataPayload.put(PushNotificationManager.USER_ID_KEY, "9559c591-bde9-413f-bf29-668a8e02ec05");
////                    "fE654C0NSp-IEUsuXw-S68:APA91bGPYgRA0R2TpUgNA1q1c_TqS0bTsep3Acrx3_CmUsqEfIN6yKK1jddRAwQBgeSU9fTqdne0rbaBtemYmAJvTdTGVFBiE_gbhGs28_2pRHDiwbYYFYeCyCtcm3_5d6MZF-5Tz3ao"
//            dataPayload.put(PushNotificationManager.FLUTTER_REQUIRED_CLICK_ACTION_KEY, FLUTTER_REQUIRED_CLICK_ACTION_VAL);
//            List<PushNotificationPayload> pushNotificationPayloadList = new ArrayList<>();
//            PushNotificationPayload pushNotificationPayload = new PushNotificationPayload("test", "test", null,
//                    "eRP69BFMQ8KbqyN4LdFZXU:APA91bHTRwmWWXf0MuuQnM_V5RH7IU7Xc8ER-1s0d7uAG7Yq1uB6xPf0kflpQQfNJUV7JZBSXs3rbKuynxyCEdfr82F6zTkkzjPfamdA3B_d1hGLKP9NC0SbXq35HVU31lWTwq266UWG", dataPayload);
//            pushNotificationPayloadList.add(pushNotificationPayload);
//            BulkPushNotificationPayload bulkPushNotificationPayload = new BulkPushNotificationPayload(pushNotificationPayloadList);
//            pushNotificationManager.sendBulkNotificationsAsync(110, bulkPushNotificationPayload);

//            pushNotificationManager.testing(pushNotificationPayload);


//            String razorpayPaymentWebhookResponseString = "{'entity':'event','account_id':'acc_Ofc5Oo5it3xvUC','event':'payment.failed','contains':['payment'],'payload':{'payment':{'entity':{'id':'pay_OwIK4ZpOCyACgG','entity':'payment','amount':200,'currency':'INR','status':'failed','order_id':'order_OwIJyzZBWTdVX3','invoice_id':null,'international':false,'method':'upi','amount_refunded':0,'refund_status':null,'captured':false,'description':null,'card_id':null,'bank':null,'wallet':null,'vpa':null,'email':'<EMAIL>','contact':'+************','notes':[],'fee':null,'tax':null,'error_code':'BAD_REQUEST_ERROR','error_description':'You may have cancelled the payment or there was a delay in response from the UPI app.','error_source':'customer','error_step':'payment_authentication','error_reason':'payment_cancelled','acquirer_data':{'rrn':null},'created_at':**********,'provider':null,'upi':{'vpa':null},'reward':null}}},'created_at':**********}";
//
//            System.out.println("webhookDataString : " + razorpayPaymentWebhookResponseString);
//
//            RazorpayPaymentWebhookResponse razorpayPaymentWebhookResponse = GSON.fromJson(razorpayPaymentWebhookResponseString, RazorpayPaymentWebhookResponse.class);
//
//            System.out.println("webhookData : " + razorpayPaymentWebhookResponse);


            System.out.println("webhookData : " + razorpayPaymentWebhookResponse); */
//            webhookData : RazorpayPaymentWebhookResponse{entity='event', accountId='null', event='payment.failed', contains=[payment], razorpayPaymentWebhookResponsePayload=null, createdAt=null}

//            webhookData : RazorpayPaymentWebhookResponse{entity='event', accountId='acc_Ofc5Oo5it3xvUC', event='payment.failed', contains=[payment], razorpayPaymentWebhookResponsePayload=RazorpayPaymentWebhookResponsePayload{razorpayPaymentWebhookResponsePaymentPayload=null}, createdAt=**********}


//            /data-server/2.0/examination/student-exams-result/c7d57041-6d24-45eb-92bb-c67841d4493b/field-data?institute_id=101&academic_session_id=33&section_ids_str=&exam_ids_str=********-590c-4f16-88e1-a0789ac2b091 HTTP/1.1" 200 4826
///data-server/2.0/examination/student-report-card-status/cb352822-3b78-44da-b513-8332b900dc46?institute_id=101&academic_session_id=33&section_ids_str=85&report_card_id=a804e6c8-6525-11ef-b658-e55b1866b766 HTTP/1.1" 200 7918


//            final ExaminationManager examinationManager = context.getBean(ExaminationManager.class);


              /* final StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
              int institute_id = 101;
              UUID student_id = UUID.fromString("05863d09-5132-4959-a9b7-da9e64e24f2e");
              StudentLeavePayload studentLeavePayload = new StudentLeavePayload(null, 256, **********, **********, student_id, LeaveTransactionStatus.PENDING, student_id, **********, "Medical leave", null);
              boolean status = studentLeaveManagementManager.addStudentLeaves(institute_id, studentLeavePayload, null);
              System.out.println(status);
              List<StudentLeaveDetails> studentLeaveDetailsList = studentLeaveManagementManager.getStudentLeaves(institute_id, 256, student_id);
              System.out.println(studentLeaveDetailsList); */

              /* final StaffManager staffManager = context.getBean(StaffManager.class);
              int institute_id = 101;
              UUID user_id = UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693");
              UUID staff_id = UUID.fromString("81545ba4-d34f-44a7-a47f-44727aeb0a5e");
              boolean status = staffManager.onboardStaff(staff_id, false, institute_id, user_id, null);
              System.out.println(status); */

//              final StaffLeaveManagementManager staffLeaveManagementManager = context.getBean(StaffLeaveManagementManager.class);
//              int institute_id = 101;
//              UUID transactionId = UUID.randomUUID();
//              UUID staff_id = UUID.fromString("78d3d7f2-7bdf-4616-924d-297d202ab07e");
//              UUID user_id = UUID.fromString("6c711b90-ca3a-495a-b013-27c3dbdc8693");
//              UUID leaveTranscationId = UUID.fromString("a50d3b53-b26a-4d6b-8f5e-67039989e750");
//              int academicSessionId = 256;
//              int startDate = **********;
//              int endDate = **********;
              /* LeaveType leaveType = new LeaveType(162,"Sick Leave","SL",LeavePaymentType.PAID,UserType.STAFF,"ill",null);
              final StaffLeavePayload staffLeavePayload = new StaffLeavePayload(transactionId, academicSessionId, startDate, endDate, staff_id, LeaveTransactionStatus.PENDING, staff_id, 1726790400, "Sick Leave", null, leaveType);
              Set<UUID> LeaveId = staffLeaveManagementManager.addStaffLeaves(institute_id, staffLeavePayload, null);
              System.out.println(LeaveId); */
             /*  List<StaffLeaveDetails> staffLeaveDetails = staffLeaveManagementManager.getAllLeaves(institute_id, academicSessionId, user_id);
              System.out.println("Leave : "+ staffLeaveDetails); */
              /* LeaveReviewPayload leaveReviewPayload = new LeaveReviewPayload(academicSessionId, leaveTranscationId, LeaveTransactionStatus.APPROVED, user_id, 1726790400, "Approved");
              boolean LeaveStatus = staffLeaveManagementManager.updateLeaveReviewDetails(institute_id, user_id, true, leaveReviewPayload);
              System.out.println(LeaveStatus); */

//
//            int instituteId = 101;
//            int academicSessionId = 33;
//            UUID standardId = UUID.fromString("cb352822-3b78-44da-b513-8332b900dc46");
//            UUID reportCardId = UUID.fromString("a804e6c8-6525-11ef-b658-e55b1866b766");
//            String sectionIdSetStr = "85";
//            Set<Integer> sectionIdSet = StringHelper.convertStringToSetInteger(sectionIdSetStr);
//
//            final List<StudentReportCardStatusDetails> studentReportCardStatusDetailsList = examinationManager
//                    .getStudentReportCardStatusDetails(instituteId, academicSessionId, standardId, sectionIdSet, reportCardId, null);
//
//            for(StudentReportCardStatusDetails studentReportCardStatusDetails : studentReportCardStatusDetailsList) {
//                System.out.println(studentReportCardStatusDetails.toString());
//            }

//            final AppointmentDetailsManager appointmentDetailsManager = context.getBean(AppointmentDetailsManager.class);
//            AppointmentDetailsPayload appointmentDetailsPayload = new AppointmentDetailsPayload(101, 190, UUID.fromString("ee41aa48-f724-4117-860c-6804c862870d"), UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), UserType.STUDENT, "Parent", null, UUID.fromString("03c7da43-e225-417b-b12c-45b7c6d2f619"), UserType.STAFF, 1625149900, "i am parent ready ", AppointmentStatus.RAISED, 0);
////            final UUID appointmentId = appointmentDetailsManager.addAppointmentDetails(101,  UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), appointmentDetailsPayload);
////            System.out.println(appointmentId);
//            final boolean updateAppointment = appointmentDetailsManager.updateAppointmentDetails(101, UUID.fromString("07984426-6efc-4e30-8b20-3ded2fb545c6"), 190,  UUID.fromString("6895a1c1-541b-4126-a305-fe3df64775c4"), appointmentDetailsPayload);
//            Set<AppointmentStatus> set = new HashSet<>();
//            set.add(AppointmentStatus.DELETED);
//            set.add(AppointmentStatus.RAISED);
//            set.add(AppointmentStatus.COMPLETED);
//            set.add(AppointmentStatus.ACCEPTED);
//
//            final List<StudentAppointmentDetails> studentAppointmentDetailsList = appointmentDetailsManager.getAppointmentDetails(101, UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), 190, UserType.STUDENT,set, 2, 0);
//            System.out.println(studentAppointmentDetailsList);
//              final AppointmentDetails appointmentDetails = appointmentDetailsManager.getAppointmentDetailsByAppointmentId(101,  UUID.fromString("03c7da43-e225-417b-b12c-45b7c6d2f619"), 190, UUID.fromString("8dc803fa-9d98-4f95-941e-d5e2fca67cab"));
//              System.out.println(appointmentDetails);
//            final boolean updateAppointmentStatus = appointmentDetailsManager.updateAppointmentStatus(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, UUID.fromString("79efa393-c0b9-4699-a76e-717f9e74dd82"),
//                    AppointmentStatus.ACCEPTED, UserType.ADMIN, null);
////            System.out.println(updateAppointmentStatus);
//            final StudentAppointmentDetails studentAppointmentDetails = appointmentDetailsManager.getAppointmentDetailsByAppointmentId(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), 190, UUID.fromString("10179838-759c-429c-9152-2447194b85bc"));
//System.out.println(studentAppointmentDetails);


//            int instituteId = 101;
//            int academicSessionId = 33;
//            UUID standardId = UUID.fromString("c7d57041-6d24-45eb-92bb-c67841d4493b");
//            Set<UUID> examIdSet = new HashSet<>(Arrays.asList(UUIDUtils.getUUID("********-590c-4f16-88e1-a0789ac2b091")));
//            Set<UUID> additionalCourseIdSet = null;
//            String sectionIdSetStr = null;
//            final StudentManagementFieldDetails studentManagementFieldDetails = examReportCardManager
//                    .getStudentExamResultDetails(instituteId, academicSessionId, standardId,
//                            //Only supporting to update roll number and section from examination for now.
//                            examIdSet, additionalCourseIdSet, sectionIdSetStr, "SECTION,ROLL_NUMBER");
//
//            for(StudentManagementOutputFieldStudentData studentManagementOutputFieldStudentData : studentManagementFieldDetails.getStudentDataList()) {
//                System.out.println(studentManagementOutputFieldStudentData.getStudentExamResultDetails().toString());
//            }

//            /data-server/2.0/staff-diary/download-remark-document/eb8ec092-73e1-4eb4-9b9c-7318df463121/1d6f92fc-2cff-41a2-bab7-0f5527f20920?institute_id=110

//            final DownloadDocumentWrapper<Document<DiaryRemarkDocumentType>> documentWrapper = staffDiaryManager.downloadDocument(110,
//                    UUIDUtils.getUUID("eb8ec092-73e1-4eb4-9b9c-7318df463121"), UUIDUtils.getUUID("1d6f92fc-2cff-41a2-bab7-0f5527f20920"));

//            final List<StaffDiaryRemarkDetails> remarkDetails = staffDiaryManager.getRemarksBySendToId(110, 153,
//                    UUIDUtils.getUUID("140967ea-c2b2-464a-af50-c304b7a8418d"), null, null, null);

//            staffDiaryManager.getRemarkDetails(110, 153, null, null, userIdSet);

//            127.0.0.1 - - [20/Jul/2024:00:58:13 +0530] "GET /data-server/2.0/student-diary/all-remark-details-with-pagination
//            ?institute_id=110&academic_session_id=153&search_text=&category_ids=&user_ids=&offset=0&limit=10 HTTP/1.1" 200 -

//            DiaryRemarkDetailsPayload diaryRemarkDetailsPayload = new DiaryRemarkDetailsPayload(
//            110, 153, null, "My Daily Status", "My Daily Status is that I am Present in the school.", 95,
//                    Arrays.asList(UUIDUtils.getUUID("dfcf66a8-a117-43a9-af11-97c5ccad1951")),
//                    RemarkUserType.STAFF, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), null
//            );
//
////            {'academicSessionId': 153, 'categoryId': '95', 'title': 'My Daily Status', 'description': 'My Daily Status is that I am Present in the school.vsofduj', 'studentId': ['dfcf66a8-a117-43a9-af11-97c5ccad1951'], 'instituteId': '110'}
//            final UUID remarkId = staffDiaryManager.addRemarks(110, 153,
//                    UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), diaryRemarkDetailsPayload, null);

//            SearchResultWithPagination<DiaryRemarkDetails> remarkDetails = studentDiaryManager.getRemarkDetailsWithPagination(
//                    110, 153, "", null, null, 0, 10);

//            Set<Integer> categoryIdInt = StringHelper.convertStringToSetInteger("");
//            Set<UUID> userIdList = StringHelper.convertStringToSetUUID("");
//            final SearchResultWithPagination<DiaryRemarkDetails> remarkDetails = studentDiaryManager.getRemarkDetailsWithPagination(
//                    110, 153, "", categoryIdInt, userIdList, 0, 10);
//
//            System.out.println(documentWrapper);


//            final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//            StudentClassChangePayload studentClassChangePayload = new StudentClassChangePayload(10225, 194,
//                    UUIDUtils.getUUID("5b5465dd-3305-4a99-9c72-80ed98ef5734"),
//                    UUIDUtils.getUUID("b97c695b-57c8-4f6f-b21a-5a0f6bbcd119"), 2835,
//                    UUIDUtils.getUUID("9cb5ed05-dffd-4e06-94bc-31a9d515e126"), null,
//                    null, true);
//            final boolean classChanged = studentAdmissionManager.studentClassChange(10225, 194,
//                    studentClassChangePayload, UUIDUtils.getUUID("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"));

//            int instituteId, int currentSessionId, UUID standardId,
//                    Integer sectionId, List<UUID> promotingStudentList, List<UUID> feeStructureIdsList,
//            boolean carryForwardDiscount, List<UUID> discountStructureIdsList, boolean carryForwardTransport
//            StudentPromotionDetails studentPromotionDetails = new StudentPromotionDetails(10225, 110,
//                    UUID.fromString("9cb5ed05-dffd-4e06-94bc-31a9d515e126"), null,
//                    Arrays.asList(UUID.fromString("4a5f472a-3a97-4637-9a3a-3d2acd0fb9d7")),
//                    null, false, null, false);
//            final boolean promoted = studentAdmissionManager.promoteStudents(10225, 110,
//                    studentPromotionDetails, UUIDUtils.getUUID("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"));
////
//            System.out.println("promoted : " + promoted);
//            final CourseManager courseManager = context.getBean(CourseManager.class);
////            int instituteId, int currentSessionId, UUID standardId, Integer sectionId, Set<UUID> studentIdSet, int nextSessionId, UUID newStandardId, UUID userId
//            10225
//            110
//            b97c695b-57c8-4f6f-b21a-5a0f6bbcd119
//                    [c2ee84dd-2ee1-4c0d-99d3-64806ef13157]
//            194
//            9cb5ed05-dffd-4e06-94bc-31a9d515e126
//            courseManager.migrateOptionalCoursesDueToClassChangeOrPromotion(
//                    10225, 110, UUID.fromString("b97c695b-57c8-4f6f-b21a-5a0f6bbcd119"), 1057,
//                    new HashSet<>(Arrays.asList(UUID.fromString("c2ee84dd-2ee1-4c0d-99d3-64806ef13157"))),
//                    194, UUID.fromString("9cb5ed05-dffd-4e06-94bc-31a9d515e126"),
//                    UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"));
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
////            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
////            PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////		/2.0/attendance/reports/view-report/101/"ATTENDANCE_DETAILS_IN_A_MONTH"?academic_session_id=190&standard_id=&attendance_status=&attendance_type_id=&start=1717180200&end=1717353000&single=-1&user_id=08dc0556-b8a1-4acd-a6fa-759ca864df9e&requiredHeaders=sr_no,attendance_student_name,admission_no,attendance_student_class,attendance_student_roll_number,total_days,present_count,absent_count,leave_count,half_day_count,holiday_count,unmarked_count,present_percent
//            final ReportDetails reportDetails = attendanceReportGenerator.generateReport(101, 190
//                    , "ATTENDANCE_DETAILS_IN_A_MONTH","", "", "",
//                    1717180200, 1717353000, -1, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), DownloadFormat.PDF, "attendance_student_name,admission_no,attendance_student_class,attendance_student_roll_number,total_days,present_count,absent_count,leave_count,half_day_count,holiday_count,unmarked_count,present_percent");
////		final DocumentOutput documentOutput = reportDetails;
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//            reportOutput.getReportContent().writeTo(outputStream);
//          final
//=======
//            final StudentDiaryManager studentDiaryManager = context.getBean(StudentDiaryManager.class);
//            /2.0/student-diary/update-diary-remark?institute_id=101&academic_session_id=190&user_id=08dc0556-b8a1-4acd-a6fa-759ca864df9e&edit_attachments=true
//                [('update_diary_remark_payload', '{"academicSessionId": 190, "remarkId": "9a73763b-5e25-4f26-b150-6dd6a76868e5", "categoryId": "88", "title": "qe", "description": "", "studentId": ["0c07c4af-4c9e-4494-a345-cc6f2a228c2b"], "instituteId": "101"}'),
//            ('file', <InMemoryUploadedFile: school (5) (1).jpg (application/octet-stream)>)]
//            DiaryRemarkDetailsPayload diaryRemarkDetailsPayload = academicSessionId: 190, "remarkId": "9a73763b-5e25-4f26-b150-6dd6a76868e5", "categoryId": "88", "title": "qe", "description": "", "studentId": ["0c07c4af-4c9e-4494-a345-cc6f2a228c2b"], "instituteId": "101"};
            // Assuming you have a constructor or setter methods to set these values.
//            DiaryRemarkDetailsPayload diaryRemarkDetailsPayload = new DiaryRemarkDetailsPayload();
//
//            diaryRemarkDetailsPayload.setAcademicSessionId(190);
//            diaryRemarkDetailsPayload.setRemarkId(UUID.fromString("9a73763b-5e25-4f26-b150-6dd6a76868e5"));
//            diaryRemarkDetailsPayload.setCategoryId(88);
//            diaryRemarkDetailsPayload.setTitle("s");
//            diaryRemarkDetailsPayload.setDescription("");
//            diaryRemarkDetailsPayload.setStudentId(Arrays.asList(UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b")));
//            diaryRemarkDetailsPayload.setInstituteId(101);
//            final ComplainBoxManager complainBoxManager = context.getBean(ComplainBoxManager.class);
//            StudentComplaintMetadataPayload complaintMetadataPayload = new StudentComplaintMetadataPayload(101, null, "new complaint", "complaint is regarding the work of complaint box", "101", "Mukul", UUID.fromString("89d18fa9-3f29-4ba2-b521-b10315a285fe"), ComplainStatus.OPEN, 1, null, UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), null, null, null);
////
//            UUID result = complainBoxManager.addComplainMetadata(complaintMetadataPayload,101, UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), null);
//            if (result == null) {
//                throw new ApplicationException(
//                        new ErrorResponse(ApplicationErrorCode.INVALID_DIARY_REMARK_CATEGORY_TYPE, "Unable to add student remarks."));
//            }
//            System.out.println(result);
//            final ComplainBoxPushNotificationHandler complainBoxPushNotificationHandler
//            complainBoxPushNotificationHandler.sendComplainBoxNotificationsAsync(101, result, complaintMetadataPayload.getTitle(), UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"));
//// If you need to set createdBy and updatedBy fields, you can do so as follows:
//            diaryRemarkDetailsPayload.setCreatedBy(null);  // Replace null with actual UUID if available
//            diaryRemarkDetailsPayload.setUpdatedBy(null);  // Replace null with actual UUID if available
//
//            final boolean remarkUpdated = studentDiaryManager.updateRemarks(diaryRemarkDetailsPayload, 101,
//                    190, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), true, null);
//            System.out.println(remarkUpdated);

//            final StudentReportsGenerator studentReportsGenerator = context.getBean(StudentReportsGenerator.class);
//            final ReportDetails reportDetails = studentReportsGenerator.generateReport(101,
//                    190,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), StudentReportType.SECTION_WISE_STUDENT_COUNT_REPORT ,DownloadFormat.EXCEL, null,
//                    null,null,null,null, null, null, false);
//
//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
////
////
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = new FileOutputStream(DEST1 + reportOutput.getReportName());
//            reportOutput.getReportContent().writeTo(outputStream);

//            final InstituteOnBoardingManager instituteOnBoardingManager = context.getBean(InstituteOnBoardingManager.class);
//
//            final StudentDataIngestionExecutionSummary studentDataIngestionExecutionSummary = instituteOnBoardingManager
//                    .ingestStudentsDryRun(110, 70, "637JS6-1717569661864",
//                            UUID.fromString("59f8c344-8850-46bb-8101-3b671b3f6cda"));
//
//            System.out.println(studentDataIngestionExecutionSummary.getFailureCount());
//
//            for(StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionExecutionSummary.getExecutionFailureStudentDataIngestionRecords()) {
//                for(String error : studentDataIngestionRecord.getErrors()) {
//                    System.out.println("Exec : " + error);
//                }
//            }
//
//            for(StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionExecutionSummary.getFailureStudentDataIngestionRecords()) {
//                for(String error : studentDataIngestionRecord.getErrors()) {
//                    System.out.println("Fail : " + error);
//                }
//            }
//
//            for(StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionExecutionSummary.getAdmitFailureStudentDataIngestionRecords()) {
//                for(String error : studentDataIngestionRecord.getErrors()) {
//                    System.out.println("Admit : " + error);
//                }
//            }
//            final StaffAttendanceManager staffAttendanceManager = context.getBean(StaffAttendanceManager.class);
//            final StaffAttendancePushNotificationHandler staffAttendancePushNotificationHandler = context.getBean(StaffAttendancePushNotificationHandler.class);
//            final StaffAttendanceSMSHandler staffAttendanceSMSHandler = context.getBean(StaffAttendanceSMSHandler.class);
//
//            Integer attendanceDate = 1719582837;
//            List<StaffAttendanceInput> staffAttendanceInputList = new ArrayList<>();
//            UUID staffId = UUID.fromString("b320814c-b269-4ef7-8a04-a7eade96a4b7");
//            Time timeOfAction = new Time(10, 0, 0);
//            staffAttendanceInputList.add(new StaffAttendanceInput(staffId, timeOfAction));
//            StaffAttendancePayloadV2 staffAttendancePayload = new StaffAttendancePayloadV2(attendanceDate, staffAttendanceInputList);
//            final boolean success = staffAttendanceManager.saveAPIStaffAttendanceDetails(110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//                    staffAttendancePayload);
//
//            /**
//             * currently supporting staff attendance from web
//             * TODO: when mobile staff attendance flow is added need to update the flow here.
//             */
//            staffAttendancePushNotificationHandler.sendAttendanceUpdateNotificationsAsync(110, AttendanceInputType.WEB, staffAttendancePayload);
//
//            staffAttendanceSMSHandler.bulkSend(110, staffAttendancePayload, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));


//            final StudentManager studentManager = context.getBean(StudentManager.class);
//            StudentManagementUpdateFieldPayload studentManagementUpdateFieldPayload = new StudentManagementUpdateFieldPayload();
//            studentManagementUpdateFieldPayload.setDataType(StudentManagementDataType.BASIC_DATA);
//            studentManagementUpdateFieldPayload.setRequiredFields(Arrays.asList(StudentManagementField.HOUSE));
//            List<StudentManagementUpdateFieldStudentData> studentDataList = new ArrayList<>();
//            List<StudentManagementUpdateFieldValue> fieldValueList = new ArrayList<>();
//            fieldValueList.add(new StudentManagementUpdateFieldValue(StudentManagementField.HOUSE, "c51b234b-5662-420c-b8dc-3bb49d3b8224"));
//            studentDataList.add(new StudentManagementUpdateFieldStudentData(UUID.fromString("b5c06a9f-376f-4261-b3ef-683fdbf04068"), fieldValueList));
//            studentManagementUpdateFieldPayload.setStudentDataList(studentDataList);
//            final boolean success = studentManager.updateClassStudentsFieldData(10225, 194,
//                    UUID.fromString("b97c695b-57c8-4f6f-b21a-5a0f6bbcd119"), "2835", UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"),
//                    studentManagementUpdateFieldPayload);
//
//
//            System.out.println(success);
//            final InstituteOnBoardingManager instituteOnBoardingManager = context.getBean(InstituteOnBoardingManager.class);
//
//            final StudentDataIngestionExecutionSummary studentDataIngestionExecutionSummary = instituteOnBoardingManager
//                    .ingestStudentsDryRun(110, 70, "637JS6-1717569661864",
//                            UUID.fromString("59f8c344-8850-46bb-8101-3b671b3f6cda"));
//
//            System.out.println(studentDataIngestionExecutionSummary.getFailureCount());
//
//            for(StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionExecutionSummary.getExecutionFailureStudentDataIngestionRecords()) {
//                for(String error : studentDataIngestionRecord.getErrors()) {
//                    System.out.println("Exec : " + error);
//                }
//            }
//
//            for(StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionExecutionSummary.getFailureStudentDataIngestionRecords()) {
//                for(String error : studentDataIngestionRecord.getErrors()) {
//                    System.out.println("Fail : " + error);
//                }
//            }
//
//            for(StudentDataIngestionRecord studentDataIngestionRecord : studentDataIngestionExecutionSummary.getAdmitFailureStudentDataIngestionRecords()) {
//                for(String error : studentDataIngestionRecord.getErrors()) {
//                    System.out.println("Admit : " + error);
//                }
//            }

//            AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
//            Map<String, List<ReportHeaderAttribute>> REPORT_HEADERS = new HashMap<>();
//            REPORT_HEADERS = attendanceReportGenerator.getReportHeader();
//
//System.out.println(REPORT_HEADERS);

            ///data-server/2.0/institute-onboarding/ingest-student-dryrun/110?academic_session_id=70&file_upload_id=637JS6-1717569661864&user_id=59f8c344-8850-46bb-8101-3b671b3f6cda
//         final StaffManager staffManager = context.getBean(StaffManager.class);
//            final boolean updated = staffManager.updateBulkStaffDetails(instituteId, academicSessionId,
//                    updateBulkStaffDetailsPayload, userId);
//            System.out.println(updated);
//            final StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
//            LeaveReviewPayload leaveReviewPayload = new LeaveReviewPayload(153,
//                    UUID.fromString("718a14e5-ab23-4d27-aec5-ebc4664a21d8"), LeaveTransactionStatus.APPROVED,
//                    UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), DateUtils.now(), "");
//            final boolean leaveReviewDetailsUpdated = studentLeaveManagementManager.updateLeaveReviewDetails(
//                    110, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"),
//                    true, leaveReviewPayload);
//
//            System.out.println(leaveReviewDetailsUpdated);

//            /data-server/2.0/student-diary/update-diary-remark/110?institute_id=110&academic_session_id=153&user_id=74cd36ca-180d-4360-8fe5-f95472bf4e51&edit_attachments=false
//            {"bookNumber": null, "tcNumber": "TC-2", "tcGenerationDate": 1717572613, "tcSchoolDetails": {"affiliationNumber": "16", "diseCode": "12", "schoolCode": null,
//                    "primaryEducationAffiliationNumber": "", "rteAffiliationNumber": null},
//                "tcStudentDetails": {"admissionNumber": "EMB-23", "studentName": "3", "admissionDate": 1716316200, "admissionClass": "",
//                    "fatherGuardianName": null, "motherName": null, "dob": null, "proofOfDoBAtTheTimeOfAdmission": "", "category": null, "nationality": "INDIAN"},
//                "tcStudentLastActiveSessionDetails": {"lastActiveSessionClass": "VII-b", "lastActiveSessionClassInFigures": "", "lastActiveSessionClassInWords": "",
//                    "lastExamTakenWithResult": null, "numberOfTimeExamFailed": null, "scholasticCoursesLastActiveSession": ["Hindi", "Physics", "GK"],
//                "promotionToHigherClass": null, "promotingClassName": null, "promotingClassNameInFigures": null, "promotingClassNameInWords": null,
//                        "lastFeesPaid": null, "discountWithNature": null, "totalWorkingDays": null, "totalAttendedDays": null},
//                "tcOtherDetails": {"nccCadetBoyScoutGirlGuideWithDetails": null, "coCurricularActivities": null},
//                "tcStudentRelievingDetails": {"codeOfConduct": "Excellent", "relieveDate": 1717525800, "relieveReason": "Due to High class pass",
//                    "remarks": null, "dateOfApplicationOfCertificate": null, "datePupilsNameStuckOffTheRolls": null}, "tcnumberFixed": false}
//             final StudentManager studentManager = context.getBean(StudentManager.class);
//            TCStudentDetails tcStudentDetails = new TCStudentDetails("EMB-23", "Student", 1716316200, "",
//					"father"," mother", 1616316200, "", null, "INDIAN");
//            TCSchoolDetails tcSchoolDetails = new TCSchoolDetails( "16", "12",  null, "", "");
//            List<String> courses = new ArrayList<>();
//            courses.add("hindi");
//            courses.add("Physics");
//            courses.add("GK");
//            TCStudentLastActiveSessionDetails tcStudentLastActiveSessionDetails = new TCStudentLastActiveSessionDetails("VII-b",  "",  "",
//                    null,null, courses,  null,  null, null, null, null, null, null, null);
//
//            TCOtherDetails tcOtherDetails = new TCOtherDetails(null, null);
//            TCStudentRelievingDetails tcStudentRelievingDetails =  new TCStudentRelievingDetails("Excellent", 171752580, "Due to High class pass", null, null, null);
//            List<TCPreviousSessionDetailSummary> tcPreviousExamDetails =  new ArrayList<>();
//           TCPreviousSessionDetailSummary prev = new TCPreviousSessionDetailSummary("VII", 1716316200, 1716316800,"345","340","7","Passed - 1","English","as offered","good-good");
//           tcPreviousExamDetails.add(prev);
//           prev = new TCPreviousSessionDetailSummary("VIII", 1716317200, 1716317900, "370","360","7/9","Passed ","English","as chosen","good-excellent");
//           tcPreviousExamDetails.add(prev);
////            prev = new PreviousExamDetails("IX", 1716317208, 1716317100, "370","360","7/10","Promoted ","English","as offered","good-excellent");
////            previousExamDetails.add(prev);
//           StudentTransferCertificateDetails studentTransferCertificateDetails = new StudentTransferCertificateDetails(null, "tc-3", true, 1714933800, tcSchoolDetails,tcStudentDetails, tcStudentLastActiveSessionDetails, tcOtherDetails,tcStudentRelievingDetails, tcPreviousExamDetails);
//            boolean studentTransferCertificateDetailsAdded = studentManager.updateStudentTransferCertificateDetails(101, UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"), studentTransferCertificateDetails);
//            System.out.println(studentTransferCertificateDetails);
//           System.out.println(tcPreviousExamDetails.size());
//           System.out.println(studentTransferCertificateDetailsAdded);
//            final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//            StudentTransferCertificateDetails studentTransferCertificateDetails =
//                    studentAdmissionManager.getStudentTransferCertificateDetails(101, UUID.fromString("c16c73c6-6a19-447f-a79a-fab41284e4e4"));
//            System.out.println(studentTransferCertificateDetails);
//                    "primaryEducationAffiliationNumber": "", "rteAffiliationNumber": null);
//            final StudentDiaryManager studentDiaryManager = context.getBean(StudentDiaryManager.class);
//
//            DiaryRemarkDetailsPayload diaryRemarkDetailsPayload = new DiaryRemarkDetailsPayload(110, 153,
//                    UUID.fromString("26989da6-5ccc-4a8a-bcca-bad0c0a1ccf8"),
//                    "Hello", "Hello 22", 53, Arrays.asList(UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d")),
//                    UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"));
//
//            boolean remarkUpdated = studentDiaryManager.updateStudentRemarks(diaryRemarkDetailsPayload, 110,
//                    153, UUID.fromString("74cd36ca-180d-4360-8fe5-f95472bf4e51"), false, null);
//
//            System.out.println(remarkUpdated);


////            /2.0/homework/homework-details-limit-offset/SAVED?institute_id=110&academic_session_id=153&limit=80&offset=0&user_id=140967ea-c2b2-464a-af50-c304b7a8418d
//            List<DatewiseHomeworkDetails> homeworkDetailsList = homeworkManager.getInstituteHomeworkDetails(
//                    110, 153, 80, 0,
//                    HomeworkStatus.BROADCASTED, UUID.fromString("140967ea-c2b2-464a-af50-c304b7a8418d"));
//
//            System.out.println(homeworkDetailsList.size());

//
//            StudentSiblingListsPayload studentSiblingListsPayload=new StudentSiblingListsPayload();
//            List<List<String>> listOfLists = new ArrayList<>();
//
//            // Create the first list and add strings "1", "2", "3"
//            List<String> firstList = new ArrayList<>();
//            firstList.add("1");
//            firstList.add("2");
//            firstList.add("3");
//
//            // Create the second list and add strings "4", "5"
//            List<String> secondList = new ArrayList<>();
//            secondList.add("4");
//            secondList.add("5");
//
//            // Add the individual lists to the main list
//            listOfLists.add(firstList);
//            listOfLists.add(secondList);
//            studentSiblingListsPayload.setAdmissionNumbers(listOfLists);
//            final boolean siblingsAdded = studentManager.bulkSiblingAssignment(101, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),studentSiblingListsPayload, 190, true);
////            final UserManager userManager = context.getBean(UserManager.class);
//
////            List<UserCredentials> userCredentialsList = new ArrayList<>();
////            userCredentialsList.add(new UserCredentials("admin10225@embrate", "123456"));
////            userCredentialsList.add(new UserCredentials("JISM3569@JISM", "123456"));
////            userCredentialsList.add(new UserCredentials("5321@JISM", "123456"));
////            final Boolean status = userManager.bulkUpdateUserPassword(10225, userCredentialsList);
//
//            System.out.println(siblingsAdded);
//
//            System.out.println(status);

//            int instituteId = 10225;
//            Integer academicSessionId = 194;
//            UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//            Set<String> admissionNumberSet = new HashSet<>(Arrays.asList("JISM3692"));
//            Set<String> feeStructureNameSet = new HashSet<>(Arrays.asList("Admission & Registration Fees", "ANNUAL CHAEGES", "Installments", "Testing 1"));
//            BulkStudentFeeStructureAssignmentReadablePayload bulkStudentFeeStructureAssignmentReadablePayload = new BulkStudentFeeStructureAssignmentReadablePayload();
//            bulkStudentFeeStructureAssignmentReadablePayload.setFeesStructureNameSet(feeStructureNameSet);
//            bulkStudentFeeStructureAssignmentReadablePayload.setAdmissionNumberSet(admissionNumberSet);
//            final List<String> errorDetailsList = feeConfigurationManager.readableBulkFeeStructureAssignment(
//                    instituteId, academicSessionId, userId, bulkStudentFeeStructureAssignmentReadablePayload);
//            System.out.println(errorDetailsList.size());

//            int instituteId = 10225;
//            Integer academicSessionId = 194;
//            FeesReportType feesReportType = FeesReportType.TRANSACTION_SUMMARY_DETAILED_REPORT;
//            Integer start = 1714501800;
//            Integer end = 1716489000;
//            Integer feeDueDate = -1;
//            String feeIdsStr = null;
//            String requiredStandardsCSV = null;
//            FeePaymentTransactionStatus feePaymentTransactionStatus = FeePaymentTransactionStatus.ACTIVE;
//            String requiredHeaders = "sr_no,transaction_date,transaction_added_at,transaction_mode,invoice_number,transaction_by,admission_no,name,status,father_name,class,remarks";
//            UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//            DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//            String studentStatusCSV = null;
//            String feeHeadIdsStr = null;
//            String multipleAcademicSessionIdsStr = null;
//            FeeReportDataType feeReportDataType = null;
//            String transactionModeStr = null;
//
//            final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
//                    feesReportType, start, end, feeDueDate, feeIdsStr, requiredStandardsCSV, feePaymentTransactionStatus,requiredHeaders,
//                    userId, downloadFormat, studentStatusCSV, feeHeadIdsStr, multipleAcademicSessionIdsStr, feeReportDataType, transactionModeStr);

//            HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);

//            final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
////            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
////            PdfReportGenerator pdfReportGenerator = context.getBean(PdfReportGenerator.class);
////		/2.0/attendance/reports/view-report/101/"ATTENDANCE_DETAILS_IN_A_MONTH"?academic_session_id=190&standard_id=&attendance_status=&attendance_type_id=&start=1717180200&end=1717353000&single=-1&user_id=08dc0556-b8a1-4acd-a6fa-759ca864df9e&requiredHeaders=sr_no,attendance_student_name,admission_no,attendance_student_class,attendance_student_roll_number,total_days,present_count,absent_count,leave_count,half_day_count,holiday_count,unmarked_count,present_percent
//            final ReportDetails reportDetails = attendanceReportGenerator.generateReport(101, 190
//                    , "ATTENDANCE_DETAILS_IN_A_MONTH","", "", "",
//                    1717180200, 1717353000, -1, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"), DownloadFormat.PDF, "attendance_student_name,admission_no,attendance_student_class,attendance_student_roll_number,total_days,present_count,absent_count,leave_count,half_day_count,holiday_count,unmarked_count,present_percent");
////		final DocumentOutput documentOutput = reportDetails;
//            final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//            final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);
//            /data-server/2.0/fees/reports/10225/view-report/TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL?academic_session_id=110
//            &requiredHeaders=sr_no,transaction_date,transaction_added_at,transaction_mode,invoice_number,transaction_by,admission_no,name,status,father_name,class,remarks
//            &multiple_academic_session_ids=&start=1704047400&end=1717093800&fee_ids=&required_standards=&fee_due_date=-1&student_status=
//            &user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=ACTIVE&fee_report_data_type=&transaction_mode=


//            final StudentManager studentManager = context.getBean(StudentManager.class);
//            List<Student> studentList = studentManager.getStudentByAcademicSessionAdmissionNumber(
//                    10225, 110, new HashSet<>(Arrays.asList("JISM3072")));
//            System.out.println(studentList.size());
//            for(Student student : studentList) {
//                System.out.println(student.toString());
//            }

//            final  CourseManager courseManager = context.getBean(CourseManager.class);
//            Set<UUID> courseIdSet = new HashSet<>();
//            courseIdSet.add(UUID.fromString("10f4e85d-8517-490e-88cc-0a17945c499f"));
//            courseIdSet.add(UUID.fromString("c98ac247-f49a-494b-bba9-4ba8153b82b1"));
//            List<Course> classCourseLists = courseManager.getClassCoursesByStandardId(101, UUID.fromString("4de3d93b-4390-41df-9e04-ddcdb854feb8"),
//                    1, null, null);
//             System.out.println(classCourseLists);
//            data-server/2.0/fee-payment/restrict-session-fee-payment/2c2e8bc2-d850-4677-b731-1be53edf733d?institute_id=110&academic_session_id=153
//            final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);
//
//            final boolean status = feePaymentManager
//                    .restrictSessionFeesPayment(110, 153, UUID.fromString("2c2e8bc2-d850-4677-b731-1be53edf733d"));
//
//            System.out.println(status);

//            int instituteId = 10020;
//            int srcAcademicSessionId = 145;
//            int destAcademicSessionId = 207;
//            StructureCloneType cloneType = StructureCloneType.CUSTOM;
//            Set<String> structureNames = new HashSet<>(Arrays.asList("VERY POOR"));
//            StructureCloneRequest request = new StructureCloneRequest(instituteId, srcAcademicSessionId, destAcademicSessionId, cloneType, structureNames);
//            final boolean status = feeSetupUtilityManager.cloneDiscountStructureAcrossSessions(request);


//            UUID examId = UUID.fromString("2c1eb3fa-e332-4d92-a688-8c095b621f2b");
//            int instituteId = 10225;
//            int academicSessionId = 194;
//            String courseIdsStr = "9d2d0cb4-1f2f-4622-b8fa-8c0aac78b87e";
//            final Set<UUID> courseIdSet = StringHelper.convertStringToSetUUID(courseIdsStr);
//            UUID standardId = UUID.fromString("b97c695b-57c8-4f6f-b21a-5a0f6bbcd119");
//            String sectionIdStr = "2835";
//            final Integer sectionId = getSectionId(sectionIdStr);
//            boolean addRelievedStudents = false;
//            UUID userId = UUID.fromString("9e313657-6e84-403b-b741-ab00a6b61566");
////            UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
////            final List<StudentExamMarksDetails> studentExamMarksDetails = examinationManager
////                    .getClassMarksByCourseList(instituteId, academicSessionId, examId, courseIdSet, standardId, sectionId, addRelievedStudents, userId);
//
////            final List<CourseDimensionSectionMarksStatusDetails> courseDimensionSectionMarksStatusDetailsList = examinationManager
////                    .getCourseDimensionSectionMarksStatusDetails(instituteId, academicSessionId, examId, userId);
//
//                        List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationManager.getClassMarksByCourseId(
//                                instituteId, examId, UUID.fromString(courseIdsStr), sectionId);
//            System.out.println(studentExamMarksDetailsList.size());

//            /data-server/2.0/examination/2c1eb3fa-e332-4d92-a688-8c095b621f2b/course/9d2d0cb4-1f2f-4622-b8fa-8c0aac78b87e/effective-marks?institute_id=10225&section_id=2835&add_relieved_students=False&user_id=9e313657-6e84-403b-b741-ab00a6b61566&academic_session_id=194&standard_id=b97c695b-57c8-4f6f-b21a-5a0f6bbcd119

//            127.0.0.1 - - [03/May/2024:17:11:34 +0530] "GET /data-server/2.0/examination/2c1eb3fa-e332-4d92-a688-8c095b621f2b/marks-status?institute_id=10225&academic_session_id=194&user_id=9e313657-6e84-403b-b741-ab00a6b61566 HTTP/1.1" 200 3653


//            127.0.0.1 - - [03/May/2024:14:59:19 +0530] "GET /data-server/2.0/examination/2c1eb3fa-e332-4d92-a688-8c095b621f2b/marks?institute_id=10225&course_id=9d2d0cb4-1f2f-4622-b8fa-8c0aac78b87e&section_id=2835&add_relieved_students=false&user_id=9e313657-6e84-403b-b741-ab00a6b61566&academic_session_id=194&standard_id=b97c695b-57c8-4f6f-b21a-5a0f6bbcd119 HTTP/1.1" 200 50651

//			final FeeConfigurationManager feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
//			BulkStudentFeesDeletionPayload bulkStudentFeesDeletionPayload = new BulkStudentFeesDeletionPayload();
//			bulkStudentFeesDeletionPayload.setFeesIds(new HashSet<>(Arrays.asList(UUID.fromString("352683ec-0d61-4284-9a6f-c95ebdd14778"))));
//			bulkStudentFeesDeletionPayload.setStudentIds(new HashSet<>(Arrays.asList(UUID.fromString("b5e80269-f66f-4836-abd8-d267fcf465b5"))));
//			final List<String> errorDetailsList = feeConfigurationManager.bulkFeeAssignmentDeletion(
//					10225, 194, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"),
//					bulkStudentFeesDeletionPayload);
//			BulkStudentFeeStructureAssignmentPayload bulkStudentFeeStructureAssignmentPayload =
//					new BulkStudentFeeStructureAssignmentPayload();
//			bulkStudentFeeStructureAssignmentPayload.setFeesStructureIds(new HashSet<>(Arrays.asList(UUID.fromString("0858d878-6ab6-43b7-96c7-8931e6b709ba"))));
//			bulkStudentFeeStructureAssignmentPayload.setStudentIds(new HashSet<>(Arrays.asList(UUID.fromString("b5e80269-f66f-4836-abd8-d267fcf465b5"))));
//
//			final List<String> errorDetailsList = feeConfigurationManager.bulkFeeStructureAssignment(
//					10225, 194, UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"),
//					bulkStudentFeeStructureAssignmentPayload);
//
//			System.out.println(errorDetailsList.size());
//			for(String errorDetails : errorDetailsList) {
//				System.out.println(errorDetails);

//			final UserManager userManager = context.getBean(UserManager.class);
//
//            final StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
//            boolean status = instituteManagementManager.updateStandardStaffAssignment(
//                    10225, 194, UUID.fromString("1a3b1a8d-514d-442f-a7d6-72ac91978cbc"),
//                    UUID.fromString("de0f2f1a-86f3-4a03-aa9c-b20ffa70d1f2"), 2538,
//                    UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"));

//            /data-server/2.0/institute/standard-sections?institute_id=10225&academic_session_id=194&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7
//            String sectionIds = "2541";
//            List<Integer> sectionIdInt = new ArrayList<>();
//            if(!StringUtils.isBlank(sectionIds)){
//                String[] sectionIdStr = sectionIds.split(",");
//                for(String sectionIdString: sectionIdStr){
//                    sectionIdInt.add(Integer.parseInt(sectionIdString));
//                }
//            }
//
//            final boolean status = instituteManagementManager.deleteStandardSections(10225, 194,
//                    UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"),UUID.fromString("9cb5ed05-dffd-4e06-94bc-31a9d515e126"), sectionIdInt,
//                    0);

//            List<StandardSections> standardSectionsList = new ArrayList<>();
//            standardSectionsList.add(new StandardSections(0, "E"));
//            InstituteStandardSectionsPayload instituteStandardSectionsPayload = new InstituteStandardSectionsPayload(

//            data-server/2.0/institute/standard-sections?institute_id=10225&academic_session_id=194&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&standard_id=9cb5ed05-dffd-4e06-94bc-31a9d515e126&section_ids=2541&student_assign_section_id=0

//            /data-server/2.0/institute/standard-staff-details/1a3b1a8d-514d-442f-a7d6-72ac91978cbc/b7a221f0-8072-40af-9836-dad0537b657b?institute_id=10225&academic_session_id=194&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&section_id=

//            /data-server/2.0/institute/standard-staff-details/8c9ee9ff-d4ea-41fa-8961-0ab30402bbba/de0f2f1a-86f3-4a03-aa9c-b20ffa70d1f2?institute_id=10225&academic_session_id=194&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&section_id=2538
//			List< StudentAttendancePayload > studentAttendances = new ArrayList<>();
//			studentAttendances.add(new StudentAttendancePayload(
//					67, UUID.fromString("d0736f0f-8f5b-4fa3-b775-3d423ff2c177"), AttendanceStatus.HALF_DAY, null
//			));
//			AttendanceRegister attendanceRegister = new AttendanceRegister(
//			110, 153, 1713386675,
//					null, null,  studentAttendances
//			);
//			final SaveStudentAttendanceResult saveStudentAttendanceResult = attendanceManager.saveAttendance(attendanceRegister,
//					AttendanceInputType.WEB);
//
//			System.out.println(saveStudentAttendanceResult.toString());
//			final TransportSetupUtilityManager transportSetupUtilityManager = context.getBean(TransportSetupUtilityManager.class);
//			final UserManager userManager = context.getBean(UserManager.class);
//
//			final List<UUID> createdUsers = userManager.createStudentUsers(10031, UUID.fromString("79dc0b57-**************-153a0d858021"));
//			System.out.println(createdUsers.size());

//            final InstituteManagementManager instituteManagementManager = context.getBean(InstituteManagementManager.class);

////            final StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
//            StudentLeaveDetails studentLeaveDetails = studentLeaveManagementManager.getLeaveByTransactionId(101, 190, UUID.fromString("261d2db9-cb5b-4519-a471-932a43a94fad"));
//System.out.println(studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardSection());
//StandardSections section = studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardSection();
//Integer sectionId = null;
//if (section != null) {
//    sectionId = section.getSectionId();
//}
//
//System.out.println(sectionId);
//System.out.println( studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardId());
//            final  StandardWithStaffDetails staffDetails = instituteManagementManager.getClassTeacherByStudentDetails(

//            final StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
//            StudentLeaveDetails studentLeaveDetails = studentLeaveManagementManager.getLeaveByTransactionId(101, 190, UUID.fromString("261d2db9-cb5b-4519-a471-932a43a94fad"));
//            System.out.println(studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardSection());
//            StandardSections section = studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardSection();
//            Integer sectionId = null;
//            if (section != null) {
//                sectionId = section.getSectionId();
//            }
//
//            System.out.println(sectionId);
//            System.out.println(studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardId());
//            final StandardWithStaffDetails staffDetails = instituteManagementManager.getClassTeacherByStudentDetails(

//                    101, 190, studentLeaveDetails.getStudentLite().getStudentSessionData().getStandardId(), sectionId);
//            System.out.println(staffDetails);


//			final boolean status = transportSetupUtilityManager.cloneTransportAreaVehicleAmountsAcrossSession(
//					10228, 144, 197);

//            final StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
//            StudentLeavePayload studentLeavePayload = new StudentLeavePayload(null, 190, **********, **********, UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), LeaveTransactionStatus.PENDING, UUID.fromString("e5b75d5d-7683-41e3-b952-4dd2fc48c625"), **********, "Medical leave due to illness", null);
//                    {{
//                    "transactionId": null,
//                    "academicSessionId": 190,
//                    "startDate": **********,
//                    "endDate": **********,
//                    "studentId": "e5b75d5d-7683-41e3-b952-4dd2fc48c625",
//                    "transactionStatus": "PENDING",
//                    "appliedBy": "e5b75d5d-7683-41e3-b952-4dd2fc48c625",
//                    "appliedAt": 0,
//                    "description": "Medical leave due to illness",
//                    "leaveAttachments": []
//}
//}
//            final FeeConfigurationManager feeConfigurationManager = context.getBean(FeeConfigurationManager.class);
//
//            List<FeeHeadAmount> feeHeadAmountList1 = new ArrayList<>();
//            feeHeadAmountList1.add(new FeeHeadAmount(525, 200.0, FeeEntity.STUDENT));
//
//            List<FeeHeadAmount> feeHeadAmountList2 = new ArrayList<>();
//            feeHeadAmountList2.add(new FeeHeadAmount(525, 24599.0, FeeEntity.STUDENT));
//            feeHeadAmountList2.add(new FeeHeadAmount(526, 2800.0, FeeEntity.STUDENT));
//
//            List<FeeHeadAmount> feeHeadAmountList3 = new ArrayList<>();
//            feeHeadAmountList3.add(new FeeHeadAmount(525, 2900.0, FeeEntity.STUDENT));
//            feeHeadAmountList3.add(new FeeHeadAmount(526, 3000.0, FeeEntity.STUDENT));
//            List<FeeHeadAmount> feeHeadAmountList4 = new ArrayList<>();
//            feeHeadAmountList4.add(new FeeHeadAmount(525, 60000.0, FeeEntity.STUDENT));
//            feeHeadAmountList4.add(new FeeHeadAmount(526, 1000.0, FeeEntity.STUDENT));
//
//            List<FeeHeadAmount> feeHeadAmountList5 = new ArrayList<>();
//            feeHeadAmountList5.add(new FeeHeadAmount(525, 29999.0, FeeEntity.STUDENT));
//            feeHeadAmountList5.add(new FeeHeadAmount(526, 100.0, FeeEntity.STUDENT));
//
//            // Create FeeIdFeeHead objects
//            List<FeeIdFeeHead> feeIdFeeHeadList = new ArrayList<>();
//            feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("27d3c75b-55d9-484e-9eb7-f4f8d58be25a"), feeHeadAmountList1));
//            feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("1efed782-a16b-4fb8-b4db-ef70fed8c089"), feeHeadAmountList2));
//            feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("e870781e-5a34-4759-b096-d61f6d9b2fb6"), feeHeadAmountList3));
//            feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("bb66c01d-6e30-41f1-a920-455be249ad06"), feeHeadAmountList4));
//            feeIdFeeHeadList.add(new FeeIdFeeHead(UUID.fromString("2e30f6fb-6581-451d-b77e-18ff02969190"), feeHeadAmountList5));
//
//            // Create the payload
//            FeeAssignmentAmountsPayload payload = new FeeAssignmentAmountsPayload();
//            payload.setStudentId(UUID.fromString("0c07c4af-4c9e-4494-a345-cc6f2a228c2b"));
//            payload.setFeeIdFeeHeadList(feeIdFeeHeadList);
//            final boolean updatedAssignedFees = feeConfigurationManager.updateAssignedFeeAmounts(101, 190, payload, UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"));
//            System.out.println(updatedAssignedFees);
//            Set<UUID> transactionIdSet = studentLeaveManagementManager.addStudentLeaves(101, studentLeavePayload,null);
////
//            System.out.println(transactionIdSet);
//			System.out.println("status : " + status);
//			int instituteId = 10215;
//			final Organisation organisation = instituteManager.getOrganization(instituteId);
//			List<User> internalUsers = new ArrayList<>();
//			if(organisation == null || organisation.getOrganisationId() == null || org.apache.commons.collections.CollectionUtils.isEmpty(organisation.getInstitutes())) {
//				internalUsers = userManager.getAllInternalUser(instituteId);
//			} else {
//				internalUsers = userManager.getAllInternalUserOfOrganisation(instituteId);
//			}
//			if (CollectionUtils.isEmpty(internalUsers)) {
//				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
//						"No system user configured for institute"));
//			}
//			final User user = internalUsers.get(0);
//			final UUID systemUserId = user.getUuid();
//			System.out.println(user.getUserName());
//
//            final StudentManager studentManager = context.getBean(StudentManager.class);
//            Set<UUID> standard = new HashSet<>();
//            standard.add(UUID.fromString("7b924ff4-dd2a-43d0-8ec7-3b8caadb1780"));
//            standard.add(UUID.fromString("317088ce-7ce3-4787-b0a8-fe9d4bfc3fb1"));
//            standard.add(UUID.fromString("dc96d032-36fc-4352-8e43-505ec08bb539"));
           /*  List<StudentStatus> studentStatusSet = new ArrayList<>();
            studentStatusSet.add(StudentStatus.ENROLLED); */
//            Set<TaggedActions> taggedActionsSet = new HashSet<>();
//            taggedActionsSet.add(TaggedActions.FEE_PAYMENT_RESTRICTED);
//            taggedActionsSet.add(TaggedActions.INVENTORY_SALE_RESTRICTED);
////            studentStatusSet.add(StudentStatus.ENROLMENT_PENDING);S
//            String str = "97b19ba1-a0ef-41da-a345-c2eaf62985b3,4dbc60bd-b6ea-44a1-8a4f-3505f0390d65:2393";
////           List<Student> classStudentsInAcademicSession = studentManager.getStudentsByStandardIds(101, 1, standard, studentStatusSet);
//            Set<UUID> hostelId=  new HashSet<>();
//            hostelId.add(UUID.fromString("95c3c777-c642-4bf7-834d-81b75e333fe3"));
//            SearchResultWithPagination<Student> studentsInAcademicSession = studentManager.searchStudentsInAcademicSesison(110, "",
//                    255, studentStatusSet, null, null, null, null, false,  null, hostelId);
//
////           System.out.println("list of student is");
//           System.out.println(studentsInAcademicSession);
//            System.out.println(studentsInAcademicSession.getResult().size());

//			final OrganisationReportGenerator organisationReportGenerator = context.getBean(OrganisationReportGenerator.class);
//			int instituteId = 10225;
//			Integer academicSessionId  = 144;
//			UUID organisationId = UUID.fromString("538eab7b-eb05-4b17-b4b5-6f297999035c");
//			OrganisationReportType organisationReportType = OrganisationReportType.DISCOUNT_SUMMARY_REPORT;
//			Integer start = null;
//			Integer end = null;
//			Integer feeDueDate = -1;
//			String feeIdsStr = null;
//			String requiredStandardsCSV = null;
//			FeePaymentTransactionStatus feePaymentTransactionStatus = null;
////			String requiredHeaders = "sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount";
//			String requiredHeaders = "";
//			UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String studentStatusCSV = "ENROLLED";
//			String feeHeadIdsStr = "";
//			String multipleAcademicSessionIdsStr = "";
//			FeeReportDataType feeReportDataType = null;

//			final ReportDetails reportDetails = organisationReportGenerator.generateReport(organisationId, organisationReportType,
//					academicSessionId, studentStatusCSV, userId, downloadFormat);
//
////			final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
////					feesReportType, start, end, feeDueDate, feeIdsStr, requiredStandardsCSV, feePaymentTransactionStatus,requiredHeaders,
////					userId, downloadFormat,studentStatusCSV,feeHeadIdsStr, multipleAcademicSessionIdsStr, feeReportDataType);
//>>>>>>> 17e113c2a8c1c7c8d3a44ced867f8b1df55a64fc
//
//            System.out.println("hello");
//            final InstituteManager instituteManager = context.getBean(InstituteManager.class);
////           System.out.println("in");
//            Map<InstituteMetadataVariables, String> instituteMetadataVariablesMap = new HashMap<>();
//            instituteMetadataVariablesMap.put(AFFILIATION_NUMBER, "16");
//          instituteMetadataVariablesMap.put(InstituteMetadataVariables.DISE_CODE, "18");
////          System.out.println(instituteMetadataVariablesMap);
//			InstitutePayload institutePayload = new InstitutePayload(101, "embrate", "", "Gaushala Road",
//                    "Jhunjhunu (Rajasthan)", "" , "head 2", "New DELHI", "DELHI", "INDIA", "110077",
//                    "LANDMARK", "", "", "", "", instituteMetadataVariablesMap);
////
//            boolean updateInstituteDetail = instituteManager.updateInstituteDetails(101,  UUID.fromString("08dc0556-b8a1-4acd-a6fa-759ca864df9e"),
//                    institutePayload);

////
////
//            System.out.println(updateInstituteDetail);
//            Institute institute = instituteManager.getInstitute(101, true);
//                    System.out.println(institute);
//            ObjectMapper mapper = SharedConstants.OBJECT_MAPPER;
//            SimpleModule module = new SimpleModule();
//            module.addSerializer(InstituteMetadataVariables.class, new InstituteMetadataVariablesSerializer());
//            module.addDeserializer(InstituteMetadataVariables.class, new InstituteMetadataVariablesDeserializer());
//
//            mapper.registerModule(module);
//
//
//// Example usage
//            Map<InstituteMetadataVariables, String> metadata = new HashMap<>();
//            metadata.put(InstituteMetadataVariables.AFFILIATION_NUMBER, "123456");
//            metadata.put(InstituteMetadataVariables.DISE_CODE, "654321");
//
//// Serialize
//            String json = mapper.writeValueAsString(metadata);
//            System.out.println("Serialized: " + json);
//
//// Deserialize
//            TypeReference<HashMap<InstituteMetadataVariables, String>> typeRef = new TypeReference<HashMap<InstituteMetadataVariables, String>>() {};
//            Map<InstituteMetadataVariables, String> deserializedMap = mapper.readValue(json, typeRef);
//

//			Set<Integer> sectionId =new HashSet<>();
//			sectionId.add(9);

//			faculty.add(UUID.fromString("5980966d-cda9-42bb-823f-f3c9eab13399"));
//			System.out.println(courseId);
//			final List<FileData> files = RestFormDataHandler.getFileDataFromFormDataBodyPart(bodyParts);

//			final Organisation organisationView = instituteManager.getOrganization(101, true);
//			System.out.println("reached end");
//			 System.out.println(organisationView);

//                  System.out.println(standardLectureDetails);
//				  System.out.println((standardLectureDetails.size()));
//			List<ClassCourses> classCourseLists = courseManager.getStandardCourses(101, 1);
//			Course Course=new Course(UUID.randomUUID().toString(), null, CourseType.SCHOLASTIC, "chemistry",true, 349);
//		List<Course>  courses=new ArrayList<>();
//		courses.add(Course);
//		ClassCoursesPayload  classCoursesPayload= new ClassCoursesPayload(1, UUID.fromString("317088ce-7ce3-4787-b0a8-fe9d4bfc3fb1"),courses);
//					Boolean success = courseManager.addStandardCourse(classCoursesPayload,101, UUID.fromString("85c34583-3545-4c8b-b3e0-ab041eac3559"), false);
//			/data-server/2.0/fees/reports/10205/view-report/STUDENT_AGGREGATED_PAYMENT?academic_session_id=131&requiredHeaders=sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount&multiple_academic_session_ids=&start=-1&end=-1&fee_ids=&required_standards=&fee_due_date=-1&student_status=&user_id=e1982041-d931-4663-9f9e-e07fb6f92d55&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type=ALL
//if(!CollectionUtils.isEmpty(classCourseLists)){
//	System.out.println("list is not empty ");
//	for(ClassCourses Class: classCourseLists){
//		System.out.println(Class.toString());
//	}
//}
//else {
//	System.out.println("list is empty");
//}
//			System.out.print(success);


//=======
//>>>>>>> ce9ae388ac054706d9487b1109d685f5f96f36f7
//
//			List<ClassCourse> classCourseLists = courseManager.getClassCoursesByInstitute1(101, 1);
////			/data-server/2.0/fees/reports/10205/view-report/STUDENT_AGGREGATED_PAYMENT?academic_session_id=131&requiredHeaders=sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount&multiple_academic_session_ids=&start=-1&end=-1&fee_ids=&required_standards=&fee_due_date=-1&student_status=&user_id=e1982041-d931-4663-9f9e-e07fb6f92d55&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type=ALL
//<<<<<<< HEAD
//			/data-server/2.0/fees/reports/10225/view-report/FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT?academic_session_id=110&requiredHeaders=&multiple_academic_session_ids=&start=1704047400&end=1711650600&fee_ids=&required_standards=&fee_due_date=-1&student_status=ENROLLED&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type= HTTP/1.1" 400 84
//			127.0.0.1 - - [29/Mar/2024:10:59:34 +0530] "GET /data-server/2.0/fees/reports/10225/view-report/FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT?academic_session_id=110&requiredHeaders=&multiple_academic_session_ids=&start=1704047400&end=1711650600&fee_ids=&required_standards=&fee_due_date=-1&student_status=&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type= HTTP/1.1" 200 39591
//			127.0.0.1 - - [29/Mar/2024:10:59:42 +0530] "GET /data-server/2.0/fees/reports/10225/view-report/FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT?academic_session_id=110&requiredHeaders=&multiple_academic_session_ids=&start=1704047400&end=1711650600&fee_ids=&required_standards=&fee_due_date=-1&student_status=NSO&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type= HTTP/1.1" 400 84

//if(!CollectionUtils.isEmpty(classCourseLists)){
//	System.out.println("list is not empty ");
//	for(ClassCourse Class: classCourseLists){
//		System.out.println(Class.toString());
//	}
//}
//else {
//	System.out.println("list is empty");
//}


//			final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//			int instituteId = 10225;
//			Integer academicSessionId  = 110;
//			FeesReportType feesReportType = FeesReportType.FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT;
//			Integer start = 1680287400;
//			Integer end = 1711650600;
//			Integer feeDueDate = -1;
//			String feeIdsStr = null;
//			String requiredStandardsCSV = null;
//			FeePaymentTransactionStatus feePaymentTransactionStatus = null;
////			String requiredHeaders = "sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount";
//			String requiredHeaders = "";
//			UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String studentStatusCSV = "ENROLLED";
//			String feeHeadIdsStr = "";
//			String multipleAcademicSessionIdsStr = "";
//			FeeReportDataType feeReportDataType = null;
//			final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
//					feesReportType, start, end, feeDueDate, feeIdsStr, requiredStandardsCSV, feePaymentTransactionStatus,requiredHeaders,
//					userId, downloadFormat,studentStatusCSV,feeHeadIdsStr, multipleAcademicSessionIdsStr, feeReportDataType);
//=======
//			final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//			int instituteId = 10225;
//			Integer academicSessionId  = 110;
//			FeesReportType feesReportType = FeesReportType.DISCOUNT_SUMMARY_REPORT;
//			Integer start = null;
//			Integer end = null;
//			Integer feeDueDate = -1;
//			String feeIdsStr = null;
//			String requiredStandardsCSV = null;
//			FeePaymentTransactionStatus feePaymentTransactionStatus = null;
////			String requiredHeaders = "sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount";
//			String requiredHeaders = "";
//			UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String studentStatusCSV = "ENROLLED";
//			String feeHeadIdsStr = "";
//			String multipleAcademicSessionIdsStr = "";
//			FeeReportDataType feeReportDataType = null;
//			final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
//					feesReportType, start, end, feeDueDate, feeIdsStr, requiredStandardsCSV, feePaymentTransactionStatus,requiredHeaders,
//					userId, downloadFormat,studentStatusCSV,feeHeadIdsStr, multipleAcademicSessionIdsStr, feeReportDataType);
//>>>>>>> f28ea21a58b8b86435d8c449a3748a1fe552009c

//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//			final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//			final StudentTransferCertificateDetails studentTransferCertificateDetails =
//					studentAdmissionManager.getStudentTransferCertificateDetails(10295, UUID.fromString("bfb9a1c2-e3d8-420a-8963-a2283eaef384"));

//			{
//				"instituteId": 110,
//					"srcAcademicSessionId": 70,
//					"destAcademicSessionId": 153,
//					"cloneType": "ALL_STRUCTURES",
//					"structureNames": null
//			}

//			final FeeSetupUtilityManager feeSetupUtilityManager = context.getBean(FeeSetupUtilityManager.class);
//			final boolean status = feeSetupUtilityManager.cloneDiscountStructureAcrossSessions(new StructureCloneRequest(
//					110, 70, 153, StructureCloneType.ALL_STRUCTURES, null
//			));
//			System.out.println(status);

//			/data-server/2.0/examination-reports/10225/view-report/TEACHER_WISE_DIVISION_RESULT_ANALYSIS_REPORT?academic_session_id=110&standard_id=
//			&section_id=&exam_id=&course_id=&course_type=&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&staff_id=6486597a-7d2a-4814-aa29-1e415732fc7f
//			&report_card_type=ANNUAL&section_ids_str=&exam_ids_str=&course_ids_str=&compare_cumulative_exam_ids_str=&rank_till=&exclude_coscholastic_subjects=false
//			&show_coscholastic_grade=false&show_scholastic_grade=false&sort_student_on_rank=false&requiredHeaders=&additional_courses_str=
//			int instituteId = 10225;
//			int academicSessionId = 110;
//			ExamReportType examReportType = ExamReportType.TEACHER_WISE_GRADE_RESULT_ANALYSIS_REPORT;
//			UUID standardId = null;
//			Integer sectionId = null;
//			UUID examId = null;
//			UUID courseId = null;
//			CourseType courseType = null;
//			UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//			UUID staffId = UUID.fromString("6486597a-7d2a-4814-aa29-1e415732fc7f");
//			String reportCardType = "ANNUAL";
//			String sectionIdSetStr = null;
//			String examIdSetStr = null;
//			String courseIdSetStr = null;
//			String compareCumulativeWithExamIdSetStr = null;
//			Integer rankTill = null;
//			boolean excludeCoScholasticSubjects = false;
//			boolean showCoScholasticGrade = false;
//			boolean showScholasticGrade = false;
//			boolean isSortStudentOnRank = false;
//			String requiredHeaders = null;
//			String additionalCoursesStr = null;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//			final ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId,
//					examReportType, standardId, sectionId, examId, courseId, courseType, userId, downloadFormat,
//					reportCardType, staffId, sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr,
//					rankTill, excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders,
//					isSortStudentOnRank, additionalCoursesStr);
//
//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//			/data-server/2.0/fees/reports/10205/view-report/STUDENT_AGGREGATED_PAYMENT?academic_session_id=131&requiredHeaders=sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount&multiple_academic_session_ids=&start=-1&end=-1&fee_ids=&required_standards=&fee_due_date=-1&student_status=&user_id=e1982041-d931-4663-9f9e-e07fb6f92d55&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type=ALL
//			/data-server/2.0/fees/reports/10225/view-report/FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT?academic_session_id=110&requiredHeaders=&multiple_academic_session_ids=&start=1704047400&end=1711650600&fee_ids=&required_standards=&fee_due_date=-1&student_status=ENROLLED&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type= HTTP/1.1" 400 84
//			127.0.0.1 - - [29/Mar/2024:10:59:34 +0530] "GET /data-server/2.0/fees/reports/10225/view-report/FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT?academic_session_id=110&requiredHeaders=&multiple_academic_session_ids=&start=1704047400&end=1711650600&fee_ids=&required_standards=&fee_due_date=-1&student_status=&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type= HTTP/1.1" 200 39591
//			127.0.0.1 - - [29/Mar/2024:10:59:42 +0530] "GET /data-server/2.0/fees/reports/10225/view-report/FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT?academic_session_id=110&requiredHeaders=&multiple_academic_session_ids=&start=1704047400&end=1711650600&fee_ids=&required_standards=&fee_due_date=-1&student_status=NSO&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7&fee_head_ids=&fee_payment_transaction_status=&fee_report_data_type= HTTP/1.1" 400 84

//			final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//			int instituteId = 10225;
//			Integer academicSessionId  = 110;
//			FeesReportType feesReportType = FeesReportType.FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT;
//			Integer start = 1680287400;
//			Integer end = 1711650600;
//			Integer feeDueDate = -1;
//			String feeIdsStr = null;
//			String requiredStandardsCSV = null;
//			FeePaymentTransactionStatus feePaymentTransactionStatus = null;
////			String requiredHeaders = "sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount,due_fine_amount";
//			String requiredHeaders = "";
//			UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String studentStatusCSV = "ENROLLED";
//			String feeHeadIdsStr = "";
//			String multipleAcademicSessionIdsStr = "";
//			FeeReportDataType feeReportDataType = null;
//			final ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
//					feesReportType, start, end, feeDueDate, feeIdsStr, requiredStandardsCSV, feePaymentTransactionStatus,requiredHeaders,
//					userId, downloadFormat,studentStatusCSV,feeHeadIdsStr, multipleAcademicSessionIdsStr, feeReportDataType);

//			/2.0/student/session/131/10205?search_text=&status=ENROLLED&limit=10&offset=0
//			&requiredStandards=7c693e4f-ec9e-41b8-b974-4c885f141836:1497
//			final StudentManager studentManager = context.getBean(StudentManager.class);
//			SearchResultWithPagination<Student> studentsInAcademicSession = studentManager.searchStudentsInAcademicSesison(
//					10205, "", 131,
//					Arrays.asList(StudentStatus.ENROLLED), 0, 10, null, "7c693e4f-ec9e-41b8-b974-4c885f141836:1497");


//			final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);

//
//<<<<<<< HEAD
//			final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//=======
////			final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//>>>>>>> ce9ae388a (verify changes)


//			int instituteId = 10030;
//			int academicSessionId = 113;
//			UUID standardId = UUID.fromString("14685dbe-4ee3-4c5b-9ee2-34becf24130f");
//			Integer sectionId = null;
//			Set<UUID> courseIdSet = null;
//			UUID courseId = UUID.fromString("be4248f3-09a3-40b9-b03f-8310b20eea04");
//			boolean excludeCoScholasticSubjects = false;
//			boolean showDimensions = false;
//			boolean showTotalColumnDimension = false;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			UUID userId = UUID.fromString("94d8a997-4389-4b08-aefe-9567822a879b");
//			UUID examId = UUID.fromString("959f485b-781a-4b05-8421-693a9c787b4b");
//
//			final ReportDetails reportDetails = examReportGenerator.getClassCourseWiseExamReport(instituteId, academicSessionId, standardId, sectionId,
//					courseIdSet, excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, downloadFormat, userId, examId);

//			/data-server/2.0/examination-reports/10030/view-report/MULTIPLE_EXAM_MARKS_REPORT?academic_session_id=113&standard_id=14685dbe-4ee3-4c5b-9ee2-34becf24130f&section_id=&exam_id=0f3a104c-17b7-4753-8a50-acce1fd6808c&course_id=&course_type=&user_id=94d8a997-4389-4b08-aefe-9567822a879b&staff_id=&report_card_type=&section_ids_str=&exam_ids_str=0f3a104c-17b7-4753-8a50-acce1fd6808c&course_ids_str=&compare_cumulative_exam_ids_str=&rank_till=&exclude_coscholastic_subjects=false&show_coscholastic_grade=false&show_scholastic_grade=false&sort_student_on_rank=false&requiredHeaders=sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks&additional_courses_str=
//
//			final ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId,
//					ExamReportType.MULTIPLE_EXAM_MARKS_REPORT, standardId, sectionId, UUID.fromString("0f3a104c-17b7-4753-8a50-acce1fd6808c"),
//					null, null, userId, downloadFormat,
//					null, null, null, "0f3a104c-17b7-4753-8a50-acce1fd6808c", null,
//					null, null, excludeCoScholasticSubjects, true,  false,
//					"sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks",
//					false, null);


//			/data-server/2.0/examination-reports/879dbc76-549f-4264-ab81-47c286cddeb9/view-report/course/03046e0b-3bc3-46ea-9e53-5a85d3eee689
//			?section_id=2132&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&institute_id=10295
//			&academic_session_id=177&user_id=9e19e819-ba37-4e12-b2e3-ff1e05269b2d
//			int instituteId = 10295;
//			int academicSessionId = 177;
//			UUID standardId = UUID.fromString("879dbc76-549f-4264-ab81-47c286cddeb9");
//			Integer sectionId = 2132;
//			boolean excludeCoScholasticSubjects = true;
//			boolean showDimensions = false;
//			boolean showTotalColumnDimension = false;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			UUID userId = UUID.fromString("9e19e819-ba37-4e12-b2e3-ff1e05269b2d");
////			UUID examId = UUID.fromString("68c579c1-1f25-4b73-8289-66f561a6578c");
//			UUID examId = UUID.fromString("c3c82387-7868-46d7-b403-20fae0de152e");
//			final ReportDetails reportDetails = examReportGenerator.generateExamConsolidatedReport(instituteId, academicSessionId, standardId, sectionId,

//
////			/data-server/2.0/examination-reports/879dbc76-549f-4264-ab81-47c286cddeb9/view-report/course/03046e0b-3bc3-46ea-9e53-5a85d3eee689
////			?section_id=2132&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&institute_id=10295
////			&academic_session_id=177&user_id=9e19e819-ba37-4e12-b2e3-ff1e05269b2d
//			int instituteId = 10295;
//			int academicSessionId = 177;
//			UUID standardId = UUID.fromString("879dbc76-549f-4264-ab81-47c286cddeb9");
//			Integer sectionId = 2132;
//			boolean excludeCoScholasticSubjects = false;
//			boolean showDimensions = false;
//			boolean showTotalColumnDimension = false;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			UUID userId = UUID.fromString("9e19e819-ba37-4e12-b2e3-ff1e05269b2d");
////			UUID examId = UUID.fromString("68c579c1-1f25-4b73-8289-66f561a6578c");
//			UUID examId = UUID.fromString("c3c82387-7868-46d7-b403-20fae0de152e");
//			final ReportDetails reportDetails = examReportGenerator.generateExamConsolidatedReport(instituteId, academicSessionId, standardId, sectionId,
//					examId, excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, downloadFormat, userId);
//=======
//////
//>>>>>>> ce9ae388a (verify changes)

//

//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//			/data-server/2.0/dashboards/organization/538eab7b-eb05-4b17-b4b5-6f297999035c/66dc0bf1-e852-42bf-8f92-d6d18c68e0c7?date=1708626600


// <<<<<<< sum_of_best_n

// 			examinationManager.getSingleStudentExamMarksDetails(10295, 177,
// 					UUID.fromString("16875b88-a96a-401c-a695-219986e0eab3"),
// 					UUID.fromString("91ee2d89-89b3-43e6-a474-2620135ecdc7"));
// =======
// 			final DashboardManager dashboardManager = context.getBean(DashboardManager.class);
//
// 			final List<InstituteStats> instituteStatsList = dashboardManager.getOrganizationStats(
// 					UUID.fromString("538eab7b-eb05-4b17-b4b5-6f297999035c"),
// 					UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7"), 1708626600);
//
//			 for(InstituteStats instituteStats : instituteStatsList) {
//				 System.out.println(instituteStats.getDateWiseFeesStats().getCollectedAmount());
//				 System.out.println(instituteStats.getDateWiseFeesStats().getTodayCollectedAmount());
//			 }
// >>>>>>> release

// //			final List<StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailsList = examReportCardManager
// //					.getStudentPersonalityTraits(10295, UUID.fromString("879dbc76-549f-4264-ab81-47c286cddeb9"),
// //							null, 177, "ANNUAL", null);
// <<<<<<< sum_of_best_n

//			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10295, 177,
//					UUID.fromString("356013c0-4506-4866-a8d9-d42b440063ae"), "ANNUAL", true,
// //					UUID.fromString("9e19e819-ba37-4e12-b2e3-ff1e05269b2d"));
// =======
// //
// ////			final DocumentOutput documentOutput = examReportSerivceProvider.getExamReport(10295, 177,
// ////					UUID.fromString("356013c0-4506-4866-a8d9-d42b440063ae"), "ANNUAL", true,
// ////					UUID.fromString("9e19e819-ba37-4e12-b2e3-ff1e05269b2d"));
// >>>>>>> release
//			System.out.println(studentPersonalityTraitsDetailsList.size());
//			for(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails : studentPersonalityTraitsDetailsList ) {
//				String name = studentPersonalityTraitsDetails.getStudent().getStudentBasicInfo().getName();
//				System.out.print(name + " : ");
//				for(StudentPersonalityTraitsResponse response : studentPersonalityTraitsDetails.getStudentPersonalityTraitsResponseList()) {
//					System.out.print(response.getPersonalityTraitsDetails().getPersonalityTraitName() + " ---  " + response.getResponse() + ", ");
//				}
//				System.out.println();
//			}

//			/2.0/examination/standard/de0f2f1a-86f3-4a03-aa9c-b20ffa70d1f2/personality-traits?institute_id=10225&section_id=1055&academic_session_id=110&report_type=HALF_YEARLY_EXAM
//			final InstituteManager instituteManager = context.getBean(InstituteManager.class);
////			List<Standard> standardList = instituteManager.getAllSessionStandardDetails(Arrays.asList(10227));
////			for(Standard standard : standardList) {
////				System.out.print(standard.getAcademicSessionId() + " : " + standard.getStandardName() + " : " );
////				if(CollectionUtils.isEmpty(standard.getStandardSectionList())) {
////					System.out.print("No sections");
////					System.out.println();
////					continue;
////				}
////				for(StandardSections standardSections : standard.getStandardSectionList()) {
////					System.out.print(standardSections.getSectionName() + ", ");
////				}
////				System.out.println();
////			}
//			Map<Integer, Map<Integer, List<Standard>>> instituteSessionStandardMap = instituteManager.getAllSessionStandardDetailsWithoutStudentCount(Arrays.asList(10227));
//			for(Map.Entry<Integer, Map<Integer, List<Standard>>> instituteSessionStandardEntry : instituteSessionStandardMap.entrySet()) {
//				System.out.println("instituteId - " + instituteSessionStandardEntry.getKey());
//				for(Map.Entry<Integer, List<Standard>> sessionStandardEntry : instituteSessionStandardEntry.getValue().entrySet()) {
//					System.out.println("sessionId - " + sessionStandardEntry.getKey());
//					for(Standard standard : sessionStandardEntry.getValue()) {
//						System.out.print("standard(sessionId) - " + standard.getDisplayName() + "(" + standard.getAcademicSessionId() + ")" + "  === ");
//						List<StandardSections> standardSectionsList = standard.getStandardSectionList();
//						if(CollectionUtils.isEmpty(standardSectionsList)) {
//							System.out.println("no section found");
//							continue;
//						}
//						for(StandardSections standardSections : standard.getStandardSectionList()) {
//							System.out.print(standardSections.getSectionName() + ", ");
//						}
//					}
//					System.out.println();
//				}
//			}

//			final AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
//			final ExaminationManager examinationManager = context.getBean(ExaminationManager.class);
//
//			final List<CourseMarksFeedExamWithDimensions> courseMarksFeedExamList = examinationManager
//					.getClassMarksFeedStructureWithDimensions(UUID.fromString("df9a4db4-e554-43d5-9354-11e181b8521b"),
//							10230, 136);

//			case STUDENT_ENROLLMENT_BY_AGE:
//				return generateStudentEnrollmentByAgeReport(instituteId, academicSessionId);
//			case CAST_REPORT:
//				return generateCastWiseStudentReport(instituteId, academicSessionId);
//			case RELIGION_REPORT:
//				return generateReligionWiseStudentReport(instituteId, academicSessionId);
//			case MOBILE_APP_TRACK:
//				return generateStudentMobileAppTrackReport(studentReportType, instituteId, academicSessionId,
//						requiredHeaderAttributes, requiredStandards, filterationCriteria);
//			case GENDER_REPORT:
//				return generateGenderWiseStudentReport(instituteId, academicSessionId);
//			case HOUSE_SUMMARY_REPORT:
//				return generateHouseSummaryReport(instituteId,academicSessionId);
//			case STUDENT_DOCUMENT_REPORT:
//				return getStudentDocumentReport(instituteId, academicSessionId, requiredStandards, studentStatusList, documentTypes, documentStatus);
//			case SIBLING_DETAILS_REPORT:
//				return generateStudentsSiblingsDetailsReport(instituteId,academicSessionId,requiredStandards,studentStatusList);
//			int instituteId = 10030;
//			int academicSessionId = 32;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			StudentReportType studentReportType = StudentReportType.STUDENT_ENROLLMENT_BY_AGE;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String requiredStandards = null;
//			String studentStatus = null;
//			String documentTypes = null;
//			StudentDocumentStatus documentStatus = null;
//			String requiredHeaders = "serial_number,student_admission_number,student_session_status,student_name,student_father_name,student_mother_name,student_class_name,student_house_name,student_admission_date,student_dob,student_gender,student_primary_contact_number,student_category,student_religion,student_caste,student_rte,student_specially_abled,student_specially_abled_type,student_bpl,student_registration_number,student_relieve_date,student_birth_place,student_aadhar_number,student_nationality,student_mother_tongue,student_area_type,student_permanent_address,student_present_address,student_city,student_state,student_zipcode,student_present_city,student_present_state,student_present_zipcode,student_primary_email,student_sponsored,student_whatsapp_number,student_admission_in_class,student_present_post_office,student_permanent_post_office,student_present_police_station,student_permanent_police_station,student_academic_session";
//			FilterationCriteria filterationCriteria = new FilterationCriteria();
//			boolean newAdmission = false;
//			final ReportDetails reportDetails = studentReportsGenerator.generateReport(instituteId,
//					academicSessionId, userId, studentReportType, downloadFormat, requiredStandards,
//					studentStatus,documentTypes,documentStatus, requiredHeaders, filterationCriteria, newAdmission);

//			/data-server/2.0/attendance/reports/view-report/10225/ATTENDANCE_DETAILS_IN_A_MONTH?academic_session_id=110&standard_id=&attendance_status=&attendance_type_id=&start=1704047400&end=1706639400&single=-1&user_id=66dc0bf1-e852-42bf-8f92-d6d18c68e0c7
//			int instituteId = 10225;
//			int academicSessionId = 110;
//			UUID userId = UUID.fromString("66dc0bf1-e852-42bf-8f92-d6d18c68e0c7");
//			String attendanceReportTypeStr = "ATTENDANCE_DETAILS_IN_A_MONTH";
//			String standardIdStr = "";
//			String attendanceStatusStr = "";
//			String attendanceTypeIdStr = "";
//			int start = 1704047400;
//			int end = 1706639400;
//			int singleDate = -1;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId,
//					academicSessionId, attendanceReportTypeStr, standardIdStr, attendanceStatusStr, attendanceTypeIdStr,
//					start, end, singleDate, userId, downloadFormat);
//
//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);
//			int instituteId = 101;
//			int academicSessionId = 33;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			String attendanceReportTypeStr = "ATTENDANCE_DETAILS_IN_A_MONTH";
//			String standardIdStr = "";
//			String attendanceStatusStr = "";
//			String attendanceTypeIdStr = "";
//			int start = 1706725800;
//			int end = 1708885800;
//			int singleDate = -1;
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId,
//					academicSessionId, attendanceReportTypeStr, standardIdStr, attendanceStatusStr, attendanceTypeIdStr,
//					start, end, singleDate, userId, downloadFormat);
//
//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);


//			int instituteId = 10030;
//			Integer start = 1643653800;
//			Integer end = 1708108200;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final ReportDetails reportDetails = inventoryReportGenerationManager.generateReport(instituteId,
//					ReportType.valueOf("SALES_DETAILS_REPORT"), userId, start, end, downloadFormat);

//			if(reportDetails == null){
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
//			}

//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);

//			System.out.println(htmlReportTable);

//			final FeePaymentManager feePaymentManager = context.getBean(FeePaymentManager.class);

//			List<FeePaymentPayloadLite> feePaymentPayloadLiteList = new ArrayList<>();

//			int academicSessionId = 32;
//			double fineCollectionAmount = 10000;
//			double instantDiscountAmount = 40000;
//			double paidAmount = 20000;
//			UUID studentId = UUID.fromString("180af9a4-32f8-4da2-b52f-0a2fba5fc6cb");
//			int transactionDate  = 1707157800;
//			TransactionMode transactionMode = TransactionMode.QR_PAYMENT;
//			String transactionReference  = "Referencnek";
//			String transactionRemarks = "Remajrsklsks";
//			double walletAmount = 0;
////			double fineCollectionAmount = 200;
////			double instantDiscountAmount = 300;
////			double walletAmount = 0;
////			double paidAmount = 33000;
//			feePaymentPayloadLiteList.add(new FeePaymentPayloadLite(
//					studentId, academicSessionId, transactionMode, transactionDate,  transactionReference,
//					fineCollectionAmount, instantDiscountAmount, walletAmount, paidAmount, transactionRemarks, null
//			));
//			fineCollectionAmount = 10;
//			instantDiscountAmount = 0;
//			paidAmount = 100;
//			feePaymentPayloadLiteList.add(new FeePaymentPayloadLite(
//					UUID.fromString("dc0f1339-7d86-4e33-b38e-924ac5e6d468"), 32, TransactionMode.CASH, DateUtils.now(),  "This is transaction Reference 1",
//					fineCollectionAmount, instantDiscountAmount, walletAmount, paidAmount, "This is transaction remaks 1", null
//			));

//			final List<FeePaymentResponse> feePaymentResponseList = feePaymentManager.collectBulkFeePaymentLite(
//					10030, 32,
//					feePaymentPayloadLiteList, UUID.fromString("140a207b-f1ed-4e7f-9f61-e61757cef6aa"), false);
//			if (CollectionUtils.isEmpty(feePaymentResponseList)) {
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_FEE_TRANSACTION, "Unable to add fee transaction"));
//			}

//			for(FeePaymentResponse feePaymentResponse : feePaymentResponseList) {
//
//				if(feePaymentResponse == null || feePaymentResponse.getTransactionId() == null) {
//					continue;
//				}
//
//				System.out.println("feePaymentResponse : " + feePaymentResponse);
//			}


//			final StudentManager studentManager = context.getBean(StudentManager.class);
/////2.0/student/13c72b43-abff-4c76-91d3-b2001ed94b01/academic-session/33?institute_id=101&includeWalletAmount=True&add_thumbnail=True
//
//			final Student student = studentManager.getStudentByAcademicSessionStudentId(101, 33,
//					UUID.fromString("13c72b43-abff-4c76-91d3-b2001ed94b01"));
//			boolean includeWalletAmount = true;
//			boolean addThumbnail = true;
//			if (includeWalletAmount) {
//				final Double walletAmount = studentManager.getWalletAmount(student.getStudentId(), DBLockMode.NONE);
//				student.setWalletAmount(walletAmount);
//			}
//			if (addThumbnail) {
//				student.setThumbnail(getThumbnail(student, context));
//			}
//
//			System.out.println(student.getThumbnail());


//			/97b8ac6d-1645-49f6-b9fa-389c3401a952/fees/generate-report/STUDENT_AGGREGATED_PAYMENT?academic_session_id=32&requiredHeaders=sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount&requiredMultiSession=&reportStartDate=-1&reportEndDate=-1&fee_ids=&requiredStandards=00a137b8-68e3-4004-81f1-8abf39354860:75&feeDueDate=-1&studentStatus=&fee_head_ids=&transactionType=&includeDueStudentOnly=fals

//			final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//
////			/97b8ac6d-1645-49f6-b9fa-389c3401a952/fees/generate-report/STUDENT_WALLET_TRANSACTIONS?academic_session_id=0&requiredHeaders=&requiredMultiSession=&reportStartDate=1643653800&reportEndDate=1708108200&fee_ids=&requiredStandards=&feeDueDate=-1&studentStatus=&fee_head_ids=&transactionType=&feeReportDataType=
//			int instituteId = 10030;
//			Integer academicSessionId = 32;
////			FeesReportType feesReportType = FeesReportType.STUDENT_FEES_PAYMENT;
//			FeesReportType feesReportType = FeesReportType.STUDENT_WALLET_TRANSACTIONS;
//			Integer start = 1643653800;
//			Integer end = 1708108200;
//			Integer feeDueDate = -1;
//			String feeIdsStr = "2807e376-2b0f-4d13-8998-bcd14b9f886a";
////			String requiredStandardsCSV = "00a137b8-68e3-4004-81f1-8abf39354860:75";
//			String requiredStandardsCSV = "";
//			FeePaymentTransactionStatus feePaymentTransactionStatus = null;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String studentStatusCSV = null;
//			String feeHeadIdsStr = "23,22";
////			String feeHeadIdsStr = "23";
//			String multipleAcademicSessionIdsStr = null;
//			String requiredHeaders = "sr_no,admission_no,name,status,class,father_name,primary_contact,father_contact,assigned_amount,amount_collected,given_discount,discount_to_be_given,due_amount,paid_fine_amount";
//			FeeReportDataType feeReportDataType = FeeReportDataType.ONLY_NON_DUE_STUDENTS;
//
//			ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(instituteId, academicSessionId,
//					feesReportType, start, end, feeDueDate, feeIdsStr, requiredStandardsCSV, feePaymentTransactionStatus,
//					requiredHeaders, userId, downloadFormat,studentStatusCSV,feeHeadIdsStr, multipleAcademicSessionIdsStr, feeReportDataType);

//			89c3401a952/staff-attendance/generate-report/STAFF_MONTHLY_ATTENDANCE_SUMMARY?attendance_status=&startDate=**********&endDate=1612031400&staffCategory=

//			int instituteId = 10030;
////			String reportTypeStr = "STAFF_MONTHLY_ATTENDANCE_SUMMARY";
//			String reportTypeStr = "STAFF_ATTENDANCE_DETAILS_IN_A_DAY";
//			String attendanceStatusStr = null;
//			String staffCategoriesStr = null;
////			int start = 1675189800;
////			int end = 1677522600;
//			int start = **********;
//			int end = 1612031400;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(instituteId,
//					reportTypeStr, attendanceStatusStr, staffCategoriesStr, start, end, userId, downloadFormat);

//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);


//			StudentManager studentManager = context.getBean(StudentManager.class);
//			String status="ENROLMENT_PENDING,ENROLLED,NSO";
//			int instituteId = 10030;
//			String searchText = null;
//			int academicSessionId = 32;
//			UUID siblingGroupId = UUID.fromString("8a17c4ca-05ce-4205-96b9-17b235e3998e");
//			UUID studentId = UUID.fromString("8402e14c-90c6-451a-9ebe-f8ab35730e41");
//			final Set<StudentStatus> studentStatusList = StudentStatus.getStudentStatusSet(status);
//			final List<StudentSiblingDetails> studentSiblingDetailsList = studentManager
//					.getStudentSiblingDetailWithSessionList(instituteId, searchText, academicSessionId, studentStatusList);
//
////			final StudentSiblingDetails studentSiblingDetails = studentManager
////					.getStudentSiblingDetailWithSession(instituteId, siblingGroupId, academicSessionId);

//			StudentSiblingDetails studentSiblingDetails = studentManager.getStudentSiblingDetailsWithSessionByStudentId(
//					instituteId, academicSessionId, studentId);
//
////			for(StudentSiblingDetails studentSiblingDetails : studentSiblingDetailsList) {
////				System.out.println(studentSiblingDetails.getStudentList().size());
//				System.out.println(studentSiblingDetails);
////			}

//			StudentLeaveManagementManager studentLeaveManagementManager = context.getBean(StudentLeaveManagementManager.class);
//
//			final StudentLeaveAttendanceErrorPayload studentLeaveAttendanceErrorPayload = studentLeaveManagementManager.getStudentLeaveAttendanceErrorReason(
//					10030, 32, UUID.fromString("86b3c4fd-09e8-4925-bda5-c727ffd8bc9d"));
//
//			System.out.println("studentLeaveAttendanceErrorPayload : " + studentLeaveAttendanceErrorPayload);


//			StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//
//			int instituteId = 10030;
//			UUID studentId = UUID.fromString("8356afe4-de80-4663-818e-9fd4900a4033");
//			String admissionNumber = "Ad-13";
//			Integer admissionDate = null;
//			UUID houseId = null;
//			UUID siblingStudentId = null;
//			List<UUID> feeStructureIds = null;
//			List<UUID> optionalCourseIds = null;
//			boolean sendNotification = false;
//			boolean sendEmail = true;
//			List<UUID> discountStructureIds = null;
//			EntityDiscountStructurePayload entityDiscountStructurePayload = null;
//			TransportAssignmentPayload transportAssignmentPayload = null;
//
//			EnrollStudentPayload enrollStudentPayload = new EnrollStudentPayload(studentId, admissionNumber, admissionDate,
//					houseId, siblingStudentId, feeStructureIds, optionalCourseIds, sendNotification, sendEmail, discountStructureIds,
//					entityDiscountStructurePayload, transportAssignmentPayload);
//
////
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			final AdmitStudentResponse admittedStudent = studentAdmissionManager.admitStudent(instituteId,
//					enrollStudentPayload, userId);
//			System.out.println("out from manager");
//			if (admittedStudent == null || !admittedStudent.isSuccess()) {
//				System.out.println("error in admitting");
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to admit student"));
//			}
//
//			System.out.println("admittedStudent : " + admittedStudent.toString());
//
//			RegeneratePasswordUserData<Student> regeneratePasswordUserData = null;
//			if(admittedStudent.getStudentUserCreated() != null && admittedStudent.getStudentUserCreated()) {
//				System.out.println("resetting details");
//				regeneratePasswordUserData = studentAdmissionManager.getResetPasswordUserDetails(instituteId, admittedStudent.getStudentId());
//			}
//
//			if(enrollStudentPayload.isSendNotification()) {
//				System.out.println("in sms flow");
////				final Student student = studentManager.getStudentByAcademicSessionStudentId(
////						instituteId, admittedStudent.getAcademicSessionId(), admittedStudent.getStudentId());
////				studentSMSHandler.sendAdmissionSMSAsync(instituteId, student, regeneratePasswordUserData, admittedStudent, userId);
//			}
//
//			System.out.println("out of sms flow");
//			/**
//			 * only sending email when email is on from FE or user is created for student
//			 */
//			if(enrollStudentPayload.isSendEmail() &&
//					admittedStudent.getStudentUserCreated() != null && admittedStudent.getStudentUserCreated()) {
//				System.out.println("in email flow");
////				studentAdmissionEmailHandler.sendStudentAdmissionEmail(instituteId, regeneratePasswordUserData);
//			}
//
//			System.out.println("out of emailflow");

//			select * from student_attendance_register  where institute_id = ? and academic_session_id = ?  and student_id in (?)  and attendance_date >= ?  and attendance_date <= ?
//			10030
//			32
//			140a207b-f1ed-4e7f-9f61-e61757cef6aa
//			2021-01-16 10:33:39.0
//			2021-01-17 10:33:39.0
//			pushNotificationManager.getBellNotificationByEntity(10030, null, 0, 10);

//			examinationManager.updateExamMarksFeedStatus(10030, 32, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"),
//					MarksFeedStatus.SUBMITTED, UUID.fromString("a1ad15d0-9eba-40f7-95e6-9bda509cfd93"),
//					UUID.fromString("2a9178be-d88b-4cc5-855d-152b5c6699c9"), 27, 83);

//			final List<CourseDimensionSectionMarksStatusDetails> courseDimensionSectionMarksStatusDetailsList = examinationManager
//					.getCourseDimensionSectionMarksStatusDetails(10030, 32, UUID.fromString("c7f89e6e-08b4-4ce1-994e-fc42400bcb40"));

//			System.out.println(courseDimensionSectionMarksStatusDetailsList.toString());

//			UserSalaryManager userSalaryManager = context.getBean(UserSalaryManager.class);
//			final List<GeneratedStaffPayslip> generatedStaffPayslipList = userSalaryManager.generateSalaryPayslips(110, 153, 1);
//			System.out.println(generatedStaffPayslipList);
//
//			DateTime birthday = new DateTime(1074554051 * 1000L,  DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE)); // Format: (year, month, day, hour, minute)
//
//			DateTime now = DateTime.now();
//
//			DateTime nextSevenDays = now.plusDays(7);
//
//			if (birthday.getMonthOfYear() == now.getMonthOfYear() && birthday.getDayOfMonth() >= now.getDayOfMonth() && birthday.getDayOfMonth() <= nextSevenDays.getDayOfMonth()) {
//				if (birthday.getMonthOfYear() == now.getMonthOfYear() && birthday.getDayOfMonth() == now.getDayOfMonth()) {
//					System.out.println("birthday today");
//				} else {
//					System.out.println("7 days");
//				}
//			} else {
//				System.out.println("nothing.");
//			}
//			final ApplicationContext context = new ClassPathXmlApplicationContext("core-lib.xml");

//			final InstituteManager instituteManager = context.getBean(InstituteManager.class);
//			instituteManager.getAcademicSessionList(10030);
//			final PushNotificationManager pushNotificationManager = context.getBean(PushNotificationManager.class);
//			UUID userId = UUID.fromString("d150bffb-bea0-4f34-89a5-2e5a7d66af80");
//
//			System.out.println(pushNotificationManager.getBellNotificationWithoutUserListByUserId(10030, userId,
//					0, 30, new HashSet<>(Arrays.asList(NotificationEntity.CUSTOM, NotificationEntity.NOTICE))));

//			final HomeworkManager homeworkManager = context.getBean(HomeworkManager.class);
//			List<DatewiseHomeworkDetails> homeworkDetailsList = homeworkManager.getInstituteHomeworkDetails(10030, 32, 5, 0, HomeworkStatus.BROADCASTED);
//			System.out.println(homeworkDetailsList.getPaginationInfo().toString());
//			System.out.println(homeworkDetailsList.getResult().size());

//			Integer courseTotalStudentCount = 37;
//			Integer courseTotalPassStudentCount = 35;
//			Integer courseTotalFailStudentCount = 2;
//			Double coursePassPercentageCount = courseTotalStudentCount == null || courseTotalStudentCount == 0
//					? 0 : ((double) courseTotalPassStudentCount / courseTotalStudentCount) * 100d;
//
//			System.out.println(courseTotalPassStudentCount);
//			System.out.println(courseTotalStudentCount);
//			System.out.println((double) courseTotalPassStudentCount / courseTotalStudentCount * 1d);
//			System.out.println(coursePassPercentageCount);
//
//			System.out.println(NumberUtils.formatDouble(coursePassPercentageCount));


//			/ecf846c3-1abd-4a54-afa3-5c6f150af34a/examination/generate-reports/SUBJECT_WISE_RANK_REPORT?academic_session_id=33
//			&standard_id=cb352822-3b78-44da-b513-8332b900dc46&section_id=&exam_id=5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6&course_type=
//			&course_id=&staffId=&reportTypeName=&sectionIdsStr=&examIdsStr=5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6&courseIdsStr=
//			&compareCumulativeExamIdsStr=&rankTill=1


//			/ecf846c3-1abd-4a54-afa3-5c6f150af34a/examination/generate-reports/SUBJECT_WISE_RANK_REPORT?academic_session_id=33
//			&standard_id=cb352822-3b78-44da-b513-8332b900dc46&section_id=&exam_id=ccdc7962-b852-4906-a800-a2cab26230b3
//			&course_type=&course_id=&staffId=&reportTypeName=&sectionIdsStr=&examIdsStr=ccdc7962-b852-4906-a800-a2cab26230b3
//			&courseIdsStr=&compareCumulativeExamIdsStr=5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6&rankTill=4

//			final StaffAttendanceReportGenerator staffAttendanceReportGenerator = context.getBean(StaffAttendanceReportGenerator.class);
//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
////			final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
//
////			/97b8ac6d-1645-49f6-b9fa-389c3401a952/fees/generate-report/STUDENT_AGGREGATED_PAYMENT
////			?academic_session_id=32&requiredMultiSession=&reportStartDate=-1&reportEndDate=-1&fee_ids=&requiredStandards=
////			&feeDueDate=-1&studentStatus=&fee_head_ids=&transactionType=&includeDueStudentOnly=false
//
////			int instituteId = 10030;
////			Integer academicSessionId = 32;
//////			FeesReportType feesReportType = FeesReportType.STUDENT_FEES_PAYMENT;
////			FeesReportType feesReportType = FeesReportType.STUDENT_FEES_HEAD_PAYMENT;
////			Integer start = -1;
////			Integer end = -1;
////			Integer feeDueDate = -1;
////			String feeIdsStr = null;
////			String requiredStandardsCSV = null;
////			FeePaymentTransactionStatus feePaymentTransactionStatus = null;
////			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
////			DownloadFormat downloadFormat = null;
////			String studentStatusCSV = null;
////			String feeHeadIdsStr = null;
////			String multipleAcademicSessionIdsStr = null;
////			boolean includeDueStudentOnly = false;
//
//			int instituteId = 10030;
//			String reportTypeStr = "";
//			String attendanceStatusStr = null;
//			String staffCategoriesStr = null;
//			int start = 1675189800;
//			int end = 1677522600;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final ReportDetails reportDetails = staffAttendanceReportGenerator.generateReport(instituteId,
//					reportTypeStr, attendanceStatusStr, staffCategoriesStr, start, end, userId, downloadFormat);
////
////			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//
////			/2.0/student/reports/110/generate-report/STUDENT_DETAILS_REPORT?academic_session_id=153&
////			requiredHeaders=serial_number,student_admission_number,student_session_status,student_name,student_father_name,student_mother_name,student_class_name,student_house_name,student_admission_date,student_dob,student_gender,student_primary_contact_number,student_category,student_religion,student_caste,student_rte,student_specially_abled,student_specially_abled_type,student_bpl,student_registration_number,student_relieve_date,student_birth_place,student_aadhar_number,student_nationality,student_mother_tongue,student_area_type,student_permanent_address,student_present_address,student_city,student_state,student_zipcode,student_present_city,student_present_state,student_present_zipcode,student_primary_email,student_sponsored,student_whatsapp_number,student_admission_in_class,student_present_post_office,student_permanent_post_office,student_present_police_station,student_permanent_police_station,student_academic_session
////			&requiredStandards=&user_id=74cd36ca-180d-4360-8fe5-f95472bf4e51&downloadFormat=EXCEL&studentStatus=&newAdmission=false
////			final StudentReportsGenerator studentReportsGenerator = context.getBean(StudentReportsGenerator.class);
////			final ReportDetails reportDetails = studentReportsGenerator.generateReportDetails(
////					10030, 32, StudentReportType.STUDENT_DETAILS_REPORT, -1, -1,
////					"serial_number,student_admission_number,student_session_status,student_name,student_father_name,student_mother_name,student_class_name,student_house_name,student_admission_date,student_dob,student_gender,student_primary_contact_number,student_category,student_religion,student_caste,student_rte,student_specially_abled,student_specially_abled_type,student_bpl,student_registration_number,student_relieve_date,student_birth_place,student_aadhar_number,student_nationality,student_mother_tongue,student_area_type,student_permanent_address,student_present_address,student_city,student_state,student_zipcode,student_present_city,student_present_state,student_present_zipcode,student_primary_email,student_sponsored,student_whatsapp_number,student_admission_in_class,student_present_post_office,student_permanent_post_office,student_present_police_station,student_permanent_police_station,student_academic_session",
////					null, new FilterationCriteria(), UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"),
////					null, false
////			);
//////			int instituteId, Integer academicSessionId, StudentReportType studentReportType,
//////					Integer start, Integer end, String requiredHeaders, String requiredStandardsCSV,
//////					FilterationCriteria filterationCriteria, UUID userId,String studentStatusStr,boolean newAdmission
////			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
////
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//			/ecf846c3-1abd-4a54-afa3-5c6f150af34a/examination/generate-report
//			/5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6?section_id_str=&exclude_coscholastic_subjects=false
//			reportType=EXAM_MARKS_REPORT&show_dimensions=false&show_total_column_dimension=false&display_rank=true
//			&show_coscholastic_grade=false&display_attendance=true&show_scholastic_grade=false&parent_remark=true
//			&requiredHeaders=sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result

//			/2.0/examination-reports/101/view-report/MULTIPLE_EXAM_MARKS_REPORT?academic_session_id=33
//			&standard_id=cb352822-3b78-44da-b513-8332b900dc46&section_id=&exam_id=5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6
//			&course_id=&course_type=&user_id=4cb09c60-eabc-4192-a9d4-e357cf773db3&staff_id=&report_card_type=&section_ids_str=
//			&exam_ids_str=5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6&course_ids_str=&compare_cumulative_exam_ids_str=&rank_till=
//			&exclude_coscholastic_subjects=false&show_coscholastic_grade=false&show_scholastic_grade=false
//			&requiredHeaders=sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks
//			int instituteId = 101;
//			int academicSessionId = 33;
//			ExamReportType examReportType = ExamReportType.DISCO;
//			UUID standardId = UUID.fromString("cb352822-3b78-44da-b513-8332b900dc46");
//			Integer sectionId = null;
////			Integer sectionId = 85;
//			UUID examId = UUID.fromString("5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6");
//			UUID courseId = null;
//			CourseType courseType = null;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			UUID staffId = null;
//			String reportCardType = null;
//			String sectionIdSetStr = null;
//			String examIdSetStr = "5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6,ccdc7962-b852-4906-a800-a2cab26230b3";
////			String examIdSetStr = "ccdc7962-b852-4906-a800-a2cab26230b3";
////			String examIdSetStr = "5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6";
//			String courseIdSetStr = null;
//			String compareCumulativeWithExamIdSetStr = null;
//			Integer rankTill = null;
//			boolean excludeCoScholasticSubjects = false;
//			boolean showCoScholasticGrade = true;
//			boolean showScholasticGrade = false;
//			String requiredHeaders = "sr_no,admission_no,roll_no,name,father_name,dob,class,percentage,grade,division,result,rank,attendance,parent_remarks";
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			boolean isSortStudentOnRank = false;

//			int instituteId = 702;
//			int academicSessionId = 55;
//			TransportReportType transportReportType = TransportReportType.ROUTE_DETAILS;
//			String requiredHeaders = "serial_number,transport_route_type,transport_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_route_area_time,student_count";
//			String requiredStandards = "";
//			String feeIds = "";
//			boolean onlyEnrolledStudents = false;
//			String pickupIdStr = "";
//			String areaIdStr = "";
//			String dropIdStr = "";
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			final FeeReportDetailsGenerator feeReportDetailsGenerator = context.getBean(FeeReportDetailsGenerator.class);
////			UUID userId = UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30");
////			///2.0/transport/reports/702/generate-report/ROUTE_DETAILS?academic_session_id=55&requiredHeaders=serial_number,transport_route_type,transport_route_name,transport_parent_area_name,transport_area_name,transport_area_id,transport_route_area_time,student_count&requiredStandards=&fee_ids=&only_enrolled_students=false&area_ids=&pickup_ids=&drop_ids=&download_format=EXCEL&user_id=cf33b91c-cab2-421a-802d-c18ba8423e30
//			ReportDetails reportDetails = transportReportsGenerator.generateReport(702, 55,
//					TransportReportType.STOPPAGE_FEES_CHART, -1, -1, null, null, null, false,null,null,null,null, DownloadFormat.EXCEL, null);
//			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//					standardId, sectionId, examId, courseId, courseType, userId, null, reportCardType, staffId,
//					sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr, rankTill,
//					excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders, isSortStudentOnRank);

//			int instituteId, Integer academicSessionId, FeesReportType feesReportType,
//					Integer start, Integer end, Integer feeDueDate, String feeIdsStr, String requiredStandardsCSV,
//					FeePaymentTransactionStatus feePaymentTransactionStatus,String requiredHeaders, UUID userId, DownloadFormat downloadFormat,
//					String studentStatusCSV, String feeHeadIdsStr, String multipleAcademicSessionIdsStr, boolean includeDueStudentOnly
//			ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(10030,
//					32, FeesReportType.STUDENT_DISCOUNT_ASSIGNMENT,
//				-1, -1, -1, null, null, null, null,
//					UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), DownloadFormat.EXCEL,
//					"ENROLLED", null, null, false);
//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);
//
//			int instituteId = 101;
//			int academicSessionId = 33;
//			ExamReportType examReportType = ExamReportType.MULTIPLE_EXAM_MARKS_REPORT;
//			UUID standardId = UUID.fromString("cb352822-3b78-44da-b513-8332b900dc46");
//			Integer sectionId = 85;
//			UUID examId = UUID.fromString("5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6");
//			UUID courseId = null;
//			CourseType courseType = null;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			UUID staffId = null;
//			String reportCardType = null;
//			String sectionIdSetStr = null;
//			String examIdSetStr = "ccdc7962-b852-4906-a800-a2cab26230b3";
////			String examIdSetStr = "5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6";
//			String courseIdSetStr = null;
//			String compareCumulativeWithExamIdSetStr = null;
//			Integer rankTill = null;
//			boolean excludeCoScholasticSubjects = true;
//			boolean showCoScholasticGrade = true;
//			boolean showScholasticGrade = false;
//			String requiredHeaders = "sr_no,admission_no,roll_no,name,father_name,class,percentage,grade,division,result,rank,attendance";
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//					standardId, sectionId, examId, courseId,
//					courseType, userId, downloadFormat,
//					reportCardType, staffId, sectionIdSetStr,
//					examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr,
//					rankTill, excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders);
//			boolean excludeCoScholasticSubjects = true;
//			boolean showCoScholasticGrade = true;
//			boolean showScholasticGrade = false;
//			String requiredHeaders = "sr_no,admission_no,roll_no,name,father_name,class,percentage,grade,division,result,rank,attendance";
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//					standardId, sectionId, examId, courseId,
//					courseType, userId, downloadFormat,
//					reportCardType, staffId, sectionIdSetStr,
//					examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr,
//					rankTill, excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders);
//			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//					standardId, sectionId, examId, courseId, courseType, userId, null, reportCardType, staffId,
//					sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr, rankTill,
//					excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders);
//			ReportDetails reportDetails = courseReportGenerator.generateReport(101,33,
//					UUID.fromString("1e3eb7ca-d827-11ed-9f73-8f324eeebff2"),
//					null,null,
//					DownloadFormat.EXCEL,UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"),
//					CourseReportType.STUDENT_COURSE_DETAIL_REPORT);
//
//			if(reportDetails == null){
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
//			}

//			int instituteId, int academicSessionId, TransportReportType transportReportType,
//					Integer start, Integer end, String requiredHeaders, String requiredStandardsCSV, String feeIdsStr,
//			boolean onlyEnrolledStudents,String areaIdStr,String pickupIdsStr,String dropIdsStr,DownloadFormat downloadFormat, UUID userId

//			TransportReportsGenerator transportReportsGenerator = context.getBean(TransportReportsGenerator.class);
//			final ReportDetails reportDetails = transportReportsGenerator
//					.generateReport(10030, 32, TransportReportType.TRANSPORT_ASSIGNED_STUDENTS_DETAILS,
//							-1, -1, "serial_number,student_admission_number,student_name,student_status,student_father_name,student_class_name,transport_pickup_route_name,transport_drop_route_name,transport_area_name,transport_area_id,transport_route_area_payble_amount,transport_route_area_due_amount",
//							null, null, false, null, null, null,
//							DownloadFormat.EXCEL, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

//			/2.0/transport/reports/10230/view-report/TRANSPORT_ASSIGNED_STUDENTS_DETAILS?
//			academic_session_id=136&
//			requiredHeaders=serial_number,student_admission_number,student_name,student_status,student_father_name,student_class_name,transport_pickup_route_name,transport_drop_route_name,transport_area_name,transport_area_id,transport_route_area_payble_amount,transport_route_area_due_amount
//			&requiredStandards=&fee_ids=&only_enrolled_students=false&area_ids=&pickup_ids=&drop_ids=
//			&user_id=a0571475-5ae3-419c-ac8c-66abffd52d6a

//
//
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//
//			System.out.println(htmlReportTable);


            //			System.out.println(reportDetails);
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//			System.out.println(htmlReportTable);
//			int instituteId = 101;
//			int academicSessionId = 33;
//			ExamReportType examReportType = ExamReportType.OVERALL_RANK_REPORT;
//			UUID standardId = UUID.fromString("cb352822-3b78-44da-b513-8332b900dc46");
//			Integer sectionId = 85;
//			UUID examId = UUID.fromString("5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6");
//			UUID courseId = null;
//			CourseType courseType = null;
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			UUID staffId = null;
//			String reportCardType = null;
//			String sectionIdSetStr = null;
//			String examIdSetStr = "ccdc7962-b852-4906-a800-a2cab26230b3";
////			String examIdSetStr = "5ce75b01-1e7d-42e8-9b4d-bd1bc3327cc6";
//			String courseIdSetStr = null;
//			String compareCumulativeWithExamIdSetStr = null;
//			Integer rankTill = null;
//			boolean excludeCoScholasticSubjects = true;
//			boolean showCoScholasticGrade = true;
//			boolean showScholasticGrade = false;
//			String requiredHeaders = "sr_no,admission_no,roll_no,name,father_name,class,percentage,grade,division,result,rank,attendance";
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			boolean showDimensions = false;
//			boolean showTotalColumnDimension = false;
//
//			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//					standardId, sectionId, examId, courseId,
//					courseType, userId, downloadFormat,
//					reportCardType, staffId, sectionIdSetStr,
//					examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr,
//					rankTill, excludeCoScholasticSubjects, showCoScholasticGrade, showScholasticGrade, requiredHeaders);

//			final ReportDetails reportDetails = examReportGenerator.getClassExamReport(instituteId, examId, null,
//					excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension,
//					showCoScholasticGrade, showScholasticGrade, requiredHeaders, ExamReportType.EXAM_MARKS_REPORT,
//					downloadFormat, userId);

//			int instituteId = 101;
//			UUID examId = UUID.fromString("ccdc7962-b852-4906-a800-a2cab26230b3");
//			UUID examId = UUID.fromString("b309cc82-a9ab-4e56-ae20-855323f833db");
//			Integer classSectionId = null;
//			excludeCoScholasticSubjects = false;
//			boolean showDimensions = true;
//			boolean showTotalColumnDimension = false;
//			boolean displayRank = true;
//			showCoScholasticGrade = true;
//			boolean displayAttendance = true;
//
//			final ReportDetails reportDetails = examReportGenerator.getClassExamReport(instituteId, examId, null,
//					excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension,
//					showCoScholasticGrade, showScholasticGrade, requiredHeaders, ExamReportType.EXAM_MARKS_REPORT,
//					downloadFormat, userId);

//			final ExcelReportGenerator excelReportGenerator = context.getBean(ExcelReportGenerator.class);
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//			DateTime birthday = new DateTime(1074554051 * 1000L,  DateTimeZone.forTimeZone(DateUtils.DEFAULT_TIMEZONE)); // Format: (year, month, day, hour, minute)
//
//			DateTime now = DateTime.now();
//
//			DateTime nextSevenDays = now.plusDays(7);
//
//			if (birthday.getMonthOfYear() == now.getMonthOfYear() && birthday.getDayOfMonth() >= now.getDayOfMonth() && birthday.getDayOfMonth() <= nextSevenDays.getDayOfMonth()) {
//				if (birthday.getMonthOfYear() == now.getMonthOfYear() && birthday.getDayOfMonth() == now.getDayOfMonth()) {
//					System.out.println("birthday today");
//				} else {
//					System.out.println("7 days");
//				}
//			} else {
//				System.out.println("nothing.");
//			}


//			final FollowUpManager followUpManager = context.getBean(FollowUpManager.class);
//
//			final List<StudentFollowUpDetails> studentFollowUpDetailsList = followUpManager.getStudentFollowUpDetails(101);
//
////			for(StudentFollowUpDetails entry :studentFollowUpDetailsList) {
////				for(StudentFollowUpDetailsRow studentFollowUpDetailsRow : entry.getStudentFollowUpDetailsRowList()) {
////					System.out.println(studentFollowUpDetailsRow.getStudentDetailedRow().getStudentBasicInfo().getName());
////					System.out.println(studentFollowUpDetailsRow.getFollowUpMode().toString());
////				}
////			}
//
////			final List<FollowUpPayload> followUpPayloadList = followUpManager.getFeeFollowUpTrailByEntityId(
////					10030, UUID.fromString("48aa4c8e-0b48-4fbd-87e0-b71d4bd5291a"), FollowUpEntity.STUDENT);
////
////			for(FollowUpPayload followUpPayload : followUpPayloadList) {
////				System.out.println(followUpPayload.toString());
////				System.out.println(followUpPayload.getFollowUpMode().getFollowUpModeDisplayName());
////			}
////			Map<UUID,List<StudentFollowUpDetails>> studentMap = followUpManager.getStudentFeeFollowUpDetails(101,FollowUpEntity.STUDENT);
////			System.out.println(studentMap);
////
//			final NoticeBoardManager noticeBoardManager = context.getBean(NoticeBoardManager.class);
//
//			List<NoticeDetails> noticeDetailsList = noticeBoardManager.getInstituteNoticeDetails(10030, 32);
//			for(NoticeDetails noticeDetails : noticeDetailsList) {
//				System.out.println(noticeDetails.toString());
//			}
//

//			final TransportReportsGenerator transportReportsGenerator = context.getBean(TransportReportsGenerator.class);
////			final PaymentGatewayManager paymentGatewayManager = context.getBean(PaymentGatewayManager.class);
////
//////			{
//////				"transactionData": {
//////				"status": "error",
//////						"order_id": "gAAAAAAAAAAAcy_NwaNloWi3ereDc2Fo8jfhKDUDoXTBsQuIiQM4Gp469VVCOdPsSSrkdlDry6IMcdaiOl1c2x-T2M7w65mqFg%3D%3D",
//////						"message": "Request%20authorization%20is%20declined"
//////			}
//////			}
//
//			PGProcessTransactionPayload processTransactionPayload = new PGProcessTransactionPayload();
//			Map<String, String> transactionData = new HashMap<>();
//			transactionData.put("status", "error");
//			transactionData.put("order_id", "gAAAAAAAAAAAcy_NwaNloWi3ereDc2Fo8j3JIrQfBU_ARprcZU45i7x8qnYYTECfTAvP0GZ11a6wIXbKOtn4JWKJ909kfc_wvg%3D%3D");
//			transactionData.put("message", "Debit%20has%20been%20failed");
//			processTransactionPayload.setTransactionData(transactionData);
//			PGProcessTransactionResponse initiateTransactionResponse = paymentGatewayManager.processTransaction(
//					10030, processTransactionPayload, true);
//
//			System.out.println(initiateTransactionResponse);

//			PGInitiateTransactionPayload initiateTransactionPayload = new PGInitiateTransactionPayload();
//			{
//				"paymentGatewayStage": "PROD",
//					"userId": "140a207b-f1ed-4e7f-9f61-e61757cef6aa",
//					"transactionType": "STUDENT_FEE_PAYMENT",
//					"paymentGatewayAmount": 1000.0,
//					"description": "",
//					"instantDiscountAmount": 0.0,
//					"fineAmount": 0.0,
//					"studentFeePaymentMetadata": {
//				"academicSessionId": 32,
//						"dueFeeIds": [
//				"7488c6e4-b77a-4e7a-a1ea-7205adbf60cd"
//        ]
//			},
//				"currency": "INR",
//					"walletAmount": 0.0
//			}
//			initiateTransactionPayload.setPaymentGatewayStage(PaymentGatewayStage.PROD);
//			initiateTransactionPayload.setUserId(UUID.fromString("140a207b-f1ed-4e7f-9f61-e61757cef6aa"));
//			initiateTransactionPayload.setTransactionType(PGTransactionType.STUDENT_FEE_PAYMENT);
//			initiateTransactionPayload.setPaymentGatewayAmount(1000.0);
//			initiateTransactionPayload.setDescription("");
//			initiateTransactionPayload.setInstantDiscountAmount(0.0);
//			initiateTransactionPayload.setFineAmount(0.0);
//
//			PGStudentFeePaymentMetadata studentFeePaymentMetadata = new PGStudentFeePaymentMetadata();
//			studentFeePaymentMetadata.setAcademicSessionId(32);
//			studentFeePaymentMetadata.setDueFeeIds(new HashSet<>(Arrays.asList(UUID.fromString("7488c6e4-b77a-4e7a-a1ea-7205adbf60cd"))));
//			initiateTransactionPayload.setStudentFeePaymentMetadata(studentFeePaymentMetadata);
//			initiateTransactionPayload.setCurrency(PaymentGatewayCurrency.INR);
//			initiateTransactionPayload.setWalletAmount(0.0);
//
//			PGInitiateTransactionResponse initiateTransactionResponse = paymentGatewayManager.initiatePaymentTransaction(
//					10030, initiateTransactionPayload, MobileAppPlatform.GOOGLE);
//
//			System.out.println(initiateTransactionResponse);


//			final NoticeBoardManager noticeBoardManager = context.getBean(NoticeBoardManager.class);
//			boolean migrate = noticeBoardManager.migrateNoticeDataSessionSpecific(new HashSet<>(Arrays.asList(10030)),
//					true);
//			System.out.println("migrate : " + migrate);

//			/2.0/notice-board/notice-details/SAVED?institute_id=10030&date=0&limit=10&offset=0&academic_session_id=32
//			System.out.println(noticeBoardManager.getNoticeDetails(10030,
//					0, 0, 10, NoticeBoardStatus.SAVED, 32));

//			final StudentManager studentManager = context.getBean(StudentManager.class);
//
//
//			List<StudentDocumentType> collectedStudentDocumentType = new ArrayList<>();
//			collectedStudentDocumentType.add(StudentDocumentType.STUDENT_PROFILE_IMAGE);
//			collectedStudentDocumentType.add(StudentDocumentType.AADHAR_CARD);
//			studentManager.updateCollectedDocuments(10030, UUID.fromString("d0e0ac6b-1cc6-463c-ba61-c8d152e212a0"), collectedStudentDocumentType);

//			 ReportDetails reportDetails = examReportGenerator.getClassExamReport(10030,
//			 		UUID.fromString("a1ad15d0-9eba-40f7-95e6-9bda509cfd93"), null,
//			 		false, false, false, false, false,
//			 		false, false, DownloadFormat.EXCEL, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

            // if(reportDetails == null){
            // 	throw new ApplicationException(
            // 			new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
            // }

            // HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
            // System.out.println(htmlReportTable);
//			final ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//
//			ReportDetails reportDetails = examReportGenerator.getClassExamReport(10030,
//					UUID.fromString("a1ad15d0-9eba-40f7-95e6-9bda509cfd93"), null,
//					false, false, false, false, false,
//					false, false, DownloadFormat.EXCEL, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
//			if(reportDetails == null){
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
//			}

//			final IncomeExpenseReportGenerator incomeExpenseReportGenerator = context.getBean(IncomeExpenseReportGenerator.class);
//			final ReportDetails reportDetails = incomeExpenseReportGenerator.generateReport(702,
//					IncomeExpenseReportType.NET_COLLECTION_SUMMARY_REPORT, 1693656829, 1695730429, null, UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30") , DownloadFormat.EXCEL);
////
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//			System.out.println(htmlReportTable);
//
//			ReportDetails reportDetails = examReportGenerator.getClassExamReport(10030,
//					UUID.fromString("a1ad15d0-9eba-40f7-95e6-9bda509cfd93"), null,
//					false, false, false, false, false,
//					false, false, DownloadFormat.EXCEL, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
//			if(reportDetails == null){
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
//			}
//
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//			System.out.println(htmlReportTable);
//
//			if(reportDetails == null){
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
//			}
//
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//			System.out.println(htmlReportTable);
//
//			if(reportDetails == null){
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Unable to generate report"));
//			}
//
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//			System.out.println(htmlReportTable);
////
//			final ReportDetails reportDetails = examReportGenerator.getClassExamReport(110, UUID.fromString("31abc041-08d0-4ff8-ae64-429e11841e8c"),
//					null,
//					false, false, false, false, false,
//					false, false);

//			System.out.println(reportDetails);
//			HTMLReportTable htmlReportTable = ReportUtils.convertToHTMLTable(reportDetails);
//			System.out.println(htmlReportTable);
//			int instituteId = 101;
//			UUID examId = UUID.fromString("d9ee8782-4d11-4f7d-ab78-f33fe5b9011d");
//			Integer classSectionId = null;
//			boolean excludeCoScholasticSubjects = false;
//			boolean showDimensions = true;
//			boolean showTotalColumnDimension = false;
//			boolean displayRank = true;
//			boolean showCoScholasticGrade = true;
//			boolean displayAttendance = true;

//			/1aea0dea-6130-444a-957e-c3c7468c859c/examination/generate-report/f3636598-254a-4e6e-9e3f-7f610ec5ec26?section_id=1448&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&display_rank=true&show_coscholastic_grade=true&display_attendance=true
            ///2.0/examination-reports/10227/exam/f3636598-254a-4e6e-9e3f-7f610ec5ec26?section_id=1448&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&display_rank=true&show_coscholastic_grade=true&display_attendance=true
//			int instituteId = 10225;
//			UUID examId = UUID.fromString("6313d70f-93a0-42ad-8a32-41ab53d0cc47");
//			Integer classSectionId = null;
//			boolean excludeCoScholasticSubjects = false;
//			boolean showDimensions = false;
//			boolean showTotalColumnDimension = false;
//			boolean displayRank = true;
//			boolean displayAttendance = true;
//			boolean showCoScholasticGrade = true;
//			boolean showScholasticGrade = true;
//			final ReportOutput reportOutput = examReportGenerator.getClassExamReport(instituteId, examId, classSectionId,
//					excludeCoScholasticSubjects, showDimensions, showTotalColumnDimension, displayRank, showCoScholasticGrade,
//					displayAttendance, showScholasticGrade);

//

//			int instituteId = 10030;
//			int academicSessionId = 32;
//			ExamReportType examReportType = ExamReportType.OVERALL_RANK_REPORT;
//			UUID standardId = UUID.fromString("fa1c9752-2edd-49b1-8e88-22c02a3e288c");
//			Integer sectionId = null;
//			UUID examId = null;
//			UUID courseId = null;
//			CourseType courseType = null;
//			UUID userId =UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
//			String reportCardType = null;
//			UUID staffId = null;
////        String sectionIdSetStr = "83,84";
//			String sectionIdSetStr = null;
//			String examIdSetStr = "a1ad15d0-9eba-40f7-95e6-9bda509cfd93,c7f89e6e-08b4-4ce1-994e-fc42400bcb40";
////        	String courseIdSetStr = "8f7c6026-61f0-41e5-9da0-e6ab92f96848,2a9178be-d88b-4cc5-855d-152b5c6699c9";
//			String courseIdSetStr = null;
//			String compareCumulativeWithExamIdSetStr = null;
//			Integer rankTill = 20;
//			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
//					standardId, sectionId, examId, courseId, courseType, userId, downloadFormat, reportCardType, staffId,
//					sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr, rankTill);
//
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

            //  ReportDetails reportDetails = transportReportsGenerator.generateReports(702, 55, UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30"), null, TransportReportType.STOPPAGE_FEES_CHART);
            //  ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
            //  final OutputStream outputStream = new FileOutputStream(DEST2 + reportOutput.getReportName());
            //  reportOutput.getReportContent().writeTo(outputStream);

////			int instituteId = 10030;
////			ExamReportType examReportType = ExamReportType.OVERALL_RANK_REPORT;
////			UUID standardId = UUID.fromString("fa1c9752-2edd-49b1-8e88-22c02a3e288c");
////			Integer sectionId = null;
////			UUID examId = null;
////			UUID courseId = null;
////			CourseType courseType = null;
////			UUID userId =UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
////			DownloadFormat downloadFormat = DownloadFormat.EXCEL;
////			String reportCardType = null;
////			UUID staffId = null;
//////        String sectionIdSetStr = "83,84";
////			String sectionIdSetStr = null;
////			String examIdSetStr = "a1ad15d0-9eba-40f7-95e6-9bda509cfd93,c7f89e6e-08b4-4ce1-994e-fc42400bcb40";
//////        	String courseIdSetStr = "8f7c6026-61f0-41e5-9da0-e6ab92f96848,2a9178be-d88b-4cc5-855d-152b5c6699c9";
////			String courseIdSetStr = null;
////			String compareCumulativeWithExamIdSetStr = null;
////			Integer rankTill = 20;
////			ReportDetails reportDetails = examReportGenerator.generateReport(instituteId, academicSessionId, examReportType,
////					standardId, sectionId, examId, courseId, courseType, userId, downloadFormat, reportCardType, staffId,
////					sectionIdSetStr, examIdSetStr, courseIdSetStr, compareCumulativeWithExamIdSetStr, rankTill);
////
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//			final OutputStream outputStream = new FileOutputStream(DEST3 + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);
            // ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
            // final OutputStream outputStream = new FileOutputStream(DEST2 + reportOutput.getReportName());

//			//generate-report/ATTENDANCE_DETAILS_IN_A_DAY?academic_session_id=32&standard_id=&attendance_status=&attendance_type_id=&startDate=1672511400&endDate=1691605800&downloadReport=true&downloadFormat=PDF
//			int academicSessionId = 55;
//			String reportType = "ATTENDANCE_DETAILS_IN_A_MONTH";
//			String attendanceStatusStr = null;
//			String attendanceTypeIdStr = null;
//			String standardIdStr = null;
//			Integer start = 1690865142;
//			Integer end = 1692247542;
//			UUID userId = UUID.fromString("cf33b91c-cab2-421a-802d-c18ba8423e30");
//			DownloadFormat downloadFormat = DownloadFormat.PDF;

//			ReportDetails reportDetails = attendanceReportGenerator.generateReport(instituteId, academicSessionId, reportType, standardIdStr, attendanceStatusStr,
//					attendanceTypeIdStr, start, end, userId, downloadFormat);
//			if (reportDetails == null) {
////				return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
//				System.out.println("reportDetails is null");
//			}
//
//			if (downloadFormat == DownloadFormat.PDF) {
//				final DocumentOutput documentOutput = pdfReportGenerator.generateReport(reportDetails);
//				if (documentOutput == null) {
//					System.out.println("documentOutput is null");
//				}
//				System.out.println(documentOutput);
//				ByteArrayOutputStream byteArrayOutputStream = documentOutput.getContent();
//				try (OutputStream outputStream = new FileOutputStream(DEST2)) {
//					byteArrayOutputStream.writeTo(outputStream);
//				}
//				return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
//						.header("Content-Disposition", "filename=" + documentOutput.getName())
//						.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
//			} else {
//				final ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//				if (reportOutput == null) {
////					return Response.status(Response.Status.NOT_FOUND.getStatusCode()).build();
//					System.out.println("null report");
//				}
//
            //}


//			final HomeworkManager homeworkManager = context.getBean(HomeworkManager.class);
//			final FollowUpManager followUpManage = context.getBean(FollowUpManager.class);


//			StudentHomeworkStats studentHomeworkStats = homeworkManager.getStudentHomeworkStats(
//					10030, UUID.fromString("09b6d630-036f-477a-baf6-d924299d4a50"));

//			List<User> userList = homeworkManager.getHomeworkUsers(10030, UUID.fromString("db8affb7-b3b0-4052-b769-07bb4fc6bd64"));
//
//			System.out.println("userList : " + userList);
//			if(CollectionUtils.isEmpty(userList)) {
//				System.out.println("list is empty");
//			} else {
//				System.out.println("It is not empty");
//			}

//			StudentHomeworkStats studentHomeworkStats = homeworkManager.getStudentHomeworkStats(
//					10030, UUID.fromString("09b6d630-036f-477a-baf6-d924299d4a50"));
//           StudentHomeworkStats studentHomeworkStats = homeworkManager.getStudentHomeworkStats(
// 		     10030, UUID.fromString("09b6d630-036f-477a-baf6-d924299d4a50"));
//
//           System.out.println(studentHomeworkStats.toString());

//			StandardHomeworkDetails standardHomeworkDetails = homeworkManager.getHomeworkDetailsByFilter(10030,
//					32, UUID.fromString("ce85eed6-e510-4e29-912b-84fbea45290d"),
//					null, null, null);
//			StandardHomeworkDetails standardHomeworkDetails = homeworkManager.getHomeworkDetailsByFilter(10030,
//					32, null, null, 1690137000, null);
//			System.out.println("standardHomeworkDetails : " + standardHomeworkDetails);

//			final List<StudentHomeworkViewSubmissionDetails> studentHomeworkViewSubmissionDetailsList = homeworkManager.
//					getStudentHomeworkViewSubmissionDetailsList(10030, 32,
//							UUID.fromString("3bd92947-399a-4ff3-bfa1-5a4621b2585e"),
//							UUID.fromString("ce85eed6-e510-4e29-912b-84fbea45290d"));
//
//			System.out.println(studentHomeworkViewSubmissionDetailsList.toString());

//			List<User> userList = homeworkManager.getHomeworkUsers(10030, new HashSet<>(Arrays.asList(UUID.fromString("0b65dd1c-4a9a-439d-a244-572094b14c65"))));
//			System.out.println("userList : " + userList);

//			final StudentHomeworkDetails studentHomeworkDetails = homeworkManager.getStudentHomeworkDetails(10030, UUID.fromString("140a207b-f1ed-4e7f-9f61-e61757cef6aa"), null);
//
//			System.out.println("studentHomeworkDetails : " + studentHomeworkDetails);

//			final Student student = studentManager.getStudentByLatestAcademicSession(
//					UUID.fromString("140a207b-f1ed-4e7f-9f61-e61757cef6aa"),
//					Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING));


//			401066ae-0f33-473e-9da4-9673e971fc97
//			0390f54b-4a59-4b97-8a59-74870eb955b5
//			32
//			16
//			final StudentTimetableDetails studentTimetableDetails =
//					timetableManager.getStudentTimetableDetails(10030, 32,
//							UUID.fromString("401066ae-0f33-473e-9da4-9673e971fc97"),
//							UUID.fromString("0390f54b-4a59-4b97-8a59-74870eb955b5"), 16);

//			(int instituteId,
//					int academicSessionId, UUID studentId, UUID standardId, Integer sectionId)


//			List<FacultyPeriodSubstitutionDetails> facultyPeriodSubstitutionDetailsList =
//					timetableManager.getFacultyPeriodSubstitutionDetails(10030, 10,
//							UUID.fromString("fb0db604-d1ef-427e-a047-27c670e499ab"), DayOfWeek.SATURDAY);
//
//			System.out.println(studentTimetableDetails);

//			System.out.println(timetableManager.getFacultyPeriodSubstitutionDetails(10030, 10,
//					UUID.fromString("6d33f1dd-fbfd-4390-9029-d94c93608daa"), DayOfWeek.SATURDAY));

//			System.out.println(timetableManager.getTimetableDetailsByTimetableId(10030, 10, UUID.fromString("aea79eef-a5df-4f8c-8bf0-0b91194ff8ca")));

//			System.out.println(timetableManager.updateTimetableStatus(10030, UUID.fromString("aea79eef-a5df-4f8c-8bf0-0b91194ff8ca"),
//					TimetableStatus.SAVED, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3")));
//			StudentTimetableDetails studentTimetableDetails = timetableManager.getStudentTimetableDetails(10030, 10,
//					UUID.fromString("140a207b-f1ed-4e7f-9f61-e61757cef6aa"),
//					UUID.fromString("fa1c9752-2edd-49b1-8e88-22c02a3e288c"), 12);

//			List<FacultyPeriodSubstitutionDetails> facultyPeriodSubstitutionDetailsList =
//					timetableManager.getFacultyPeriodSubstitutionDetails(10030, 10,
//					UUID.fromString("edafc3fc-d8c9-4ff5-9e98-f9089ae82098"), DayOfWeek.TUESDAY);
//			int instituteId,
//			int academicSessionId, UUID studentId, UUID standardId, Integer sectionId

//			timetableManager.getSchoolShiftConfigurationList(10030, 10);
//			System.out.println(facultyPeriodSubstitutionDetailsList);
//			final NoticeBoardManager noticeBoardManager = context.getBean(NoticeBoardManager.class);
//			System.out.println(noticeBoardManager.getNoticeDetails(10030, 0, 0, 5, NoticeBoardStatus.SAVED));
//			final CourseManager courseManager = context.getBean(CourseManager.class);
//			final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//
//			System.out.println(studentAdmissionManager
//					.checkStudentStandardChangeEligibility(10030, 10, UUID.fromString("97e42a32-cf2d-4c3a-ba66-b533b3e862d")));
//
//			final FeePaymentInsightManager feePaymentInsightManager = context.getBean(FeePaymentInsightManager.class);
////			FeePaymentReportGenerator feePaymentReportGenerator = context.getBean(FeePaymentReportGenerator.class);


//			/2.0/examination-reports/10030/exam/30f9ef42-8907-40e2-86b5-43a9dba65bc6?section_id=5&exclude_coscholastic_subjects=false&show_dimensions=false&show_total_column_dimension=false&display_rank=false

//			f86e5215-17ea-4fa7-9f47-a59e2bb0478a
//			a6874ed8-106a-4769-addc-ff3ccfddfce6
//			ExamReportGenerator examReportGenerator = context.getBean(ExamReportGenerator.class);
//			ReportOutput reportOutput = examReportGenerator.getClassExamReport(10030, UUID.fromString("e5c6af95-0465-41ff-b381-db8379da11d2"),
//					13, false, false, false, false);

//			ReportOutput reportOutput = examReportGenerator.getClassExamReport(10030, UUID.fromString("f86e5215-17ea-4fa7-9f47-a59e2bb0478a"),
//					5, false, false, false, false);

//			ReportOutput reportOutput = examReportGenerator.getClassExamReport(10030, UUID.fromString("a6874ed8-106a-4769-addc-ff3ccfddfce6"),
//					5, false, false, false, false);

//			feePaymentInsightManager.getStudentLedgerDetails(10030, 10, UUID.fromString("00a137b8-68e3-4004-81f1-8abf39354860"), null);
//			http://127.0.0.1:8000/97b8ac6d-1645-49f6-b9fa-389c3401a952/fees/generate-report/STUDENT_AGGREGATED_DUE_PAYMENT?academic_session_id=32&reportStartDate=-1&reportEndDate=-1&fee_ids=&requiredStandards=&feeDueDate=-1&includeEnrolledStudentsOnly=false&downloadFormat=
//			/2.0/fees/reports/10030/generate-report/STUDENT_AGGREGATED_DUE_PAYMENT?academic_session_id=32&start=-1&end=-1&fee_ids=&required_standards=&fee_due_date=-1&student_status=&user_id=4cb09c60-eabc-4192-a9d4-e357cf773db3&download_format=

//			ReportOutput reportOutput = feePaymentReportGenerator.generateReport(10030, 32, FeesReportType.STUDENT_AGGREGATED_DUE_PAYMENT,
//					-1, -1, -1, null, null, null, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//
//			ReportOutput reportOutput = feePaymentReportGenerator.generateReport(10030, 10, FeesReportType.STUDENT_LEDGER,
//					-1, -1, null, null, "00a137b8-68e3-4004-81f1-8abf39354860", null, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));
//			System.out.println(studentAdmissionManager
//					.checkStudentStandardChangeEligibility(10030, 10, UUID.fromString("97e42a32-cf2d-4c3a-ba66-b533b3e862d")));

//			/2.0/student/student-course-details/a7030783-72fd-4a85-9462-2f730a4daeb0/fd1e9835-1f1c-4d21-b193-38234522cc31?institute_id=10030&academic_session_id=10
//			System.out.println(courseManager.getCourseStudentAssignment(10030, 10, UUID.fromString("fd1e9835-1f1c-4d21-b193-38234522cc31"),
//					UUID.fromString("a7030783-72fd-4a85-9462-2f730a4daeb0")));
//
//			final UserManager usermanager = context.getBean(UserManager.class);
//			UserAuthenticationPayload userAuthenticationPayload = new UserAuthenticationPayload ("tamanna@embrate", "tamanna@embrate",
//					null);
//			System.out.println(usermanager.authenticate(userAuthenticationPayload, null, null));
//			final ExaminationManager examinationManager = context.getBean(ExaminationManager.class);
////			System.out.println(examinationManager.getExamDetails(UUID.fromString("e5c6af95-0465-41ff-b381-db8379da11d2"), 10030));
////			System.out.println(examinationManager.getExamCourses(10, UUID.fromString("fa1c9752-2edd-49b1-8e88-22c02a3e288c")));
//			System.out.println(examinationManager.getPublishExamDetailsByStudentId(10030, 10,
//					UUID.fromString("b6bbeeba-8be4-4ef1-b3f2-2a2a32f3549b")));
//			System.out.println(examinationManager.getSingleStudentExamMarksDetails(10030, 10, UUID.fromString("e5c6af95-0465-41ff-b381-db8379da11d2"),
//					UUID.fromString("b6bbeeba-8be4-4ef1-b3f2-2a2a32f3549b")).size());
//			examinationDao.getSingleStudentExamMarksDetails(instituteId, examId, studentId);
//			final FeePaymentInsightManager feePaymentInsightManager = context.getBean(FeePaymentInsightManager.class);
//			final UserManager userManager = context.getBean(UserManager.class);

			/* final UserPermissionManager userPermissionManager = context.getBean(UserPermissionManager.class);

            Set<AuthorisationRequiredAction> authorisationRequiredActionSet = new HashSet<>();
            authorisationRequiredActionSet.add(AuthorisationRequiredAction.PARENT_APPOINTMENT_ADMIN_ACCESS);
            authorisationRequiredActionSet.add(AuthorisationRequiredAction.ACCESS_ALL_CLASSES_STUDENT_LEAVE_DATA);
            List<User> adminUserList = userPermissionManager.getUserByUserPermission(101, authorisationRequiredActionSet);
            System.out.println(adminUserList); */
//			FeeReportDetailsGenerator feeReportDetailsGenerator = new FeeReportDetailsGenerator(feePaymentInsightManager,
//					userManager, userPermissionManager);

//			ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(10030, 0, FeesReportType.TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL,
//					1527791400, 1624991400, -1, null, null, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), DownloadFormat.EXCEL);
//
//			System.out.println(reportDetails.toString());
//			for(ReportSheetDetails reportSheetDetails : reportDetails.getReportSheetDetailsList()) {
//				for(List<ReportCellDetails> reportCellDetailsRow : reportSheetDetails.getReportCellDetails()) {
//					for(ReportCellDetails reportCellDetails : reportCellDetailsRow) {
//						System.out.print(reportCellDetails.getValue() + "  ");
//					}
//					System.out.println();
//				}
//			}
//	}
//			final FeePaymentInsightManager feePaymentInsightManager = context.getBean(FeePaymentInsightManager.class);
//			final UserManager userManager = context.getBean(UserManager.class);
//			final UserPermissionManager userPermissionManager = context.getBean(UserPermissionManager.class);

//			FeeReportDetailsGenerator feeReportDetailsGenerator = new FeeReportDetailsGenerator(feePaymentInsightManager,
//					userManager, userPermissionManager);

//			ReportDetails reportDetails = feeReportDetailsGenerator.generateReport(10030, 0, FeesReportType.TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL,
//					1527791400, 1624991400, -1, null, null, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"), DownloadFormat.EXCEL);

//			System.out.println(reportDetails.toString());
//			for(ReportSheetDetails reportSheetDetails : reportDetails.getReportSheetDetailsList()) {
//				for(List<ReportCellDetails> reportCellDetailsRow : reportSheetDetails.getReportCellDetails()) {
//					for(ReportCellDetails reportCellDetails : reportCellDetailsRow) {
//						System.out.print(reportCellDetails.getValue() + "  ");
//					}
//					System.out.println();
//				}
//			}

//			ExcelReportGenerator excelReportGenerator = new ExcelReportGenerator();
//			ReportOutput reportOutput = excelReportGenerator.generateReport(reportDetails);
//
//			final OutputStream outputStream = new FileOutputStream(
//					"/Users/<USER>/Lernen/outputPdfHTML/" + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);


//			final StudentAdmissionManager studentAdmissionManager = context.getBean(StudentAdmissionManager.class);
//			final LectureManager lectureManager = context.getBean(LectureManager.class);
//
//			System.out.println(
//					lectureManager.getStudentLectureStats(10030, UUID.fromString("140a207b-f1ed-4e7f-9f61-e61757cef6aa")));
//
//			140a207b-f1ed-4e7f-9f61-e61757cef6aa

//			int instituteId = 10030;
//			int academicSessionId = 10;
//			int nextSessionId = 32;
//			UUID standardId = UUID.fromString("a7030783-72fd-4a85-9462-2f730a4daeb0");
//			Integer sectionId = null;
//			List<UUID> promotingStudentList = new ArrayList<UUID>();
//			promotingStudentList.add(UUID.fromString("01b8dc01-d6fa-4ac0-a403-235a6f209064"));
//
//			List<UUID> feeStructureIdsList = new ArrayList<UUID>();
//			feeStructureIdsList.add(UUID.fromString("cf94254d-a4a3-481f-bd86-4f93064f3cf4"));
//			feeStructureIdsList.add(UUID.fromString("6ed900de-2295-4e70-876d-a877c495e60b"));
//
//			boolean carryForwardDiscount = true;
//
//			List<UUID> discountStructureIdsList = new ArrayList<UUID>();
//			discountStructureIdsList.add(UUID.fromString("67604de5-3314-454a-b203-1e6cfc667e80"));
//
//			boolean carryForwardTransport = false;
//
//			StudentPromotionDetails studentPromotionDetails = new StudentPromotionDetails(instituteId, academicSessionId,
//					nextSessionId, standardId, sectionId, promotingStudentList, feeStructureIdsList, carryForwardDiscount,
//					discountStructureIdsList, carryForwardTransport);
//			UUID userId = UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3");
//			boolean status = studentAdmissionManager.promoteStudents(instituteId, academicSessionId, studentPromotionDetails, userId);
//
//			System.out.println("final result = " + status);


//			final FeeDiscountConfigurationManager feeDiscountConfigurationManager = context.getBean(FeeDiscountConfigurationManager.class);

//			System.out.println(feeDiscountConfigurationManager.getDiscountStructureMetaDataForStandard(10030, 32, UUID.fromString("00a137b8-68e3-4004-81f1-8abf39354860")));

//			final StudentManager studentManager = context.getBean(StudentManager.class);
//
//
//			String sectionIdsStr = "";
//			Set<Integer> sectionIds = null;
//			if(!StringUtils.isBlank(sectionIdsStr)) {
//				sectionIds = new HashSet<>();
//				final String[] sectionTokens = sectionIdsStr.split(",");
//				for (final String sectionId : sectionTokens) {
//					sectionIds.add(Integer.parseInt(sectionId));
//				}
//			}
//
//			String rteStr = "";
//			Boolean rte = null;
//			if(!StringUtils.isBlank(rteStr)) {
//				rte = Boolean.valueOf(rteStr);
//			}
//
//			String categoryStr = "";
//			Set<UserCategory> categorys = null;
//			if(!StringUtils.isBlank(categoryStr)) {
//				categorys = new HashSet<>();
//				final String[] categoryTokens = categoryStr.split(",");
//				for (final String category : categoryTokens) {
//					categorys.add(UserCategory.getCategory(category));
//				}
//			}
//
//			String genderStr = "";
//			Set<Gender> genders = null;
//			if(!StringUtils.isBlank(genderStr)) {
//				genders = new HashSet<>();
//				final String[] genderTokens = genderStr.split(",");
//				for (final String gender : genderTokens) {
//					genders.add(Gender.getGender(gender));
//				}
//			}
//
//			String areaTypeStr = "";
//			Set<AreaType> areaTypes = null;
//			if(!StringUtils.isBlank(areaTypeStr)) {
//				areaTypes = new HashSet<>();
//				final String[] areaTypeTokens = areaTypeStr.split(",");
//				for (final String areaType : areaTypeTokens) {
//					areaTypes.add(AreaType.getAreaType(areaType));
//				}
//			}
//
//			String speciallyAbledStr = "";
//			Boolean speciallyAbled = null;
//			if(!StringUtils.isBlank(speciallyAbledStr)) {
//				speciallyAbled = Boolean.valueOf(speciallyAbledStr);
//			}
//
//			String bplStr = "";
//			Boolean bpl = null;
//			if(!StringUtils.isBlank(bplStr)) {
//				bpl = Boolean.valueOf(bplStr);
//			}
//
//			int instituteId = 10030;
//			int academicSessionId = 10;
//			UUID standardId = UUID.fromString("00a137b8-68e3-4004-81f1-8abf39354860");
//
//			final List<Student> studentList = studentManager.getStudentsWithAllAcademicSessionDetailWithFilter(
//					instituteId, academicSessionId, standardId, sectionIds, rte, categorys, genders, areaTypes, speciallyAbled, bpl);
//
//			if(CollectionUtils.isEmpty(studentList)) {
//				System.out.println("List is empty");
//			} else {
//				System.out.println("List is not empty");
//				System.out.println(studentList.size());
//				for(Student student : studentList) {
//					System.out.println(student);
//				}
//			}
//				}
//			}
//
//			String rteStr = "";
//			Boolean rte = null;
//			if(!StringUtils.isBlank(rteStr)) {
//				rte = Boolean.valueOf(rteStr);
//			}
//
//			String categoryStr = "";
//			Set<UserCategory> categorys = null;
//			if(!StringUtils.isBlank(categoryStr)) {
//				categorys = new HashSet<>();
//				final String[] categoryTokens = categoryStr.split(",");
//				for (final String category : categoryTokens) {
//					categorys.add(UserCategory.getCategory(category));
//				}
//			}
//				}
//			}
//
//			String rteStr = "";
//			Boolean rte = null;
//			if(!StringUtils.isBlank(rteStr)) {
//				rte = Boolean.valueOf(rteStr);
//			}
//
//			String categoryStr = "";
//			Set<UserCategory> categorys = null;
//			if(!StringUtils.isBlank(categoryStr)) {
//				categorys = new HashSet<>();
//				final String[] categoryTokens = categoryStr.split(",");
//				for (final String category : categoryTokens) {
//					categorys.add(UserCategory.getCategory(category));
//				}
//			}
//
//			String genderStr = "";
//			Set<Gender> genders = null;
//			if(!StringUtils.isBlank(genderStr)) {
//				genders = new HashSet<>();
//				final String[] genderTokens = genderStr.split(",");
//				for (final String gender : genderTokens) {
//					genders.add(Gender.getGender(gender));
//				}
//			}
//
//			String areaTypeStr = "";
//			Set<AreaType> areaTypes = null;
//			if(!StringUtils.isBlank(areaTypeStr)) {
//				areaTypes = new HashSet<>();
//				final String[] areaTypeTokens = areaTypeStr.split(",");
//				for (final String areaType : areaTypeTokens) {
//					areaTypes.add(AreaType.getAreaType(areaType));
//				}
//			}
//
//			String speciallyAbledStr = "";
//			Boolean speciallyAbled = null;
//			if(!StringUtils.isBlank(speciallyAbledStr)) {
//				speciallyAbled = Boolean.valueOf(speciallyAbledStr);
//			}
//
//			String bplStr = "";
//			Boolean bpl = null;
//			if(!StringUtils.isBlank(bplStr)) {
//				bpl = Boolean.valueOf(bplStr);
//			}
//
//			int instituteId = 10030;
//			int academicSessionId = 10;
//			UUID standardId = UUID.fromString("00a137b8-68e3-4004-81f1-8abf39354860");
//
//			final List<Student> studentList = studentManager.getStudentsWithAllAcademicSessionDetailWithFilter(
//					instituteId, academicSessionId, standardId, sectionIds, rte, categorys, genders, areaTypes, speciallyAbled, bpl);
//
//			if(CollectionUtils.isEmpty(studentList)) {
//				System.out.println("List is empty");
//			} else {
//				System.out.println("List is not empty");
//				System.out.println(studentList.size());
////				for(Student student : studentList) {
////					System.out.println(student);
////				}
//			}
//			final StaffManager staffManager = context.getBean(StaffManager.class);
//			System.out.println(staffManager.onboardStaff(UUID.fromString("2d1b2785-c2ac-4337-b44c-e4bfccaa0340"),
//					true, 10030, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3")));

//			List<UUID> category = new ArrayList<UUID>();
//			category.add(UUID.fromString("4fc0e4dc-a597-47cc-9675-e4d8ba067806"));
//			category.add(UUID.fromString("22a90ea5-1a09-489e-b345-d668ade9925f"));

//			List<UUID> department = new ArrayList<UUID>();
//			department.add(UUID.fromString("a7190fb3-6616-493a-9c6e-6109518eb4d1"));
//			department.add(UUID.fromString("e7da6caa-38cc-45bc-9f60-c9c1b2968f51"));
//			department.add(UUID.fromString("eb02a6b8-ed4e-423a-9345-b06e2f0e2137"));

//			final StaffManager staffManager = context.getBean(StaffManager.class);
////			System.out.println(staffManager.onboardStaff(UUID.fromString("2d1b2785-c2ac-4337-b44c-e4bfccaa0340"),
////					true, 10030, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3")));
//
//			List<UUID> category = new ArrayList<UUID>();
////			category.add(UUID.fromString("4fc0e4dc-a597-47cc-9675-e4d8ba067806"));
//			category.add(UUID.fromString("22a90ea5-1a09-489e-b345-d668ade9925f"));
//
//			List<UUID> department = new ArrayList<UUID>();
////			department.add(UUID.fromString("a7190fb3-6616-493a-9c6e-6109518eb4d1"));
//			department.add(UUID.fromString("e7da6caa-38cc-45bc-9f60-c9c1b2968f51"));
////			department.add(UUID.fromString("eb02a6b8-ed4e-423a-9345-b06e2f0e2137"));
//
//			List<UUID> designation = new ArrayList<UUID>();
//			designation.add(UUID.fromString("ee5bae57-7c82-47e0-99e6-66b726fbd3ef"));
//			designation.add(UUID.fromString("f3c49d33-a454-4c79-bc71-ae6a731c8465"));


//			final List<Staff> staff = staffManager.advanceSearchStaff(10030, "", StaffStatus.ONBOARD, "USER_PAGE", category,
//					department, designation);
//
//
//			final List<FullStaffDetails> staff = staffManager.getAdvanceStaffDetails(10030, StaffStatus.JOINER, "");
//
//			System.out.println(staff);
//			if(!CollectionUtils.isEmpty(staff)) {
//				System.out.println(staff.size());
//				System.out.println(staff.toString());
//			}


//			System.out.println(staffManager.getStaffStats(10030));

//			final AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
//			final AttendanceReportGenerator attendanceReportGenerator = context.getBean(AttendanceReportGenerator.class);
//			final AttendanceManager attendanceManager = context.getBean(AttendanceManager.class);

//			AttendanceReportType attendanceReportType = AttendanceReportType.getAttendanceReportType("ATTENDANCE_DETAILS_IN_A_DAY");
//
////			String standardIdStr = "00a137b8-68e3-4004-81f1-8abf39354860:3,00a137b8-68e3-4004-81f1-8abf39354860:4";
//			String standardIdStr = "";
//			String [] standardArr = standardIdStr.split(",");
//			List<UUID> standardIdList = new ArrayList<UUID>();
//			List<Integer> sectionIdList = new ArrayList<Integer>();
//			for(String standard : standardArr) {
//				String [] sectionArr = standard.split(":");
//				standardIdList.add(UUID.fromString(sectionArr[0]));
//				sectionIdList.add(Integer.parseInt(sectionArr[1]));
//
//			}
//
////			String attendanceStatusStr = "PRESENT,ABSENT,LEAVE";
//			String attendanceStatusStr = "";
//			List<AttendanceStatus> attendanceStatusList = new ArrayList<AttendanceStatus>();
//			String [] attendanceStatusArr = attendanceStatusStr.split(",");
//			for(String attendanceStatus : attendanceStatusArr) {
//				attendanceStatusList.add(AttendanceStatus.getAttendanceStatus((attendanceStatus)));
//			}
//
////			String attendanceTypeIdStr = "1,2";
//			String attendanceTypeIdStr = "";
//			List<Integer> attendanceTypeList = new ArrayList<Integer>();
//			String [] attendanceTypeArr = attendanceTypeIdStr.split(",");
//			for(String attendanceType : attendanceTypeArr) {
//				attendanceTypeList.add(Integer.parseInt(attendanceType));
//			}


//			AttendanceReportType attendanceReportType = AttendanceReportType.getAttendanceReportType("ATTENDANCE_DETAILS_IN_A_DAY");
//
//			String standardIdStr = "";
//			List<UUID> standardIdList = null;
//			List<Integer> sectionIdList = null;
//			if(!StringUtils.isBlank(standardIdStr)) {
//				standardIdList = new ArrayList<UUID>();
//				sectionIdList = new ArrayList<Integer>();
//				String [] standardArr = standardIdStr.split(",");
//				for(String standard : standardArr) {
//					String [] sectionArr = standard.split(":");
//					UUID standardId = UUID.fromString(sectionArr[0]);
//					if(!standardIdList.contains(standardId)) {
//						standardIdList.add(standardId);
//					}
//					sectionIdList.add(Integer.parseInt(sectionArr[1]));
//				}
//			}
//
//
//			String attendanceStatusStr = "";
//			List<AttendanceStatus> attendanceStatusList = null;
//			if(!StringUtils.isBlank(attendanceStatusStr)) {
//				attendanceStatusList = new ArrayList<AttendanceStatus>();
//				String [] attendanceStatusArr = attendanceStatusStr.split(",");
//				for(String attendanceStatus : attendanceStatusArr) {
//					attendanceStatusList.add(AttendanceStatus.getAttendanceStatus((attendanceStatus)));
//				}
//			}
//
//			String attendanceTypeIdStr = "";
//			List<Integer> attendanceTypeList = null;
//			if(!StringUtils.isBlank(attendanceStatusStr)) {
//				attendanceTypeList = new ArrayList<Integer>();
//				String [] attendanceTypeArr = attendanceTypeIdStr.split(",");
//				for(String attendanceType : attendanceTypeArr) {
//					attendanceTypeList.add(Integer.parseInt(attendanceType));
//				}
//			}
//
//			final ReportOutput reportOutput = attendanceReportGenerator.generateReport(10030, 10, attendanceReportType,
//					standardIdList, sectionIdList, attendanceStatusList, attendanceTypeList, 1612117800, 1614450600);
//
//			String attendanceTypeStr = "2";
//			String [] attendanceTypeStrArr =  attendanceTypeStr.split(",");
//			int[] attendanceType = new int[attendanceTypeStrArr.length];
//			for(int i = 0;i < attendanceTypeStrArr.length;i++) {
//				attendanceType[i] = Integer.parseInt(attendanceTypeStrArr[i]);
//			}
//
//			System.out.println(attendanceManager.getInstituteAttendanceTypes(10030, 10, attendanceType));

//			List<Integer> attendanceType = getStringtoIntArray("1,2");

//			String [] stringStrArr =  "1,2".split(",");
//			ArrayList<Integer> attendanceType = new ArrayList<Integer>();
//			for(int i = 0;i < stringStrArr.length;i++) {
//				attendanceType.add(Integer.parseInt(stringStrArr[i]));
//			}

//			System.out.println(attendanceManager.getAttendance(10030, 10, UUID.fromString("3d974988-2f39-403e-a0f8-fd83241a959d"),
//					7, attendanceType, 1613413800));

//			System.out.println(noticeBoardManager.getNoticeDetails(10030, 0, 0, 5, NoticeBoardStatus.SAVED));

//			System.out.println(noticeBoardManager.getNoticeDetails(10030, 1617474600, 0, 5, NoticeBoardStatus.SAVED));

//			feePaymentReportGenerator.generateReport(10030, 10,
//					FeesReportType.STUDENT_AGGREGATED_PAYMENT,
//					1, 1, -1, null, null, UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

//			StudentFeePaymentTransactionDetails feePaymentTransactionDetails = FeePaymentManager
//					.getStudentFeePaymentTransactionDetails(10030, UUID.fromString("e17ba6dd-938d-4864-a542-0d44d3138678"));
//
//			System.out.println(feePaymentTransactionDetails);
            // System.out.println(lectureManager.getStudentLectureDetails(10030,
            // UUID.fromString("5abf97d4-bcb1-4eff-bbc3-ea822507cfa9")));
            // final FeePaymentReportGenerator attendanceReportGenerator =
            // context.getBean(FeePaymentReportGenerator.class);

            // final ExamGreenSheetGenerator examGreenSheetCreation =
            // context.getBean(ExamGreenSheetGenerator.class);
            // final LectureManager lectureManager =
            // context.getBean(LectureManager.class);
            // // StudentReportsGenerator
            //
            // final String registrationToken =
            // "d4q1KcZF-zc:APA91bEo_OZa5fE6HB6VfluFNU1L3SCFd_2PQYw862KXarMKDRgJF1JqVEIZNW8Z6nYr9E95lKQNZgcTzUJW1SDsuBcjPJScCozVGvGyHxl0CL9h0Xg9c1CTY-DAfXT5q-Orm7f59XWh";
            //
            // final PushNotificationResponse pushNotificationResponse =
            // firebaseMessagingServiceHandler
            // .sendNotification(new PushNotificationPayload("New Lecture From
            // Bright Future",
            // "Watch new lecture of subject Hindi by Hemant Sir.", null,
            // registrationToken, null));
            // System.out.println(pushNotificationResponse.toString());
            // final App app = new App();
            // // final FileInputStream serviceAccount = new
            // FileInputStream("");
            //

//			int instituteId, ReportType reportType, int start, int end,
//			String inventoryTransactionType, UUID userId
//			final ReportGenerationManager reportGenerationManager = context.getBean(ReportGenerationManager.class);
//			final ReportOutput reportOutput = reportGenerationManager.generateReport(10030, ReportType.USER_LEVEL_DAY_WISE_REPORT,
//					**********, **********, "SALE", UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

//			final ReportOutput reportOutput = reportGenerationManager.generateReport(10030, ReportType.TRANSACTION,
//					**********, **********, "SALE", UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

            // attendanceReportGenerator.generateReport(10030, 10,
            // FeesReportType.TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL,
            // **********, **********, -1, "", "",
            // UUID.fromString("4cb09c60-eabc-4192-a9d4-e357cf773db3"));

            // .generateReport(10030, 10,
            // AttendanceReportType.ATTENDANCE_DETAILS_IN_A_DAY,
            // UUID.fromString("a7030783-72fd-4a85-9462-2f730a4daeb0"), 0, null,
            // 1, 1592764200);
//			final OutputStream outputStream = new FileOutputStream(
//					"/Users/<USER>/Lernen/outputPdfHTML/" + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);
////			final OutputStream outputStream = new FileOutputStream(
////					"/Users/<USER>/Lernen/outputPdfHTML/" + reportOutput.getReportName());
////			reportOutput.getReportContent().writeTo(outputStream);
//			 .generateReport(10030, 10,
//			 AttendanceReportType.ATTENDANCE_DETAILS_IN_A_DAY,
//			 UUID.fromString("a7030783-72fd-4a85-9462-2f730a4daeb0"), 0, null,
//			 1, 1592764200);
//			final OutputStream outputStream = new FileOutputStream(
//					"/Users/<USER>/Lernen/outputPdfHTML/" + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);

//		} catch (final FileNotFoundException ex) {
//			// TODO Auto-generated catch block
//			ex.printStackTrace();
//		} catch (final IOException e) {

//			// TODO Auto-generated catch block
            // TODO Auto-generated catch block

//			e.printStackTrace();
//		}
//			final OutputStream outputStream = new FileOutputStream(
//					"/Users/<USER>/Lernen/outputPdfHTML/" + reportOutput.getReportName());
//			reportOutput.getReportContent().writeTo(outputStream);
//		} catch (final FileNotFoundException ex) {
//			// TODO Auto-generated catch block
//			ex.printStackTrace();
        } catch (final Exception e) {
            e.printStackTrace();
        }
        // Gson gson = new GsonBuilder().serializeNulls().create();
        // System.out.println(gson.toJson(new StudentLectureDetails(0, null,
        // Arrays.asList(new CoursesLectureDetails(null,
        // Arrays.asList(new LectureAndViewDetails(new LectureDetails(),
        // Arrays.asList(new StudentLectureViewDetails()))))))));

        // Gson gson = new GsonBuilder().serializeNulls().create();
        // gson.toJson(new DiscussionDetails(0, new HashMap<UUID,
        // ChannelThreadDetails>().put(null, new ChannelThreadDetails(null,
        // new HashMap<UUID, ThreadConversationDetails>()))));

        // System.out.println(gson.toJson(new StudentLectureDetails(0, null,
        // Arrays.asList(new CoursesLectureDetails(null,
        // Arrays.asList(new LectureDetails(null)))))));

        // final Gson gson = new GsonBuilder().serializeNulls().create();
        // System.out.println(gson.toJson(new ChannelDetails(0, new
        // ChannelMetaData(), Arrays.asList(new ThreadMetaData()))));
        // System.out.println(gson.toJson(new ThreadConversationDetails(0, new
        // ThreadMetaData(), Arrays.asList(new ConversationDetails()))));

        // System.out.println(lectureManager.getStudentLectureDetails(10030,
        // UUID.fromString("5abf97d4-bcb1-4eff-bbc3-ea822507cfa9")));
    }
    // public static void main(String[] args) {
    // try {
    // final ApplicationContext context = new
    // ClassPathXmlApplicationContext("core-lib.xml");
    // // final LectureManager lectureManager =
    // // context.getBean(LectureManager.class);
    // // System.out.println(lectureManager.getStudentLectureDetails(10030,
    // // UUID.fromString("5abf97d4-bcb1-4eff-bbc3-ea822507cfa9")));
    // final AttendanceReportGenerator attendanceReportGenerator = context
    // .getBean(AttendanceReportGenerator.class);
    //
    // // final ExamGreenSheetGenerator examGreenSheetCreation =
    // // context.getBean(ExamGreenSheetGenerator.class);
    // // final LectureManager lectureManager =
    // // context.getBean(LectureManager.class);
    // // // StudentReportsGenerator
    // //
    // // // final ReportOutput reportOutput =
    // // // examReportGenerator.getClassExamReport(10001,
    // // // UUID.fromString("17f0af7c-6b68-4e7c-8fcf-797368422b1f"), null,
    // // true,
    // // // false, false);
    // // try {
    // // // final ReportOutput reportOutput =
    // // // examGreenSheetCreation.generateGreenSheet(10030, 10,
    // // // UUID.fromString("3d974988-2f39-403e-a0f8-fd83241a959d"));
    // // // final ReportOutput reportOutput =
    // // // studentReportsGenerator.generateCastWiseStudentReport(10030,
    // // 10);
    // //
    // //
    // final ReportOutput reportOutput =
    // attendanceReportGenerator.generateReport(10030, 10,
    // AttendanceReportType.ATTENDANCE_DETAILS_IN_A_DAY,
    // UUID.fromString("a7030783-72fd-4a85-9462-2f730a4daeb0"), 0, null, 1,
    // 1592764200);
    // final OutputStream outputStream = new FileOutputStream(
    // "/Users/<USER>/Lernen/outputPdfHTML/" +
    // reportOutput.getReportName());
    // reportOutput.getReportContent().writeTo(outputStream);
    // } catch (final FileNotFoundException ex) {
    // // TODO Auto-generated catch block
    // ex.printStackTrace();
    // } catch (final IOException e) {
    // // TODO Auto-generated catch block
    // e.printStackTrace();
    // }
    //
    // // Gson gson = new GsonBuilder().serializeNulls().create();
    // // System.out.println(gson.toJson(new StudentLectureDetails(0, null,
    // // Arrays.asList(new CoursesLectureDetails(null,
    // // Arrays.asList(new LectureAndViewDetails(new LectureDetails(),
    // // Arrays.asList(new StudentLectureViewDetails()))))))));
    //
    // // Gson gson = new GsonBuilder().serializeNulls().create();
    // // gson.toJson(new DiscussionDetails(0, new HashMap<UUID,
    // // ChannelThreadDetails>().put(null, new ChannelThreadDetails(null,
    // // new HashMap<UUID, ThreadConversationDetails>()))));
    //
    // // System.out.println(gson.toJson(new StudentLectureDetails(0, null,
    // // Arrays.asList(new CoursesLectureDetails(null,
    // // Arrays.asList(new LectureDetails(null)))))));
    //
    // // final Gson gson = new GsonBuilder().serializeNulls().create();
    // // System.out.println(gson.toJson(new ChannelDetails(0, new
    // // ChannelMetaData(), Arrays.asList(new ThreadMetaData()))));
    // // System.out.println(gson.toJson(new ThreadConversationDetails(0, new
    // // ThreadMetaData(), Arrays.asList(new ConversationDetails()))));
    //
    // // System.out.println(lectureManager.getStudentLectureDetails(10030,
    // // UUID.fromString("5abf97d4-bcb1-4eff-bbc3-ea822507cfa9")));
    //
    // }

//	public List<Integer> getStringtoIntArray(String string) {
//		if(StringUtils.isBlank(string)) {
//			return null;
//		}
//		String [] stringStrArr =  string.split(",");
//		ArrayList<Integer> intArr = new ArrayList<Integer>();
//		for(int i = 0;i < stringStrArr.length;i++) {
//			intArr.add(Integer.parseInt(stringStrArr[i]));
//		}
//		return intArr;
//	}
}