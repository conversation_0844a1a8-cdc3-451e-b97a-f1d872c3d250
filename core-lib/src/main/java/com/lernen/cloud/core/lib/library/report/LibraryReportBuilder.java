package com.lernen.cloud.core.lib.library.report;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.lernen.cloud.core.lib.reports.ReportGenerator.BLACK_COLOR;
import static com.lernen.cloud.core.lib.reports.ReportGenerator.CONTENT_SIZE;
import static com.lernen.cloud.core.lib.reports.ReportGenerator.STRING;
import static com.lernen.cloud.core.lib.reports.ReportGenerator.WHITE_COLOR;

import com.embrate.cloud.core.api.report.layout.MergeArea;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.lernen.cloud.core.api.library.IndividualBookDetails;
import com.lernen.cloud.core.api.library.report.LibraryReportDetailedData;
import com.lernen.cloud.core.api.library.report.LibraryReportPayload;
import com.lernen.cloud.core.api.library.report.LibraryReportStore;
import com.lernen.cloud.core.api.library.report.LibraryReportSummaryData;
import com.lernen.cloud.core.api.library.report.LibraryReportType;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportHorizontalTextAlignment;
import com.lernen.cloud.core.lib.managers.LibraryManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.user.Module;
import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;

import java.util.Collections;

/**
 * Report builder for library summary reports (without accession details)
 */
public class LibraryReportBuilder extends ReportBuilder<LibraryReportPayload, LibraryReportStore> {

    private final LibraryManager libraryManager;

    public LibraryReportBuilder(UserPermissionManager userPermissionManager,
                                      InstituteManager instituteManager,
                                      LibraryManager libraryManager) {
        super(userPermissionManager, instituteManager);
        this.libraryManager = libraryManager;
    }

    @Override
    public LibraryReportStore getStore(LibraryReportPayload payload) {

      List<LibraryReportSummaryData> summaryData = null;
      List<LibraryReportDetailedData> detailedData = null;
      
      switch(payload.getReportType()){
        case LIBRARY_SUMMARY_REPORT:
            summaryData = libraryManager.getLibrarySummaryReportData(
            payload.getInstituteId(), payload.getStartDate(), payload.getEndDate(),
            payload.getGenreIds(), payload.getPublicationIds(), payload.getPublisherIds(),
            payload.getAuthorIds(), payload.getLibraryTypeIds());
            break;
        case LIBRARY_DETAILED_REPORT:
            detailedData = libraryManager.getLibraryDetailedReportData(
            payload.getInstituteId(), payload.getStartDate(), payload.getEndDate(),
            payload.getGenreIds(), payload.getPublicationIds(), payload.getPublisherIds(),
            payload.getAuthorIds(), payload.getLibraryTypeIds());
            break;
        default:
            break;
      }

        String dateRange = DateUtils.buildDateRangeString(payload.getStartDate(), payload.getEndDate());
        String appliedFilters = buildAppliedFiltersString(payload);

        return new LibraryReportStore(payload.isShowAccessionDetails(), dateRange, summaryData, detailedData,  appliedFilters);
    }

    @Override
    public ReportHeader getHeader(LibraryReportPayload payload, LibraryReportStore store) {
        List<ReportRow> headerRows = new ArrayList<>();
        List<MergeArea> mergeAreaList = new ArrayList<>();

        // Total columns count (matches the column headers)
        int totalCols = getStaticHeaders(payload.getReportType()).size(); 

        // Title row
        ReportRow titleRow = new ReportRow();
        addColumns(titleRow, 11, EColorUtils.BLACK_COLOR_HEX_CODE, true, ReportHorizontalTextAlignment.CENTER, payload.getReportType().getDisplayName());
        // Fill remaining columns with empty cells
        for (int i = 1; i < totalCols; i++) {
            addColumns(titleRow, "");
        }
        headerRows.add(titleRow);
        mergeAreaList.add(new MergeArea(headerRows.size() - 1, headerRows.size() - 1, 0, totalCols - 1));

        // Date range row (if applicable)
        if (store.getDateRange() != null) {
            ReportRow dateRow = new ReportRow();
            addColumns(dateRow, "Date Range: " + store.getDateRange());
            // Fill remaining columns with empty cells
            for (int i = 1; i < totalCols; i++) {
                addColumns(dateRow, "");
            }
            headerRows.add(dateRow);
            mergeAreaList.add(new MergeArea(headerRows.size() - 1, headerRows.size() - 1, 0, totalCols - 1));
        }

        // Applied filters row (if any)
        if (store.getAppliedFilters() != null) {
            ReportRow filtersRow = new ReportRow();
            addColumns(filtersRow, "Applied Filters: " + store.getAppliedFilters());
            // Fill remaining columns with empty cells
            for (int i = 1; i < totalCols; i++) {
                addColumns(filtersRow, "");
            }
            headerRows.add(filtersRow);
            mergeAreaList.add(new MergeArea(headerRows.size() - 1, headerRows.size() - 1, 0, totalCols - 1));
        }

        List<ReportCellDetails> headerRow = new ArrayList<>();
        for (ReportHeaderAttribute headerAttribute : getStaticHeaders(payload.getReportType())) {
            headerRow.add(new ReportCellDetails(headerAttribute.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
        }

        headerRows.add(new ReportRow(headerRow));

        return new ReportHeader(headerRows, mergeAreaList);
    }

    @Override
    public ReportBody getBody(LibraryReportPayload payload, LibraryReportStore store) {
        List<ReportRow> rows = new ArrayList<>();
        List<MergeArea> mergeAreaList = new ArrayList<>();
        int serialNumber = 1;

        if (store.getSummaryData() != null) {
            for (LibraryReportSummaryData bookData : store.getSummaryData()) {
                if (bookData != null) {
                    ReportRow row = new ReportRow();

                    addColumns(row,
                        String.valueOf(serialNumber),
                        bookData.getBookTitle() != null ? bookData.getBookTitle() : "",
                        bookData.getIsbn() != null ? bookData.getIsbn() : "",
                        bookData.getAuthor() != null ? bookData.getAuthor() : "",
                        bookData.getGenre() != null ? bookData.getGenre() : "",
                        bookData.getPublication() != null ? bookData.getPublication() : "",
                        bookData.getPublisher() != null ? bookData.getPublisher() : "",
                        bookData.getEdition() != null ? bookData.getEdition() : "",
                        String.valueOf(bookData.getPublishYear()),
                        bookData.getLanguage() != null ? bookData.getLanguage() : "",
                        bookData.getAddedAt() != null ? DateUtils.getFormattedDate(bookData.getAddedAt()) : "",
                        bookData.getTotalCopies() != null ? String.valueOf(bookData.getTotalCopies()) : "",
                        bookData.getLibraryTypeName() != null ? bookData.getLibraryTypeName() : ""
                       );

                    rows.add(row);
                    serialNumber++;
                }
            }
        }

        if (store.getDetailedData() != null) {
          for (LibraryReportDetailedData bookData : store.getDetailedData()) {
              // If no individual book details, show book-level row with empty accession fields
              if (bookData.getIndividualBookDetails().isEmpty()) {
                  ReportRow row = createBookRow(serialNumber, bookData, null);
                  rows.add(row);
                  serialNumber++;
              } else {
                  // Show one row per individual book detail
                  for (IndividualBookDetails individualBook : bookData.getIndividualBookDetails()) {
                      ReportRow row = createBookRow(serialNumber, bookData, individualBook);
                      rows.add(row);
                      serialNumber++;
                  }
              }
          }
        }

        return new ReportBody(rows, mergeAreaList);
    }

    private ReportRow createBookRow(int serialNumber, LibraryReportDetailedData bookData, 
                                   IndividualBookDetails individualBook) {
        ReportRow row = new ReportRow();
        
        // Book-level information
        addColumns(row,
            String.valueOf(serialNumber),
            bookData.getBookTitle(),
            bookData.getIsbn(),
            bookData.getAuthor() != null ? bookData.getAuthor() : "",
            bookData.getGenre() != null ? bookData.getGenre() : "",
            bookData.getPublication() != null ? bookData.getPublication() : "",
            bookData.getPublisher() != null ? bookData.getPublisher() : "",
            bookData.getEdition(),
            String.valueOf(bookData.getPublishYear()),
            bookData.getAddedAt() != null ? DateUtils.getFormattedDate(bookData.getAddedAt()) : "",
            bookData.getLanguage(),
            String.valueOf(bookData.getTotalCopies())
        );

        // Individual book details (accession-level information)
        if (individualBook != null) {
            addColumns(row,
                individualBook.getAccessionNumber(),
                individualBook.getRack(),
                individualBook.getBillNumber(),
                individualBook.getStatus() != null ? individualBook.getStatus().getDisplayName() : "",
                individualBook.getVolume(),
                individualBook.getLibraryType() != null ? individualBook.getLibraryType().getLibraryName() : "",
                individualBook.getVendor() != null ? individualBook.getVendor().getVendorName() : "",
                individualBook.getRemarks());
        } else {
            // Empty accession-level columns
            addColumns(row, "", "", "", "", "", "", "", "", "");
        }

        return row;
    }


    @Override
    public String getDownloadReportName(LibraryReportPayload payload, LibraryReportStore store) {
        if(payload.getReportType() == LibraryReportType.LIBRARY_DETAILED_REPORT){
            return "Library_Detailed_Report";
        }
        return "Library_Summary_Report";
    }

    private String buildAppliedFiltersString(LibraryReportPayload payload) {
        List<String> filters = new ArrayList<>();
        
        if (payload.getGenreIds() != null && !payload.getGenreIds().isEmpty()) {
            filters.add("Genres (" + payload.getGenreIds().size() + ")");
        }
        if (payload.getPublicationIds() != null && !payload.getPublicationIds().isEmpty()) {
            filters.add("Publications (" + payload.getPublicationIds().size() + ")");
        }
        if (payload.getPublisherIds() != null && !payload.getPublisherIds().isEmpty()) {
            filters.add("Publishers (" + payload.getPublisherIds().size() + ")");
        }
        if (payload.getAuthorIds() != null && !payload.getAuthorIds().isEmpty()) {
            filters.add("Authors (" + payload.getAuthorIds().size() + ")");
        }
        if (payload.getLibraryTypeIds() != null && !payload.getLibraryTypeIds().isEmpty()) {
            filters.add("Library Types (" + payload.getLibraryTypeIds().size() + ")");
        }

        return filters.isEmpty() ? null : String.join(", ", filters);
    }

    @Override
    public ReportMetadata getReportMetadata() {
        return new ReportMetadata("LIBRARY_REPORT", "Library Report", 
                             Module.LIBRARY_MANAGEMENT, "Unified library report builder");
    }

    @Override
    public List<ReportHeaderAttribute> getStaticHeaders() {
        return null;
    }

    public List<ReportHeaderAttribute> getStaticHeaders(LibraryReportType libraryType) {
      if(libraryType == LibraryReportType.LIBRARY_DETAILED_REPORT){
        return Arrays.asList(
                ReportHeaderAttribute.LIBRARY_SERIAL_NUMBER, 
                ReportHeaderAttribute.LIBRARY_BOOK_TITLE,
                ReportHeaderAttribute.LIBRARY_ISBN,
                ReportHeaderAttribute.LIBRARY_AUTHOR, 
                ReportHeaderAttribute.LIBRARY_GENRE, 
                ReportHeaderAttribute.LIBRARY_PUBLICATION,
                ReportHeaderAttribute.LIBRARY_PUBLISHER, 
                ReportHeaderAttribute.LIBRARY_EDITION,
                ReportHeaderAttribute.LIBRARY_PUBLISH_YEAR, 
                ReportHeaderAttribute.LIBRARY_ADDED_AT, 
                ReportHeaderAttribute.LIBRARY_LANGUAGE,
                ReportHeaderAttribute.LIBRARY_TOTAL_COPIES,
                ReportHeaderAttribute.LIBRARY_ACCESSION_NUMBER,
                ReportHeaderAttribute.LIBRARY_RACK, 
                ReportHeaderAttribute.LIBRARY_BILL_NUMBER,
                ReportHeaderAttribute.LIBRARY_STATUS, 
                ReportHeaderAttribute.LIBRARY_VOLUME,
                ReportHeaderAttribute.LIBRARY_LIBRARY_TYPE, 
                ReportHeaderAttribute.LIBRARY_VENDOR, 
                ReportHeaderAttribute.LIBRARY_REMARK
        );
      } 

      return Arrays.asList(
                ReportHeaderAttribute.LIBRARY_SERIAL_NUMBER, 
                ReportHeaderAttribute.LIBRARY_BOOK_TITLE,
                ReportHeaderAttribute.LIBRARY_ISBN,
                ReportHeaderAttribute.LIBRARY_AUTHOR, 
                ReportHeaderAttribute.LIBRARY_GENRE, 
                ReportHeaderAttribute.LIBRARY_PUBLICATION,
                ReportHeaderAttribute.LIBRARY_PUBLISHER, 
                ReportHeaderAttribute.LIBRARY_EDITION,
                ReportHeaderAttribute.LIBRARY_PUBLISH_YEAR, 
                ReportHeaderAttribute.LIBRARY_LANGUAGE,
                ReportHeaderAttribute.LIBRARY_ADDED_AT, 
                ReportHeaderAttribute.LIBRARY_TOTAL_COPIES,
                ReportHeaderAttribute.LIBRARY_LIBRARY_TYPE
        );
    }


    @Override
    public void payloadVerification(LibraryReportPayload payload) {
        if (payload.getInstituteId() <= 0) {
            throw new RuntimeException("Invalid institute ID");
        }
        if (payload.getUserId() == null) {
            throw new RuntimeException("Invalid user ID");
        }
    }

    @Override
    public ReportConfigs getConfigs(LibraryReportPayload payload, LibraryReportStore store) {
        return new ReportConfigs(); // Default configs
    }

    @Override
    public void verifyAuthorisation(LibraryReportPayload payload) {
        userPermissionManager.verifyAuthorisation(payload.getInstituteId(), payload.getUserId(), AuthorisationRequiredAction.GENERATE_LIBRARY_BOOKS_REPORT);
    }
}
