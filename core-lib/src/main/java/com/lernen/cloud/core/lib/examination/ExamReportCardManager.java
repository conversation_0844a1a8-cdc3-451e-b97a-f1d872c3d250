package com.lernen.cloud.core.lib.examination;

import com.embrate.cloud.core.api.examination.utility.ExamGridRowAttribute;
import com.embrate.cloud.core.api.student.management.StudentManagementFieldDetails;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.configurations.ExaminationPreferences;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.examination.report.*;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.embrate.cloud.core.lib.courses.CourseManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.utils.student.StudentDataUtils;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.*;
import com.lernen.cloud.core.utils.examination.ExamReportCardConfigurationUtils;
import com.lernen.cloud.core.utils.student.StudentSorter;
import com.lernen.cloud.dao.tier.examination.ExaminationDao;
import com.lernen.clould.core.lib.examination.rankrule.IRankCalculator;
import com.lernen.clould.core.lib.examination.rankrule.RankCalculatorFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
public class ExamReportCardManager {
    private static final Logger logger = LogManager.getLogger(ExamReportCardManager.class);
    private static final double PASSING_THRESHOLD = 0.33d;
    private static final String[] ANNUAL_RESULT_REPORT_HEADER = {"S.No", "Admission Number", "Student Name",
            "Father's Name", "Class", "Total Obtained Marks", "Total Max Marks", "Percentage", "Grade"};
    private final ExaminationDao examinationDao;
    private final CourseManager courseManager;
    private final ExaminationManager examinationManager;
    private final StudentManager studentManager;
    private final InstituteManager instituteManager;
    private final UserPermissionManager userPermissionManager;

    private final UserPreferenceSettings userPreferenceSettings;
    private final RankCalculatorFactory rankCalculatorFactory;
    private final ExamResultCalculatorFactory examResultCalculatorFactory = new ExamResultCalculatorFactory();


    public ExamReportCardManager(ExaminationDao examinationDao, CourseManager courseManager,
                                 ExaminationManager examinationManager, StudentManager studentManager, InstituteManager instituteManager,
                                 UserPermissionManager userPermissionManager, UserPreferenceSettings userPreferenceSettings, RankCalculatorFactory rankCalculatorFactory) {
        this.examinationDao = examinationDao;
        this.courseManager = courseManager;
        this.examinationManager = examinationManager;
        this.studentManager = studentManager;
        this.instituteManager = instituteManager;
        this.userPermissionManager = userPermissionManager;
        this.userPreferenceSettings = userPreferenceSettings;
        this.rankCalculatorFactory = rankCalculatorFactory;
    }

    public boolean updateExamReportCardConfiguration(int instituteId,
                                                     ExamReportCardConfiguration examReportCardConfiguration) {
        // TODO: Validate structure before adding. Check things like , exam node
        // should always be leaf node
        return examinationDao.updateExamReportStructure(instituteId, examReportCardConfiguration);
    }

    public ExamReportCardConfiguration getExamReportCardConfiguration(int instituteId, int academicSessionId,
                                                                      UUID standardId, String reportType) {
        return examinationDao.getExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);
    }

    public boolean deleteExamReportCardConfiguration(int instituteId, int academicSessionId, UUID standardId, String reportType) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid institute id."));
        }
        if (academicSessionId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid academic session id."));
        }
        if (standardId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid standard Id."));
        }
        if (StringUtils.isBlank(reportType)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                    "Invalid report type."));
        }
        return examinationDao.deleteExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);
    }

    public Map<UUID, ExamReportCardConfiguration> getExamReportCardConfigurationMap(int instituteId, int academicSessionId,
                                                                                    String reportType) {
        List<ExamReportCardConfiguration> examReportCardConfigurationList =
                examinationDao.getExamReportCardConfigurationList(instituteId, academicSessionId, reportType);
        if(CollectionUtils.isEmpty(examReportCardConfigurationList)) {
            return null;
        }
        Map<UUID, ExamReportCardConfiguration> examReportCardConfigurationMap = new HashMap<>();
        for(ExamReportCardConfiguration examReportCardConfiguration : examReportCardConfigurationList) {
            if(examReportCardConfiguration == null) {
                continue;
            }
            UUID standardId = examReportCardConfiguration.getExamReportCardMetadata().getStandardId();
            if(standardId == null) {
                continue;
            }
            if(!examReportCardConfigurationMap.containsKey(standardId)) {
                examReportCardConfigurationMap.put(standardId, examReportCardConfiguration);
            }
        }
        return examReportCardConfigurationMap;
    }

    public boolean updateExamReportCardConfiguration(int instituteId,
                                                     ExamReportCardConfigurationPayload examReportCardConfigurationPayload) {

        final ExamReportStructurePayload examReportCardStructurePayload = examReportCardConfigurationPayload
                .getExamReportStructurePayload();

        final List<Course> courses = courseManager.getClassCoursesByStandardId(instituteId,
                examReportCardConfigurationPayload.getExamReportCardMetadata().getStandardId(),
                examReportCardConfigurationPayload.getExamReportCardMetadata().getAcademicSessionId());
        final List<ExamDetails> examDetailsList = examinationManager.getExamDetails(instituteId,
                examReportCardConfigurationPayload.getExamReportCardMetadata().getAcademicSessionId(),
                examReportCardConfigurationPayload.getExamReportCardMetadata().getStandardId());
        final List<ExamDimension> examDimensions = examinationManager.getExamDimensions(instituteId);

        final Map<String, UUID> courseNameIdMap = new HashMap<>();
        final Map<String, UUID> examNameIdMap = new HashMap<>();
        final Map<String, Integer> examDimensionNameIdMap = new HashMap<>();

        populateMapping(courses, examDetailsList, examDimensions, courseNameIdMap, examNameIdMap,
                examDimensionNameIdMap);

        final ExamReportCardConfiguration examReportCardConfiguration = new ExamReportCardConfiguration();
        examReportCardConfiguration
                .setExamReportCardMetadata(examReportCardConfigurationPayload.getExamReportCardMetadata());

        final ExamReportStructure examReportStructure = new ExamReportStructure();
        if(examReportCardStructurePayload == null) {
            examReportCardConfiguration.setExamReportStructure(examReportStructure);
            return updateExamReportCardConfiguration(instituteId, examReportCardConfiguration);
        }
        examReportStructure
                .setExamReportStructureMetaData(examReportCardStructurePayload.getExamReportStructureMetaData());

        if (examReportCardStructurePayload.getExamReportResultConfigs() != null) {
            ExamReportResultConfigs examReportResultConfigs = new ExamReportResultConfigs();
            examReportResultConfigs.setResultCalculatorId(examReportCardStructurePayload.getExamReportResultConfigs().getResultCalculatorId());

            if (!CollectionUtils.isEmpty(examReportCardStructurePayload.getExamReportResultConfigs().getResultComputationGroupList())) {
                List<ExamResultComputationGroup> examResultComputationGroups = new ArrayList<>();
                for (ExamResultComputationGroupPayload examResultComputationGroupPayload :
                        examReportCardStructurePayload.getExamReportResultConfigs().getResultComputationGroupList()) {
                    List<ExamResultComputationEntry> examResultComputationEntries = new ArrayList<>();
                    for (ExamResultComputationEntryPayload examResultComputationEntryPayload : examResultComputationGroupPayload.getResultComputationEntries()) {
                        String examName = examResultComputationEntryPayload.getExamName();
                        UUID examId = examNameIdMap.get(examName.trim().toLowerCase());
                        if (examId == null) {
                            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                    examName + " exam not found in database"));
                        }

                        Set<Integer> dimensions = new HashSet<>();
                        for (String dimensionName : examResultComputationEntryPayload.getDimensions()) {
                            Integer dimensionId = examDimensionNameIdMap.get(dimensionName.trim().toLowerCase());
                            if (dimensionId == null) {
                                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                        dimensionName + " dimension not found in database"));
                            }
                            dimensions.add(dimensionId);
                        }
                        examResultComputationEntries.add(new ExamResultComputationEntry(examId, examResultComputationEntryPayload.isTotalDimension(), dimensions));
                    }
                    examResultComputationGroups.add(new ExamResultComputationGroup(examResultComputationGroupPayload.getGroupId(), examResultComputationEntries));
                }
                examReportResultConfigs.setResultComputationGroupList(examResultComputationGroups);
            }
            examReportStructure.setExamReportResultConfigs(examReportResultConfigs);
        }
        if (!CollectionUtils.isEmpty(examReportCardStructurePayload.getExamReportCourseStructure())) {
            final Map<CourseType, ExamReportCourseStructure> examReportCourseStructureMap = createExamReportCourseStructure(
                    examReportCardStructurePayload, courseNameIdMap, examNameIdMap);
            examReportStructure.setExamReportCourseStructure(examReportCourseStructureMap);
        }

        if (!CollectionUtils.isEmpty(examReportCardStructurePayload.getExamReportHeaderStructureColumns())) {
            final Map<CourseType, List<ExamReportHeaderStructureColumn>> examReportHeaderStructureColumnMap = new HashMap<>();

            for (final Entry<CourseType, List<ExamReportHeaderStructureColumnPayload>> entry : examReportCardStructurePayload
                    .getExamReportHeaderStructureColumns().entrySet()) {

                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }

                final List<ExamReportHeaderStructureColumn> examReportHeaderStructureColumns = new ArrayList<>();
                for (final ExamReportHeaderStructureColumnPayload examReportHeaderStructureColumnPayload : entry
                        .getValue()) {
                    examReportHeaderStructureColumns.add(getExamReportHeaderStructureColumn(
                            examReportHeaderStructureColumnPayload, examNameIdMap, examDimensionNameIdMap));
                }

                examReportHeaderStructureColumnMap.put(entry.getKey(), examReportHeaderStructureColumns);
            }

            examReportStructure.setExamReportHeaderStructureColumns(examReportHeaderStructureColumnMap);
        }
        if (!CollectionUtils.isEmpty(examReportCardStructurePayload.getExamReportAdditionalSubjectHeaderStructureColumns())) {
            final Map<CourseType, List<ExamReportHeaderStructureColumn>> examReportHeaderStructureColumnMap = new HashMap<>();

            for (final Entry<CourseType, List<ExamReportHeaderStructureColumnPayload>> entry : examReportCardStructurePayload
                    .getExamReportAdditionalSubjectHeaderStructureColumns().entrySet()) {

                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }

                final List<ExamReportHeaderStructureColumn> examReportHeaderStructureColumns = new ArrayList<>();
                for (final ExamReportHeaderStructureColumnPayload examReportHeaderStructureColumnPayload : entry
                        .getValue()) {
                    examReportHeaderStructureColumns.add(getExamReportHeaderStructureColumn(
                            examReportHeaderStructureColumnPayload, examNameIdMap, examDimensionNameIdMap));
                }

                examReportHeaderStructureColumnMap.put(entry.getKey(), examReportHeaderStructureColumns);
            }

            examReportStructure.setExamReportAdditionalSubjectHeaderStructureColumns(examReportHeaderStructureColumnMap);
        }

        examReportCardConfiguration.setExamReportStructure(examReportStructure);
        return updateExamReportCardConfiguration(instituteId, examReportCardConfiguration);
    }

    public ExamReportCardConfigurationPayload getReadableExamReportCardConfiguration(int instituteId,
                                                                                     int academicSessionId, UUID standardId, String reportType) {
        final ExamReportCardConfiguration examReportCardConfiguration = examinationDao
                .getExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);

        final ExamReportStructure examReportStructure = examReportCardConfiguration.getExamReportStructure();

        final List<Course> courses = courseManager.getClassCoursesByStandardId(instituteId, standardId,
                academicSessionId);
        final List<ExamDetails> examDetailsList = examinationManager.getExamDetails(instituteId, academicSessionId,
                standardId);
        final List<ExamDimension> examDimensions = examinationManager.getExamDimensions(instituteId);

        final Map<UUID, String> courseIdNameMap = new HashMap<>();
        final Map<UUID, String> examIdNameMap = new HashMap<>();
        final Map<Integer, String> examDimensionIdNameMap = new HashMap<>();

        populateMap(courses, examDetailsList, examDimensions, courseIdNameMap, examIdNameMap, examDimensionIdNameMap);

        final ExamReportCardConfigurationPayload examReportCardConfigurationPayload = new ExamReportCardConfigurationPayload();
        examReportCardConfigurationPayload
                .setExamReportCardMetadata(examReportCardConfiguration.getExamReportCardMetadata());

        final ExamReportStructurePayload examReportStructurePayload = new ExamReportStructurePayload();
        examReportStructurePayload.setExamReportStructureMetaData(examReportStructure.getExamReportStructureMetaData());

        if (examReportStructure.getExamReportResultConfigs() != null) {
            ExamReportResultConfigsPayload examReportResultConfigsPayload = new ExamReportResultConfigsPayload();
            examReportResultConfigsPayload.setResultCalculatorId(examReportStructure.getExamReportResultConfigs().getResultCalculatorId());
            if (!CollectionUtils.isEmpty(examReportStructure.getExamReportResultConfigs().getResultComputationGroupList())) {
                List<ExamResultComputationGroupPayload> examResultComputationGroupPayloads = new ArrayList<>();
                for (ExamResultComputationGroup examResultComputationGroup : examReportStructure.getExamReportResultConfigs().getResultComputationGroupList()) {
                    List<ExamResultComputationEntryPayload> examResultComputationEntryPayloads = new ArrayList<>();
                    for (ExamResultComputationEntry examResultComputationEntry : examResultComputationGroup.getResultComputationEntries()) {
                        UUID examId = examResultComputationEntry.getExamId();
                        String examName = examIdNameMap.get(examId);
                        if (examName == null) {
                            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                    examId + " exam not found in database"));
                        }
                        Set<String> dimensionNames = new HashSet<>();
                        for (Integer dimension : examResultComputationEntry.getDimensions()) {
                            String dimensionName = examDimensionIdNameMap.get(dimension);
                            if (dimensionName == null) {
                                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                        dimension + " dimension not found in database"));
                            }
                            dimensionNames.add(dimensionName);
                        }
                        examResultComputationEntryPayloads.add(new ExamResultComputationEntryPayload(examName, examResultComputationEntry.isTotalDimension(), dimensionNames));
                    }
                    examResultComputationGroupPayloads.add(new ExamResultComputationGroupPayload(examResultComputationGroup.getGroupId(), examResultComputationEntryPayloads));
                }
                examReportResultConfigsPayload.setResultComputationGroupList(examResultComputationGroupPayloads);
            }
            examReportStructurePayload.setExamReportResultConfigs(examReportResultConfigsPayload);
        }

        if (!CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())) {
            final Map<CourseType, ExamReportCourseStructurePayload> examReportCourseStructureMap = createExamReportCourseStructurePayload(
                    examReportStructure, courseIdNameMap, examIdNameMap);
            examReportStructurePayload.setExamReportCourseStructure(examReportCourseStructureMap);
        }

        if (!CollectionUtils.isEmpty(examReportStructure.getExamReportHeaderStructureColumns())) {
            final Map<CourseType, List<ExamReportHeaderStructureColumnPayload>> examReportHeaderStructureColumnMap = new HashMap<>();

            for (final Entry<CourseType, List<ExamReportHeaderStructureColumn>> entry : examReportStructure
                    .getExamReportHeaderStructureColumns().entrySet()) {

                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }

                final List<ExamReportHeaderStructureColumnPayload> examReportHeaderStructureColumns = new ArrayList<>();
                for (final ExamReportHeaderStructureColumn examReportHeaderStructureColumn : entry.getValue()) {
                    examReportHeaderStructureColumns.add(getExamReportHeaderStructureColumnPayload(
                            examReportHeaderStructureColumn, examIdNameMap, examDimensionIdNameMap));
                }

                examReportHeaderStructureColumnMap.put(entry.getKey(), examReportHeaderStructureColumns);
            }

            examReportStructurePayload.setExamReportHeaderStructureColumns(examReportHeaderStructureColumnMap);
        }
        if (!CollectionUtils.isEmpty(examReportStructure.getExamReportAdditionalSubjectHeaderStructureColumns())) {
            final Map<CourseType, List<ExamReportHeaderStructureColumnPayload>> examReportHeaderStructureColumnMap = new HashMap<>();

            for (final Entry<CourseType, List<ExamReportHeaderStructureColumn>> entry : examReportStructure
                    .getExamReportAdditionalSubjectHeaderStructureColumns().entrySet()) {

                if (CollectionUtils.isEmpty(entry.getValue())) {
                    continue;
                }

                final List<ExamReportHeaderStructureColumnPayload> examReportHeaderStructureColumns = new ArrayList<>();
                for (final ExamReportHeaderStructureColumn examReportHeaderStructureColumn : entry.getValue()) {
                    examReportHeaderStructureColumns.add(getExamReportHeaderStructureColumnPayload(
                            examReportHeaderStructureColumn, examIdNameMap, examDimensionIdNameMap));
                }

                examReportHeaderStructureColumnMap.put(entry.getKey(), examReportHeaderStructureColumns);
            }

            examReportStructurePayload.setExamReportAdditionalSubjectHeaderStructureColumns(examReportHeaderStructureColumnMap);
        }

        examReportCardConfigurationPayload.setExamReportStructurePayload(examReportStructurePayload);
        return examReportCardConfigurationPayload;
    }

    private void populateMap(final List<Course> courses, final List<ExamDetails> examDetailsList,
                             final List<ExamDimension> examDimensions, final Map<UUID, String> courseIdNameMap,
                             final Map<UUID, String> examIdNameMap, final Map<Integer, String> examDimensionIdNameMap) {
        if (!CollectionUtils.isEmpty(courses)) {
            for (final Course course : courses) {
                courseIdNameMap.put(course.getCourseId(), course.getCourseName().trim().toLowerCase());
            }
        }

        if (!CollectionUtils.isEmpty(examDetailsList)) {
            for (final ExamDetails examDetails : examDetailsList) {
                examIdNameMap.put(examDetails.getExamMetaData().getExamId(),
                        examDetails.getExamMetaData().getExamName().trim().toLowerCase());
            }
        }

        if (!CollectionUtils.isEmpty(examDimensions)) {
            for (final ExamDimension examDimension : examDimensions) {
                examDimensionIdNameMap.put(examDimension.getDimensionId(),
                        examDimension.getDimensionName().trim().toLowerCase());
            }
        }
    }

    private void populateMapping(final List<Course> courses, final List<ExamDetails> examDetailsList,
                                 final List<ExamDimension> examDimensions, final Map<String, UUID> courseNameIdMap,
                                 final Map<String, UUID> examNameIdMap, final Map<String, Integer> examDimensionNameIdMap) {
        if (!CollectionUtils.isEmpty(courses)) {
            for (final Course course : courses) {
                courseNameIdMap.put(course.getCourseName().trim().toLowerCase(), course.getCourseId());
            }
        }

        if (!CollectionUtils.isEmpty(examDetailsList)) {
            for (final ExamDetails examDetails : examDetailsList) {
                examNameIdMap.put(examDetails.getExamMetaData().getExamName().trim().toLowerCase(),
                        examDetails.getExamMetaData().getExamId());
            }
        }

        if (!CollectionUtils.isEmpty(examDimensions)) {
            for (final ExamDimension examDimension : examDimensions) {
                examDimensionNameIdMap.put(examDimension.getDimensionName().trim().toLowerCase(),
                        examDimension.getDimensionId());
            }
        }
    }

    private ExamReportHeaderStructureColumn getExamReportHeaderStructureColumn(
            ExamReportHeaderStructureColumnPayload examReportHeaderStructureColumnPayload,
            final Map<String, UUID> examNameIdMap, final Map<String, Integer> examDimensionNameIdMap) {

        final ExamReportHeaderStructureColumn examReportHeaderStructureColumn = new ExamReportHeaderStructureColumn();
        examReportHeaderStructureColumn.setId(examReportHeaderStructureColumnPayload.getId());
        examReportHeaderStructureColumn.setTitle(examReportHeaderStructureColumnPayload.getTitle());
        examReportHeaderStructureColumn.setSubTitle(examReportHeaderStructureColumnPayload.getSubTitle());
        examReportHeaderStructureColumn
                .setDisplayTotalMarks(examReportHeaderStructureColumnPayload.isDisplayTotalMarks());
        examReportHeaderStructureColumn
                .setExamReportGridColumnType(examReportHeaderStructureColumnPayload.getExamReportGridColumnType());
        examReportHeaderStructureColumn
                .setComputationColumns(examReportHeaderStructureColumnPayload.getComputationColumns());
        examReportHeaderStructureColumn.setColorHexCode(examReportHeaderStructureColumnPayload.getColorHexCode());
        examReportHeaderStructureColumn.setRoundOff(examReportHeaderStructureColumnPayload.getRoundOff());
        examReportHeaderStructureColumn.setCourseWiseClassMetricsComputeColumn(examReportHeaderStructureColumnPayload.getCourseWiseClassMetricsComputeColumn());
        examReportHeaderStructureColumn.setCourseWiseGainMetricsComputeColumns(examReportHeaderStructureColumnPayload.getCourseWiseGainMetricsComputeColumns());
        examReportHeaderStructureColumn.setColumnValue(examReportHeaderStructureColumnPayload.getColumnValue());
        examReportHeaderStructureColumn.setHide(examReportHeaderStructureColumnPayload.isHide());

        examReportHeaderStructureColumn.setHideTotalDimensionMaxMarks(examReportHeaderStructureColumnPayload.isHideTotalDimensionMaxMarks());
        examReportHeaderStructureColumn.setHideRemainingDimensionMaxMarks(examReportHeaderStructureColumnPayload.isHideRemainingDimensionMaxMarks());

        if (StringUtils.isNotBlank(examReportHeaderStructureColumnPayload.getExamName())) {
            final UUID examId = examNameIdMap
                    .get(examReportHeaderStructureColumnPayload.getExamName().trim().toLowerCase());
            if (examId == null) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                        examReportHeaderStructureColumnPayload.getExamName() + " exam not found in database"));
            }
            examReportHeaderStructureColumn.setExamId(examId);
        }

        if (!CollectionUtils.isEmpty(examReportHeaderStructureColumnPayload.getObtainedMarksDimensions())) {
            final List<Integer> obtainedMarksDimensions = new ArrayList<>();
            for (final String dimensionName : examReportHeaderStructureColumnPayload.getObtainedMarksDimensions()) {
                final Integer dimensionId = examDimensionNameIdMap.get(dimensionName.trim().toLowerCase());
                if (dimensionId == null) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                            dimensionName + " dimension not found in database"));
                }
                obtainedMarksDimensions.add(dimensionId);
            }
            examReportHeaderStructureColumn.setObtainedMarksDimensions(obtainedMarksDimensions);
        }

        if (CollectionUtils
                .isEmpty(examReportHeaderStructureColumnPayload.getChildExamReportHeaderStructureColumns())) {
            return examReportHeaderStructureColumn;
        }

        final List<ExamReportHeaderStructureColumn> childExamReportHeaderStructureColumns = new ArrayList<>();

        for (final ExamReportHeaderStructureColumnPayload childExamReportHeaderStructureColumnPayload : examReportHeaderStructureColumnPayload
                .getChildExamReportHeaderStructureColumns()) {

            childExamReportHeaderStructureColumns.add(getExamReportHeaderStructureColumn(
                    childExamReportHeaderStructureColumnPayload, examNameIdMap, examDimensionNameIdMap));
        }

        examReportHeaderStructureColumn.setChildExamReportHeaderStructureColumns(childExamReportHeaderStructureColumns);

        return examReportHeaderStructureColumn;

    }

    private ExamReportHeaderStructureColumnPayload getExamReportHeaderStructureColumnPayload(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn, final Map<UUID, String> examIdNameMap,
            final Map<Integer, String> examDimensionIdNameMap) {

        final ExamReportHeaderStructureColumnPayload examReportHeaderStructureColumnPayload = new ExamReportHeaderStructureColumnPayload();

        examReportHeaderStructureColumnPayload.setId(examReportHeaderStructureColumn.getId());
        examReportHeaderStructureColumnPayload.setTitle(examReportHeaderStructureColumn.getTitle());
        examReportHeaderStructureColumnPayload.setSubTitle(examReportHeaderStructureColumn.getSubTitle());
        examReportHeaderStructureColumnPayload.setColorHexCode(examReportHeaderStructureColumn.getColorHexCode());
        examReportHeaderStructureColumnPayload
                .setDisplayTotalMarks(examReportHeaderStructureColumn.isDisplayTotalMarks());
        examReportHeaderStructureColumnPayload
                .setExamReportGridColumnType(examReportHeaderStructureColumn.getExamReportGridColumnType());
        examReportHeaderStructureColumnPayload
                .setComputationColumns(examReportHeaderStructureColumn.getComputationColumns());
        examReportHeaderStructureColumnPayload.setRoundOff(examReportHeaderStructureColumn.getRoundOff());
        examReportHeaderStructureColumnPayload.setCourseWiseClassMetricsComputeColumn(examReportHeaderStructureColumn.getCourseWiseClassMetricsComputeColumn());
        examReportHeaderStructureColumnPayload.setCourseWiseGainMetricsComputeColumns(examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns());
        examReportHeaderStructureColumnPayload.setColumnValue(examReportHeaderStructureColumn.getColumnValue());
        examReportHeaderStructureColumnPayload.setHide(examReportHeaderStructureColumn.isHide());

        examReportHeaderStructureColumnPayload.setHideTotalDimensionMaxMarks(examReportHeaderStructureColumn.isHideTotalDimensionMaxMarks());
        examReportHeaderStructureColumnPayload.setHideRemainingDimensionMaxMarks(examReportHeaderStructureColumn.isHideRemainingDimensionMaxMarks());


        if (examReportHeaderStructureColumn.getExamId() != null) {
            final String examName = examIdNameMap.get(examReportHeaderStructureColumn.getExamId());
            examReportHeaderStructureColumnPayload.setExamName(examName);
        }

        if (!CollectionUtils.isEmpty(examReportHeaderStructureColumn.getObtainedMarksDimensions())) {
            final List<String> obtainedMarksDimensions = new ArrayList<>();
            for (final Integer dimensionId : examReportHeaderStructureColumn.getObtainedMarksDimensions()) {
                final String dimensionName = examDimensionIdNameMap.get(dimensionId);

                obtainedMarksDimensions.add(dimensionName);
            }
            examReportHeaderStructureColumnPayload.setObtainedMarksDimensions(obtainedMarksDimensions);
        }

        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getChildExamReportHeaderStructureColumns())) {
            return examReportHeaderStructureColumnPayload;
        }

        final List<ExamReportHeaderStructureColumnPayload> childExamReportHeaderStructureColumns = new ArrayList<>();

        for (final ExamReportHeaderStructureColumn childExamReportHeaderStructureColumn : examReportHeaderStructureColumn
                .getChildExamReportHeaderStructureColumns()) {

            childExamReportHeaderStructureColumns.add(getExamReportHeaderStructureColumnPayload(
                    childExamReportHeaderStructureColumn, examIdNameMap, examDimensionIdNameMap));
        }

        examReportHeaderStructureColumnPayload
                .setChildExamReportHeaderStructureColumns(childExamReportHeaderStructureColumns);

        return examReportHeaderStructureColumnPayload;

    }

    private Map<CourseType, ExamReportCourseStructure> createExamReportCourseStructure(
            ExamReportStructurePayload examReportCardStructurePayload, final Map<String, UUID> courseNameIdMap,
            Map<String, UUID> examNameIdMap) {
        final Map<CourseType, ExamReportCourseStructure> examReportCourseStructureMap = new HashMap<>();

        for (final Entry<CourseType, ExamReportCourseStructurePayload> entry : examReportCardStructurePayload
                .getExamReportCourseStructure().entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            final ExamReportCourseStructurePayload examReportCourseStructurePayload = entry.getValue();

            final ExamReportCourseStructure examReportCourseStructure = new ExamReportCourseStructure();

            if (!CollectionUtils.isEmpty(examReportCourseStructurePayload.getMandatoryCourses())) {
                final Set<UUID> mandatoryCourses = new HashSet<>();
                for (final String courseName : examReportCourseStructurePayload.getMandatoryCourses()) {
                    final UUID courseId = courseNameIdMap.get(courseName.trim().toLowerCase());
                    if (courseId == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseName + " course not found in database"));
                    }
                    mandatoryCourses.add(courseId);
                }
                examReportCourseStructure.setMandatoryCourses(mandatoryCourses);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructurePayload.getSelectionCourses())) {
                final Set<UUID> selectionCourses = new HashSet<>();
                for (final String courseName : examReportCourseStructurePayload.getSelectionCourses()) {
                    final UUID courseId = courseNameIdMap.get(courseName.trim().toLowerCase());
                    if (courseId == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseName + " course not found in database"));
                    }
                    selectionCourses.add(courseId);
                }
                examReportCourseStructure.setSelectionCourses(selectionCourses);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructurePayload.getAdditionalCourses())) {
                final Set<UUID> additionalCourses = new HashSet<>();
                for (final String courseName : examReportCourseStructurePayload.getAdditionalCourses()) {
                    final UUID courseId = courseNameIdMap.get(courseName.trim().toLowerCase());
                    if (courseId == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseName + " course not found in database"));
                    }
                    additionalCourses.add(courseId);
                }
                examReportCourseStructure.setAdditionalCourses(additionalCourses);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructurePayload.getCourseOrder())) {
                final List<UUID> courseOrder = new ArrayList<>();
                for (final String courseName : examReportCourseStructurePayload.getCourseOrder()) {
                    final UUID courseId = courseNameIdMap.get(courseName.trim().toLowerCase());
                    if (courseId == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseName + " course not found in database"));
                    }
                    courseOrder.add(courseId);
                }
                examReportCourseStructure.setCourseOrder(courseOrder);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructurePayload.getAdditionalExamMarksToFetch())) {
                final Set<UUID> additionalExamMarksToFetch = new HashSet<>();
                for (final String examName : examReportCourseStructurePayload.getAdditionalExamMarksToFetch()) {
                    final UUID examId = examNameIdMap.get(examName.trim().toLowerCase());
                    if (examId == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                examName + " exam not found in database"));
                    }
                    additionalExamMarksToFetch.add(examId);
                }
                examReportCourseStructure.setAdditionalExamMarksToFetch(additionalExamMarksToFetch);
            }

            examReportCourseStructure
                    .setTopCoursesSelectionCount(examReportCourseStructurePayload.getTopCoursesSelectionCount());

            examReportCourseStructure
                    .setNetTotalComputationColumns(examReportCourseStructurePayload.getNetTotalComputationColumns());



            examReportCourseStructure
                    .setGridHeadingBackgroundColor(examReportCourseStructurePayload.getGridHeadingBackgroundColor());

            examReportCourseStructure
                    .setGridHeadingTextColor(examReportCourseStructurePayload.getGridHeadingTextColor());

            examReportCourseStructure
                    .setGridCoursesBackgroundColor(examReportCourseStructurePayload.getGridCoursesBackgroundColor());

            examReportCourseStructure
                    .setGridCoursesTextColor(examReportCourseStructurePayload.getGridCoursesTextColor());

            examReportCourseStructure
                    .setTotalRowBackgroundColor(examReportCourseStructurePayload.getTotalRowBackgroundColor());

            examReportCourseStructure
                    .setTotalRowTextColor(examReportCourseStructurePayload.getTotalRowTextColor());


            examReportCourseStructureMap.put(entry.getKey(), examReportCourseStructure);
        }

        return examReportCourseStructureMap;
    }

    private Map<CourseType, ExamReportCourseStructurePayload> createExamReportCourseStructurePayload(
            ExamReportStructure examReportCardStructure, final Map<UUID, String> courseIdNameMap,
            Map<UUID, String> examIdNameMap) {
        final Map<CourseType, ExamReportCourseStructurePayload> examReportCourseStructureMap = new HashMap<>();

        for (final Entry<CourseType, ExamReportCourseStructure> entry : examReportCardStructure
                .getExamReportCourseStructure().entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            final ExamReportCourseStructure examReportCourseStructure = entry.getValue();

            final ExamReportCourseStructurePayload examReportCourseStructurePayload = new ExamReportCourseStructurePayload();

            if (!CollectionUtils.isEmpty(examReportCourseStructure.getMandatoryCourses())) {
                final Set<String> mandatoryCourses = new HashSet<>();
                for (final UUID courseId : examReportCourseStructure.getMandatoryCourses()) {
                    final String courseName = courseIdNameMap.get(courseId);
                    if (courseName == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseId + " course not found in database"));
                    }
                    mandatoryCourses.add(courseName);
                }
                examReportCourseStructurePayload.setMandatoryCourses(mandatoryCourses);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructure.getSelectionCourses())) {
                final Set<String> selectionCourses = new HashSet<>();
                for (final UUID courseId : examReportCourseStructure.getSelectionCourses()) {
                    final String courseName = courseIdNameMap.get(courseId);
                    if (courseName == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseId + " course not found in database"));
                    }
                    selectionCourses.add(courseName);
                }
                examReportCourseStructurePayload.setSelectionCourses(selectionCourses);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructure.getAdditionalCourses())) {
                final Set<String> additionalCourses = new HashSet<>();
                for (final UUID courseId : examReportCourseStructure.getAdditionalCourses()) {
                    final String courseName = courseIdNameMap.get(courseId);
                    if (courseName == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseId + " course not found in database"));
                    }
                    additionalCourses.add(courseName);
                }
                examReportCourseStructurePayload.setAdditionalCourses(additionalCourses);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructure.getCourseOrder())) {
                final List<String> courseOrder = new ArrayList<>();
                for (final UUID courseId : examReportCourseStructure.getCourseOrder()) {
                    final String courseName = courseIdNameMap.get(courseId);
                    if (courseName == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                courseId + " course not found in database"));
                    }
                    courseOrder.add(courseName);
                }
                examReportCourseStructurePayload.setCourseOrder(courseOrder);
            }

            if (!CollectionUtils.isEmpty(examReportCourseStructure.getAdditionalExamMarksToFetch())) {
                final Set<String> examNames = new HashSet<>();
                for (final UUID examId : examReportCourseStructure.getAdditionalExamMarksToFetch()) {
                    final String examName = examIdNameMap.get(examId);
                    if (examName == null) {
                        throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
                                examId + " course not found in database"));
                    }
                    examNames.add(examName);
                }
                examReportCourseStructurePayload.setAdditionalExamMarksToFetch(examNames);
            }

            examReportCourseStructurePayload
                    .setTopCoursesSelectionCount(examReportCourseStructure.getTopCoursesSelectionCount());

            examReportCourseStructurePayload
                    .setNetTotalComputationColumns(examReportCourseStructure.getNetTotalComputationColumns());

            examReportCourseStructurePayload
                    .setGridHeadingBackgroundColor(examReportCourseStructure.getGridHeadingBackgroundColor());

            examReportCourseStructurePayload
                    .setGridHeadingTextColor(examReportCourseStructure.getGridHeadingTextColor());

            examReportCourseStructurePayload
                    .setGridCoursesBackgroundColor(examReportCourseStructure.getGridCoursesBackgroundColor());

            examReportCourseStructurePayload
                    .setGridCoursesTextColor(examReportCourseStructure.getGridCoursesTextColor());

            examReportCourseStructurePayload
                    .setTotalRowBackgroundColor(examReportCourseStructure.getTotalRowBackgroundColor());

            examReportCourseStructurePayload
                    .setTotalRowTextColor(examReportCourseStructure.getTotalRowTextColor());


            examReportCourseStructureMap.put(entry.getKey(), examReportCourseStructurePayload);
        }

        return examReportCourseStructureMap;
    }

    // public ExamReportStructure getMockExamReportStructure(int instituteId,
    // int academicSessionId, UUID standardId,
    // String reportType) {
    // final ExamReportStructure examReportCardMetaData = new
    // ExamReportStructure();
    // final Map<CourseType, List<ExamReportCardHeaderConfiguration>>
    // examReportCardHeaderConfigurationsMap = new HashMap<>();
    //
    // final List<ExamReportCardHeaderConfiguration>
    // examReportCardHeaderConfigurations = new ArrayList<>();
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration1 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration1.setExamId(UUID.fromString("4c2f9a09-6400-4540-b57a-ebd6274591df"));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration1);
    //
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration2 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration2.setExamId(UUID.fromString("8c287e0c-f4c5-4e03-9111-c84fd2f5add0"));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration2);
    //
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration3 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration3.setExamId(UUID.fromString("3b92b3d4-316d-4f4c-bd07-6501f01aba9f"));
    // // examReportCardHeaderConfiguration3.setDimensions(Arrays.asList(3, 4,
    // // 1));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration3);
    //
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration4 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration4.setExamId(UUID.fromString("a2166396-395e-46a4-901f-3ce038cbb439"));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration4);
    //
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration5 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration5.setSumColumn(true);
    // examReportCardHeaderConfiguration5.setSumColumnName("Total");
    // examReportCardHeaderConfiguration5
    // .setSummationExamIds(Arrays.asList(UUID.fromString("4c2f9a09-6400-4540-b57a-ebd6274591df"),
    // UUID.fromString("8c287e0c-f4c5-4e03-9111-c84fd2f5add0"),
    // UUID.fromString("3b92b3d4-316d-4f4c-bd07-6501f01aba9f"),
    // UUID.fromString("a2166396-395e-46a4-901f-3ce038cbb439")));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration5);
    //
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration6 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration6.setExamId(UUID.fromString("4932df6b-9804-46e4-b48c-5091a3084128"));
    // // examReportCardHeaderConfiguration6.setDimensions(Arrays.asList(3, 4,
    // // 1));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration6);
    //
    // final ExamReportCardHeaderConfiguration
    // examReportCardHeaderConfiguration7 = new
    // ExamReportCardHeaderConfiguration();
    // examReportCardHeaderConfiguration7.setSumColumn(true);
    // examReportCardHeaderConfiguration7.setGradeColumn(true);
    // examReportCardHeaderConfiguration7.setSumColumnName("Grand Total");
    // examReportCardHeaderConfiguration7.setGradeColumnName("Grade");
    // examReportCardHeaderConfiguration7
    // .setSummationExamIds(Arrays.asList(UUID.fromString("4c2f9a09-6400-4540-b57a-ebd6274591df"),
    // UUID.fromString("8c287e0c-f4c5-4e03-9111-c84fd2f5add0"),
    // UUID.fromString("3b92b3d4-316d-4f4c-bd07-6501f01aba9f"),
    // UUID.fromString("a2166396-395e-46a4-901f-3ce038cbb439"),
    // UUID.fromString("4932df6b-9804-46e4-b48c-5091a3084128")));
    // examReportCardHeaderConfigurations.add(examReportCardHeaderConfiguration7);
    //
    // examReportCardHeaderConfigurationsMap.put(CourseType.SCHOLASTIC,
    // examReportCardHeaderConfigurations);
    //
    // final List<ExamReportCardHeaderConfiguration>
    // coScholaticExamReportCardHeaderConfigurations = new ArrayList<>();
    // final ExamReportCardHeaderConfiguration
    // coScholaticExamReportCardHeaderConfiguration1 = new
    // ExamReportCardHeaderConfiguration();
    // coScholaticExamReportCardHeaderConfiguration1
    // .setExamId(UUID.fromString("07f82b83-1754-4f92-a445-bbbd7157246d"));
    // coScholaticExamReportCardHeaderConfigurations.add(coScholaticExamReportCardHeaderConfiguration1);
    //
    // // final ExamReportCardHeaderConfiguration
    // // coScholaticExamReportCardHeaderConfiguration2 = new
    // // ExamReportCardHeaderConfiguration();
    // // coScholaticExamReportCardHeaderConfiguration2
    // // .setExamId(UUID.fromString("4c2f9a09-6400-4540-b57a-ebd6274591df"));
    // //
    // coScholaticExamReportCardHeaderConfigurations.add(coScholaticExamReportCardHeaderConfiguration2);
    // //
    // // final ExamReportCardHeaderConfiguration
    // // coScholaticExamReportCardHeaderConfiguration3 = new
    // // ExamReportCardHeaderConfiguration();
    // // coScholaticExamReportCardHeaderConfiguration3.setSumColumn(true);
    // //
    // coScholaticExamReportCardHeaderConfiguration3.setSumColumnName("Total");
    // // coScholaticExamReportCardHeaderConfiguration3
    // //
    // .setSummationExamIds(Arrays.asList(UUID.fromString("4fe90778-da0e-459f-a7cf-e22c72740e9c"),
    // // UUID.fromString("4c2f9a09-6400-4540-b57a-ebd6274591df")));
    // //
    // coScholaticExamReportCardHeaderConfigurations.add(coScholaticExamReportCardHeaderConfiguration3);
    //
    // examReportCardHeaderConfigurationsMap.put(CourseType.COSCHOLASTIC,
    // coScholaticExamReportCardHeaderConfigurations);
    //
    // examReportCardMetaData.setTemplateClass("com.lernen.cloud.pdf.exam.reports.ExamReportGenerator10005");
    // examReportCardMetaData.setExamReportCardHeaderConfigurations(examReportCardHeaderConfigurationsMap);
    // return examReportCardMetaData;
    // // return examinationDao.getExamReportCardMetaData(instituteId,
    // // academicSessionId, standardId, reportType);
    // }

    public List<StudentExamMarksDetails> getStudentEffectiveExamMarks(int instituteId, int academicSessionId,
                                                                      UUID standardId, UUID studentId, ExamReportStructure examReportStructure) {
        final Map<UUID, List<StudentExamMarksDetails>> classEffectiveMarks = getClassEffectiveExamMarks(instituteId,
                academicSessionId, standardId, null, examReportStructure);
        return classEffectiveMarks.get(studentId);
    }

    /**
     * Map<StudentId, List<StudentExamMarksDetails>>
     *
     * @param instituteId
     * @param academicSessionId
     * @param standardId
     * @param examReportCardMetaData
     * @return
     */
    public Map<UUID, List<StudentExamMarksDetails>> getClassEffectiveExamMarks(int instituteId, int academicSessionId,
                                                                               UUID standardId, Set<Integer> sectionIdSet, ExamReportStructure examReportCardMetaData) {
        final Map<UUID, List<StudentExamMarksDetails>> classEffectiveExamMarks = new HashMap<>();
        for (final Entry<CourseType, List<ExamReportHeaderStructureColumn>> examReportCardHeaderStructureColumnEntry : examReportCardMetaData
                .getExamReportHeaderStructureColumns().entrySet()) {
            for (final ExamReportHeaderStructureColumn examReportCardHeaderConfiguration : examReportCardHeaderStructureColumnEntry
                    .getValue()) {

                /**
                 * Get leaf exam nodes
                 */
                final Set<UUID> leafExamIds = new HashSet<>();
                updateLeafExamIds(examReportCardHeaderConfiguration, leafExamIds);

                boolean filterOnBasisOfMarksSubmitted = userPreferenceSettings.getExaminationPreferences(instituteId).isReportCardViewOnlySubmittedMarks();

                for (final UUID examId : leafExamIds) {

                    final List<StudentExamMarksDetails> classExamMarksDetails = examinationManager
                            .getResolvedClassMarks(instituteId, examId, null, sectionIdSet, filterOnBasisOfMarksSubmitted);

                    if(CollectionUtils.isEmpty(classExamMarksDetails)) {
                        continue;
                    }
                    for (final StudentExamMarksDetails studentExamMarksDetails : classExamMarksDetails) {
                        if (!classEffectiveExamMarks.containsKey(studentExamMarksDetails.getStudent().getStudentId())) {
                            classEffectiveExamMarks.put(studentExamMarksDetails.getStudent().getStudentId(),
                                    new ArrayList<>());
                        }
                        classEffectiveExamMarks.get(studentExamMarksDetails.getStudent().getStudentId())
                                .add(studentExamMarksDetails);
                    }
                }

            }
        }
        return classEffectiveExamMarks;
    }

    /**
     * Populates exam ids for leaf nodes. There should be no exam node which has
     * children
     *
     * @param examReportHeaderStructureColumn
     * @param examIds
     */
    private void updateLeafExamIds(ExamReportHeaderStructureColumn examReportHeaderStructureColumn, Set<UUID> examIds) {
        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getChildExamReportHeaderStructureColumns())) {
            if (examReportHeaderStructureColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM
                    || examReportHeaderStructureColumn
                    .getExamReportGridColumnType() == ExamReportGridColumnType.EXAM_GRADE
                    || examReportHeaderStructureColumn
                    .getExamReportGridColumnType() == ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
                examIds.add(examReportHeaderStructureColumn.getExamId());
            }
            return;
        }

        for (final ExamReportHeaderStructureColumn childColumns : examReportHeaderStructureColumn
                .getChildExamReportHeaderStructureColumns()) {
            updateLeafExamIds(childColumns, examIds);
        }
    }

    private Map<UUID, Student> getStudentMap(List<Student> students) {
        final Map<UUID, Student> studentsMap = new HashMap<>();
        for (final Student student : students) {
            studentsMap.put(student.getStudentId(), student);
        }
        return studentsMap;
    }

    /**
     * Generates ExamReportData for all students of a class
     *
     * @param instituteId
     * @param academicSessionId
     * @param standardId
     * @param examReportStructure
     * @param reportType
     * @return
     */
    public List<ExamReportData> getClassExamReportData(int instituteId, int academicSessionId, UUID standardId,
                                                       Set<Integer> sectionIdSet, ExamReportStructure examReportStructure, String reportType, boolean onlyEnrolledStudents) {

        final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId,
                academicSessionId, standardId);
        Set<StudentStatus> studentStatusSet = getStudentStatus(onlyEnrolledStudents);
        List<Student> students = studentManager.getStudentsByStandardIds(instituteId, academicSessionId, new HashSet<UUID>(Arrays.asList(standardId)),
                studentStatusSet);
        if (CollectionUtils.isEmpty(students)) {
            return new ArrayList<>();
        }
        return getStudentExamReportData(students,instituteId,academicSessionId,standardId,
                sectionIdSet,examReportStructure,courseTypeExamGrades,reportType,true);
    }

    public Set<StudentStatus> getStudentStatus(boolean onlyEnrolledStudents){
        Set<StudentStatus> studentStatusSet = new HashSet<>();
        studentStatusSet.add(StudentStatus.ENROLLED);
        if(!onlyEnrolledStudents) {
            studentStatusSet.add(StudentStatus.RELIEVED);
            studentStatusSet.add(StudentStatus.DELETED);
            studentStatusSet.add(StudentStatus.NSO);
            studentStatusSet.add(StudentStatus.ENROLMENT_PENDING);
        }
        return studentStatusSet;
    }

    public  List<ExamReportData> getStudentExamReportData(List<Student> students,int instituteId, int academicSessionId, UUID standardId,
                                                          Set<Integer> sectionIdSet, ExamReportStructure examReportStructure, Map<CourseType, List<ExamGrade>> courseTypeExamGrades,String reportType,boolean rank){
        final Map<UUID, Student> studentsMap = getStudentMap(students);

        final ClassExamData classExamData = getClassExamReportMarksGrid(instituteId, standardId, sectionIdSet,
                academicSessionId, examReportStructure, courseTypeExamGrades);

        final Map<UUID, List<StudentExamMarksDetails>> classEffectiveAdditionalExamMarks = fetchAdditionalExamMarksDetails(
                instituteId, examReportStructure);

        if (classExamData == null || CollectionUtils.isEmpty(classExamData.getClassExamReportMarksGridMap())) {
            return new ArrayList<>();
        }


        ExamReportMarksGrid selectedStudentScholasticGrid = null;
        for (final Entry<UUID, Map<CourseType, ExamReportMarksGrid>> studentLevelEntry : classExamData
                .getClassExamReportMarksGridMap().entrySet()) {
            ExamReportMarksGrid studentScholasticGrid = studentLevelEntry.getValue().get(CourseType.SCHOLASTIC);
            if(studentScholasticGrid == null || CollectionUtils.isEmpty(studentScholasticGrid.getExamReportAdditionalAttributesRows())){
                continue;
            }
            selectedStudentScholasticGrid = studentScholasticGrid;

            // Required to get for 1 student only as exam data should be same for all students in class
            break;
        }

        Map<String, Map<UUID, Map<AttendanceStatus, Integer>>> columnStudentAttendanceMap = getColumnStudentAttendanceMap(instituteId, academicSessionId, studentsMap.keySet(), selectedStudentScholasticGrid);

        //Student Id, Percentage
        final Map<UUID, Double> classPercentMap = new HashMap<>();
        //Course Id, StudentId, Percentage
        final Map<UUID, Map<UUID, Double>> courseWiseClassPercentMap = new HashMap<>();
        //CourseId, StudentId, totalMarks
        final Map<UUID, Map<UUID, Double>> courseWiseClassObtainedMarksMap = new HashMap<>();

        //StudentId, ExamId, Percentage
        final Map<UUID, Map<UUID, Double>> studentExamPercentageMap = new HashMap<>();
        //StudentId, ExamId, ExamResultStatus
        final Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap = new HashMap<>();

        final Map<UUID, ExamReportData> studentExamReportData = new HashMap<>();
        for (final Entry<UUID, Map<CourseType, ExamReportMarksGrid>> studentLevelEntry : classExamData
                .getClassExamReportMarksGridMap().entrySet()) {
            final UUID studentId = studentLevelEntry.getKey();

            final List<StudentExamMarksDetails> studentExamMarksDetails = getStudentMarksDetails(instituteId, studentId,
                    classEffectiveAdditionalExamMarks, classExamData);

            if(!studentExamPercentageMap.containsKey(studentId)) {
                studentExamPercentageMap.put(studentId, new HashMap<>());
            }

            if(!studentExamResultStatusMap.containsKey(studentId)) {
                studentExamResultStatusMap.put(studentId, new HashMap<>());
            }

            final ExamReportData examReportData = computeExamReportMarksData(instituteId, examReportStructure,
                    studentLevelEntry.getValue(), courseTypeExamGrades, studentsMap.get(studentId),
                    studentExamMarksDetails, studentExamPercentageMap.get(studentId), studentExamResultStatusMap.get(studentId));

            studentExamReportData.put(studentId, examReportData);

            if (examReportData.getPercentage() != null && Double.compare(examReportData.getPercentage(), 0d) >= 0) {

                /**
                 * for institute 10225, 10226, 10227, 10228, we need to show rank for all students irrespective of the result status
                 */
                if(instituteId == 10225 || instituteId == 10226 || instituteId == 10227 || instituteId == 10228) {
                    classPercentMap.put(studentId, examReportData.getPercentage());
                } else {
                    /**
                     * rank will only be assigned when result status is PASS and PASS_WITH_GRACE
                     */
                    if (examReportData.getExamResultStatus() == ExamResultStatus.PASS
                            || examReportData.getExamResultStatus() == ExamResultStatus.PASS_WITH_GRACE) {
                        classPercentMap.put(studentId, examReportData.getPercentage());
                    }
                }
            }

            collectClassStats(examReportStructure, courseWiseClassPercentMap, courseWiseClassObtainedMarksMap, studentId, examReportData);
        }
        if(!CollectionUtils.isEmpty(classExamData.getAdditionalClassExamReportMarksGridMap())){
            for(final Entry<UUID, Map<CourseType, ExamReportMarksGrid>> studentLevelEntry : classExamData
            .getAdditionalClassExamReportMarksGridMap().entrySet()){
                ExamReportData examReportData = studentExamReportData.get(studentLevelEntry.getKey());
                examReportData.setAdditionalCourseTypeExamReportMarksGrid(studentLevelEntry.getValue());
                studentExamReportData.put(studentLevelEntry.getKey(), examReportData);
            }
        }
       
        final Standard promotedStandard = studentManager.getNextPropmotionStandard(instituteId, students.get(0));

        final StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId,
                standardId);

        final ReportCardVariableDetails reportCardVariableDetails = getStudentsReportCardVariables(instituteId,
                academicSessionId, standardId, reportType);

        final Map<UUID, StudentReportCardVariableDetails> reportCardVariablesDataMap = getReportCardVariablesDataMap(
                reportCardVariableDetails);

        final List<StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailList = getStudentPersonalityTraits(instituteId,
                standardId, null, academicSessionId, reportType, null, StudentSortingParameters.ADMISSION_NUMBER);

        final Map<UUID, StudentPersonalityTraitsDetails> studentPersonalityTraitsResponseMap = getStudentPersonalityTraitsResponseMap(
                studentPersonalityTraitsDetailList);

//        final Map<Double, Integer> classRankMap = ExamMarksUtils.getClassRankMap(classPercentMap);

        for (final Entry<UUID, ExamReportData> studentEntry : studentExamReportData.entrySet()) {
            UUID studentId = studentEntry.getKey();
            ExamReportData examReportData = studentEntry.getValue();

            populateExamAttendanceColumns(examReportData.getCourseTypeExamReportMarksGrid(), studentId, columnStudentAttendanceMap);

            if(rank) {
                Map<String, Map<UUID, Integer>> columnStudentRankMap = getColumnStudentRankMap(selectedStudentScholasticGrid, studentExamPercentageMap, studentExamResultStatusMap, instituteId);
                populateExamWiseRankColumns(examReportData.getCourseTypeExamReportMarksGrid(), studentId, columnStudentRankMap);
            }

            updateExamReportData(examReportData.getStudentLite(), promotedStandard, standardMetaData, reportCardVariableDetails,
                    reportCardVariablesDataMap, studentPersonalityTraitsResponseMap, examReportData);
            updateClassStats(studentId, examReportStructure, courseWiseClassPercentMap, courseWiseClassObtainedMarksMap, examReportData, instituteId);

            if(rank){
                IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
//                examReportData.setRank(ExamMarksUtils.getRank(classPercentMap, classRankMap, studentId));
                examReportData.setRank(rankCalculator.computeRank(classPercentMap, studentId));
            }
        }

        return new ArrayList<>(studentExamReportData.values());
    }

    private Map<UUID, Map<UUID, Integer>> getExamStudentWiseRank(Map<UUID, Map<UUID, Double>> studentExamPercentageMap,
                                                                 Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap, int instituteId) {

        //ExamId, StudentId, Percentage
        final Map<UUID, Map<UUID, Double>> examStudentWisePercentage = getExamStudentWisePercentage(studentExamPercentageMap);

        //ExamId, StudentId, ExamResultStatus
        final Map<UUID, Map<UUID, ExamResultStatus>> examStudentWiseResult = getExamStudentWiseResult(studentExamResultStatusMap);

        final Map<UUID, Map<UUID, Integer>> examStudentWiseRankMap = new HashMap<>();

        for(Entry<UUID, Map<UUID, Double>> examStudentWisePercentageEntry : examStudentWisePercentage.entrySet()) {
            UUID examId = examStudentWisePercentageEntry.getKey();
            Map<UUID, Double> studentPercentageMap = examStudentWisePercentageEntry.getValue();
            if(!examStudentWiseRankMap.containsKey(examId)) {
                examStudentWiseRankMap.put(examId, new HashMap<>());
            }
            for(Entry<UUID, Double> studentPercentageEntry : studentPercentageMap.entrySet()) {
                UUID studentId = studentPercentageEntry.getKey();
                if(!examStudentWiseRankMap.get(examId).containsKey(studentId)) {
                    examStudentWiseRankMap.get(examId).put(studentId, null);
                }
                IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
                Integer courseRank = MapUtils.isEmpty(studentPercentageMap) ? null : rankCalculator.computeRank(studentPercentageMap, studentId);

                ExamResultStatus examResultStatus = CollectionUtils.isEmpty(examStudentWiseResult) ? null :
                        CollectionUtils.isEmpty(examStudentWiseResult.get(examId)) ? null : examStudentWiseResult.get(examId).get(studentId);

                /**
                 * for institute 10225, 10226, 10227, 10228, we need to show rank for all students irrespective of the result status
                 */
                if(instituteId == 10225 || instituteId == 10226 || instituteId == 10227 || instituteId == 10228) {
                    examStudentWiseRankMap.get(examId).put(studentId, courseRank);
                } else {
                    /**
                     * rank will only be assigned when result status is PASS and PASS_WITH_GRACE
                     */
                    if ((examResultStatus == ExamResultStatus.PASS || examResultStatus == ExamResultStatus.PASS_WITH_GRACE)) {
                        examStudentWiseRankMap.get(examId).put(studentId, courseRank);
                    }
                }
            }
        }

        return examStudentWiseRankMap;

    }

    /**
     * conveting map of Map<StudentId, Map<ExamID, Percentage>> to Map<ExamId, Map<StudentId, Percentage>>
     * @param studentExamPercentageMap
     * @return
     */
    private Map<UUID, Map<UUID, Double>> getExamStudentWisePercentage(Map<UUID, Map<UUID, Double>> studentExamPercentageMap) {
        Map<UUID, Map<UUID, Double>> examStudentWisePercentage = new HashMap<>();
        for(Entry<UUID, Map<UUID, Double>> studentExamPercentageEntry : studentExamPercentageMap.entrySet()) {
            UUID studentId = studentExamPercentageEntry.getKey();
            Map<UUID, Double> examPercentageMap = studentExamPercentageEntry.getValue();
            for(Entry<UUID, Double> examPercentageMapEntry : examPercentageMap.entrySet()) {
                UUID examId = examPercentageMapEntry.getKey();
                Double percentage = examPercentageMapEntry.getValue();

                if(!examStudentWisePercentage.containsKey(examId)) {
                    examStudentWisePercentage.put(examId, new HashMap<>());
                }

                if(!examStudentWisePercentage.get(examId).containsKey(studentId)) {
                    examStudentWisePercentage.get(examId).put(studentId, percentage);
                }
            }
        }
        return examStudentWisePercentage;
    }

    /**
     * conveting map of Map<StudentId, Map<ExamID, ExamResultStatus>> to Map<ExamId, Map<StudentId, ExamResultStatus>>
     * @param studentExamResultStatusMap
     * @return
     */
    private Map<UUID, Map<UUID, ExamResultStatus>> getExamStudentWiseResult(Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap) {
        Map<UUID, Map<UUID, ExamResultStatus>> examStudentWiseResultStatus = new HashMap<>();
        for(Entry<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusEntry : studentExamResultStatusMap.entrySet()) {
            UUID studentId = studentExamResultStatusEntry.getKey();
            Map<UUID, ExamResultStatus> examResultStatusMap = studentExamResultStatusEntry.getValue();
            for(Entry<UUID, ExamResultStatus> examResultStatusMapEntry : examResultStatusMap.entrySet()) {
                UUID examId = examResultStatusMapEntry.getKey();
                ExamResultStatus examResultStatus = examResultStatusMapEntry.getValue();

                if(!examStudentWiseResultStatus.containsKey(examId)) {
                    examStudentWiseResultStatus.put(examId, new HashMap<>());
                }

                if(!examStudentWiseResultStatus.get(examId).containsKey(studentId)) {
                    examStudentWiseResultStatus.get(examId).put(studentId, examResultStatus);
                }
            }
        }
        return examStudentWiseResultStatus;
    }

    private Map<String, Map<UUID, Map<AttendanceStatus, Integer>>> getColumnStudentAttendanceMap(int instituteId, int academicSessionId,
                                                                                                 Set<UUID> studentSet, ExamReportMarksGrid studentScholasticGrid) {
        Map<String, ExamMetaData> columnExamMetaDataMap = getColumnExamMetadataMap(studentScholasticGrid, ExamGridRowAttribute.ATTENDANCE);

        Map<String, Map<UUID, Map<AttendanceStatus, Integer>>> columnStudentAttendanceMap = new HashMap<>();
        for (Entry<String, ExamMetaData> columnEntry : columnExamMetaDataMap.entrySet()) {
            String columnId = columnEntry.getKey();
            ExamMetaData examMetaData = columnEntry.getValue();
            Integer attendanceStartDate = examMetaData.getAttendanceStartDate();
            Integer attendanceEndDate = examMetaData.getAttendanceEndDate();
            if (attendanceStartDate == null || attendanceEndDate == null || attendanceStartDate <= 0 || attendanceEndDate <= 0 || attendanceEndDate < attendanceStartDate) {
                logger.warn("Invalid attendanceStartDate {} or attendanceEndDate {}", attendanceStartDate, attendanceEndDate);
                continue;
            }

            Map<UUID, Map<AttendanceStatus, Integer>> studentAttendanceDetailsMap = studentManager.getStudentAttendanceDetails(instituteId, academicSessionId, studentSet, attendanceStartDate, attendanceEndDate);
            columnStudentAttendanceMap.put(columnId, studentAttendanceDetailsMap);
        }
        return columnStudentAttendanceMap;
    }

    private Map<String, Map<UUID, Integer>> getColumnStudentRankMap(ExamReportMarksGrid studentScholasticGrid,
                                                                    Map<UUID, Map<UUID, Double>> studentExamPercentageMap, Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap, int institueId) {

        //ExamId, StudentId, Rank
        final Map<UUID, Map<UUID, Integer>> examStudentWiseRank = getExamStudentWiseRank(studentExamPercentageMap, studentExamResultStatusMap, institueId);
        Map<String, ExamMetaData> columnExamMetaDataMap = getColumnExamMetadataMap(studentScholasticGrid, ExamGridRowAttribute.RANK);

        Map<String, Map<UUID, Integer>> columnStudentRankMap = new HashMap<>();
        for (Entry<String, ExamMetaData> columnEntry : columnExamMetaDataMap.entrySet()) {
            String columnId = columnEntry.getKey();
            ExamMetaData examMetaData = columnEntry.getValue();
            Map<UUID, Integer> studentWiseRankMap = examStudentWiseRank.get(examMetaData.getExamId());
            columnStudentRankMap.put(columnId, studentWiseRankMap);
        }
        return columnStudentRankMap;
    }

    private  Map<String, ExamMetaData> getColumnExamMetadataMap(ExamReportMarksGrid studentScholasticGrid, ExamGridRowAttribute examGridRowAttribute) {
        Map<String, ExamMetaData> columnExamMetaDataMap = new HashMap<>();
        if(studentScholasticGrid == null) {
            return columnExamMetaDataMap;
        }
        for (ExamReportAdditionalAttributesRow examReportAdditionalAttributesRow : studentScholasticGrid.getExamReportAdditionalAttributesRows()) {
            if (examReportAdditionalAttributesRow.getAttribute() != examGridRowAttribute) {
                continue;
            }

            for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow.getExamReportMarksColumns()) {
                if(examReportMarksColumn.isHide()) {
                    continue;
                }
                if (examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM) {
                    columnExamMetaDataMap.put(examReportMarksColumn.getId(), examReportMarksColumn.getExamMetaData());
                }
            }
        }
        return columnExamMetaDataMap;
    }

    public List<ExamReportData> getBulkClassExamReportData(int instituteId, int academicSessionId, UUID standardId,
                                                           Integer sectionId, ExamReportStructure examReportStructure, String reportType, boolean onlyEnrolledStudents,List<UUID> studentIds) {
        final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId,
                academicSessionId, standardId);
        Set<StudentStatus> studentStatusSet = getStudentStatus(onlyEnrolledStudents);

        List<Student> students = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIds ,studentStatusSet);
        if (CollectionUtils.isEmpty(students)) {
            return new ArrayList<>();
        }
        return getExamReportDataWithRank(instituteId, academicSessionId, standardId, sectionId, students,
                examReportStructure, reportType, courseTypeExamGrades);

//        return getStudentExamReportData(students,instituteId,academicSessionId,standardId,sectionId,examReportStructure,courseTypeExamGrades,reportType,false);
    }

    public ExamReportData getExamReportData(int instituteId, Student student, ExamReportStructure examReportStructure,
                                            String reportType, boolean computeRank) {

        final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId,
                student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());

        if (!computeRank) {
            return getExamReportDataWithoutRank(instituteId, student, examReportStructure, reportType,
                    courseTypeExamGrades);
        }

        List<ExamReportData> examReportDataList = getExamReportDataWithRank(instituteId, student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(), CollectionUtils.isEmpty(
                        student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0)
                                .getSectionId(), Arrays.asList(student), examReportStructure, reportType, courseTypeExamGrades);

        return CollectionUtils.isEmpty(examReportDataList) ? null : examReportDataList.get(0);
    }

    private List<ExamReportData> getExamReportDataWithRank(int instituteId, int academicSessionId, UUID standardId,
                                                           Integer sectionId, List<Student> studentList,
                                                           ExamReportStructure examReportCardMetaData, String reportType,
                                                           Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {
        final ClassExamData classExamData = getClassExamReportMarksGrid(instituteId,
                standardId, sectionId == null ? null : new HashSet<>(Arrays.asList(sectionId)), academicSessionId, examReportCardMetaData, courseTypeExamGrades);

        final Map<UUID, List<StudentExamMarksDetails>> classEffectiveAdditionalExamMarks = fetchAdditionalExamMarksDetails(
                instituteId, examReportCardMetaData);

        final Map<UUID, Double> classPercentMap = new HashMap<>();
        final Map<UUID, Map<UUID, Double>> courseWiseClassPercentMap = new HashMap<>();
        final Map<UUID, Map<UUID, Double>> courseWiseClassObtainedMarksMap = new HashMap<>();

        //StudentId, ExamId, Percentage
        final Map<UUID, Map<UUID, Double>> studentExamPercentageMap = new HashMap<>();
        //StudentId, ExamId, ExamResultStatus
        final Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap = new HashMap<>();

        List<ExamReportData> requiredStudentExamReportDataList = new ArrayList<>();
        for(Student student : studentList) {
            ExamReportData requiredStudentExamReportData = null;
            for (final Entry<UUID, Map<CourseType, ExamReportMarksGrid>> studentLevelEntry : classExamData
                    .getClassExamReportMarksGridMap().entrySet()) {
                final UUID studentId = studentLevelEntry.getKey();

                final List<StudentExamMarksDetails> studentExamMarksDetails = getStudentMarksDetails(instituteId, studentId,
                        classEffectiveAdditionalExamMarks, classExamData);

                if(!studentExamPercentageMap.containsKey(studentId)) {
                    studentExamPercentageMap.put(studentId, new HashMap<>());
                }

                if(!studentExamResultStatusMap.containsKey(studentId)) {
                    studentExamResultStatusMap.put(studentId, new HashMap<>());
                }

                final ExamReportData examReportData = computeExamReportMarksData(instituteId, examReportCardMetaData,
                        studentLevelEntry.getValue(), courseTypeExamGrades, student, studentExamMarksDetails, studentExamPercentageMap.get(studentId),
                        studentExamResultStatusMap.get(studentId));
                if (studentId.equals(student.getStudentId())) {
                    requiredStudentExamReportData = examReportData;
                }
                if (examReportData.getPercentage() != null && Double.compare(examReportData.getPercentage(), 0d) >= 0) {
                    /**
                     * for institute 10225, 10226, 10227, 10228, we need to show rank for all students irrespective of the result status
                     */
                    if(instituteId == 10225 || instituteId == 10226 || instituteId == 10227 || instituteId == 10228) {
                        classPercentMap.put(studentId, examReportData.getPercentage());
                    } else {
                        /**
                         * rank will only be assigned when result status is PASS and PASS_WITH_GRACE
                         */
                        if (examReportData.getExamResultStatus() == ExamResultStatus.PASS
                                || examReportData.getExamResultStatus() == ExamResultStatus.PASS_WITH_GRACE) {
                            classPercentMap.put(studentId, examReportData.getPercentage());
                        }
                    }
                }
                collectClassStats(examReportCardMetaData, courseWiseClassPercentMap, courseWiseClassObtainedMarksMap, studentId, examReportData);
            }
            if(!CollectionUtils.isEmpty(classExamData.getAdditionalClassExamReportMarksGridMap())){
                for(final Entry<UUID, Map<CourseType, ExamReportMarksGrid>> studentLevelEntry : classExamData
                .getAdditionalClassExamReportMarksGridMap().entrySet()){
                    final UUID studentId = studentLevelEntry.getKey();
                    if (studentId.equals(student.getStudentId())) {
                        if(requiredStudentExamReportData != null){
                            requiredStudentExamReportData.setAdditionalCourseTypeExamReportMarksGrid(studentLevelEntry.getValue());
                        }
                    }
                }
            }

            if (requiredStudentExamReportData == null) {
                logger.error("Could not compute student {} report card marks", student.getStudentId());
                requiredStudentExamReportDataList.add(new ExamReportData(Student.getStudentLite(student)));
                break;
            }
            if (requiredStudentExamReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC) != null) {
                Map<String, Map<UUID, Map<AttendanceStatus, Integer>>> columnStudentAttendanceMap = getColumnStudentAttendanceMap(instituteId,
                        student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                        Collections.singleton(student.getStudentId()),
                        requiredStudentExamReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC));
                populateExamAttendanceColumns(requiredStudentExamReportData.getCourseTypeExamReportMarksGrid(), student.getStudentId(), columnStudentAttendanceMap);
            }
            Map<String, Map<UUID, Integer>> columnStudentRankMap = getColumnStudentRankMap(requiredStudentExamReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC), studentExamPercentageMap, studentExamResultStatusMap, instituteId);
            populateExamWiseRankColumns(requiredStudentExamReportData.getCourseTypeExamReportMarksGrid(), student.getStudentId(), columnStudentRankMap);
            updateExamReportData(instituteId, student, reportType, requiredStudentExamReportData);
            updateClassStats(student.getStudentId(), examReportCardMetaData, courseWiseClassPercentMap, courseWiseClassObtainedMarksMap, requiredStudentExamReportData, instituteId);
            IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
            requiredStudentExamReportData.setRank(rankCalculator.computeRank(classPercentMap, student.getStudentId()));
            requiredStudentExamReportDataList.add(requiredStudentExamReportData);
        }

        return requiredStudentExamReportDataList;
    }

    private static void collectClassStats(ExamReportStructure examReportCardMetaData, Map<UUID, Map<UUID, Double>> courseWiseClassPercentMap, Map<UUID, Map<UUID, Double>> courseWiseClassObtainedMarksMap, UUID studentId, ExamReportData examReportData) {
        for (Entry<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGridEntry : examReportData.getCourseTypeExamReportMarksGrid().entrySet()) {
            CourseType courseType = courseTypeExamReportMarksGridEntry.getKey();
            boolean computeClassStats = (courseType == CourseType.SCHOLASTIC &&
                    examReportCardMetaData.getExamReportStructureMetaData().isShowScholasticCourseClassMetrics()) ||
                    (courseType == CourseType.COSCHOLASTIC &&
                            examReportCardMetaData.getExamReportStructureMetaData().isShowCoScholasticCourseClassMetrics());
            if (computeClassStats) {
                collectClassStatsData(studentId, courseWiseClassPercentMap, courseWiseClassObtainedMarksMap, courseTypeExamReportMarksGridEntry);
            }
        }
    }

    private void updateClassStats(UUID studentId, ExamReportStructure examReportCardMetaData, Map<UUID, Map<UUID, Double>> courseWiseClassPercentMap, Map<UUID, Map<UUID, Double>> courseWiseClassObtainedMarksMap, ExamReportData requiredStudentExamReportData, int instituteId) {
        for (Entry<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGridEntry : requiredStudentExamReportData.getCourseTypeExamReportMarksGrid().entrySet()) {
            CourseType courseType = courseTypeExamReportMarksGridEntry.getKey();
            boolean computeClassStats = (courseType == CourseType.SCHOLASTIC &&
                    examReportCardMetaData.getExamReportStructureMetaData().isShowScholasticCourseClassMetrics()) ||
                    (courseType == CourseType.COSCHOLASTIC &&
                            examReportCardMetaData.getExamReportStructureMetaData().isShowCoScholasticCourseClassMetrics());
            if (computeClassStats) {
                ExamReportMarksGrid scholasticGrid = courseTypeExamReportMarksGridEntry.getValue();
                for (ExamReportCourseMarksRow examReportCourseMarksRow : scholasticGrid.getExamReportCourseMarksRows()) {
                    UUID courseId = examReportCourseMarksRow.getCourse().getCourseId();
                    Map<UUID, Double> courseClassPercentMap = courseWiseClassPercentMap.getOrDefault(courseId, new HashMap<>());
                    Map<UUID, Double> courseClassObtainedMarksMap = courseWiseClassObtainedMarksMap.getOrDefault(courseId, new HashMap<>());
                    IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
                    Integer courseRank = MapUtils.isEmpty(courseClassPercentMap) ? null :rankCalculator.computeRank(courseClassPercentMap, studentId);
                    Double courseClassAvg = MapUtils.isEmpty(courseClassObtainedMarksMap) ? null : ExamMarksUtils.getObtainedMarksAvg(courseClassObtainedMarksMap);
                    Double courseClassMax = MapUtils.isEmpty(courseClassObtainedMarksMap) ? null : ExamMarksUtils.getMaxObtainedMarks(courseClassObtainedMarksMap);

                    for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow.getExamReportCourseMarksColumns()) {
                        switch (examReportCourseMarksColumn.getExamReportGridColumnType()) {
                            case COURSE_RANK:
                                examReportCourseMarksColumn.setColumnValue(courseRank == null ? null : String.valueOf(courseRank));
                                break;
                            case CLASS_COURSE_AVG:
                                if(examReportCourseMarksColumn.getRoundOff() == null || !examReportCourseMarksColumn.getRoundOff()) {
                                    examReportCourseMarksColumn.setColumnValue(courseClassAvg == null ? null : String.valueOf(NumberUtils.formatDouble(courseClassAvg, 1)));
                                } else {
                                    examReportCourseMarksColumn.setColumnValue(courseClassAvg == null ? null :  String.valueOf(Math.round(courseClassAvg)));
                                }

                                break;
                            case CLASS_COURSE_HIGH:
                                if(examReportCourseMarksColumn.getRoundOff() == null || !examReportCourseMarksColumn.getRoundOff()) {
                                    examReportCourseMarksColumn.setColumnValue(courseClassMax == null ? null : String.valueOf(NumberUtils.formatDouble(courseClassMax, 1)));
                                } else {
                                    examReportCourseMarksColumn.setColumnValue(courseClassMax == null ? null :  String.valueOf(Math.round(courseClassMax)));
                                }
                                break;
                        }
                    }
                }
            }
        }
    }

    private static void collectClassStatsData(UUID studentId, Map<UUID, Map<UUID, Double>> courseWiseClassPercentMap,
                                              Map<UUID, Map<UUID, Double>> courseWiseClassObtainedMarksMap, Entry<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGridEntry) {
        ExamReportMarksGrid scholasticGrid = courseTypeExamReportMarksGridEntry.getValue();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : scholasticGrid.getExamReportCourseMarksRows()) {
            UUID courseId = examReportCourseMarksRow.getCourse().getCourseId();
            if(!courseWiseClassPercentMap.containsKey(courseId)){
                courseWiseClassPercentMap.put(courseId, new HashMap<>());
            }

            if(!courseWiseClassObtainedMarksMap.containsKey(courseId)){
                courseWiseClassObtainedMarksMap.put(courseId, new HashMap<>());
            }

            Map<UUID, Double> classPercentMap = courseWiseClassPercentMap.get(courseId);
            Map<UUID, Double> classObtainedMarksMap = courseWiseClassObtainedMarksMap.get(courseId);

            String classCourseMarkColId = getClassCourseMarkColId(examReportCourseMarksRow);

            for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow.getExamReportCourseMarksColumns()) {
                if (examReportCourseMarksColumn.getId().equals(classCourseMarkColId)) {
                    // Assuming only total column should be given for class stats
                    for(ExamDimensionObtainedValues examDimensionObtainedValue : examReportCourseMarksColumn.getExamDimensionObtainedValuesList()){
                        if(examDimensionObtainedValue.getExamDimension().isTotal()){
                            if(examDimensionObtainedValue.getObtainedMarksFraction() != null){
                                classPercentMap.put(studentId, examDimensionObtainedValue.getObtainedMarksFraction());
                            }

                            if(examDimensionObtainedValue.getObtainedMarks() != null){
                                classObtainedMarksMap.put(studentId, examDimensionObtainedValue.getObtainedMarks());
                            }
                        }
                    }
                    break;
                }
            }
        }
    }

    private static String getClassCourseMarkColId(ExamReportCourseMarksRow examReportCourseMarksRow) {
        for (ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksRow.getExamReportCourseMarksColumns()) {
            switch (examReportCourseMarksColumn.getExamReportGridColumnType()) {
                // course metrics col must be same for all these class level computation columns
                case COURSE_RANK:
                case CLASS_COURSE_AVG:
                case CLASS_COURSE_HIGH:
                    return examReportCourseMarksColumn.getCourseWiseClassMetricsComputeColumn();
                default:
                    break;
            }
        }
        return null;
    }

    private List<StudentExamMarksDetails> getStudentMarksDetails(int instituteId, UUID studentId,
                                                                 Map<UUID, List<StudentExamMarksDetails>> classEffectiveExamMarks, ClassExamData classExamData) {
        final List<StudentExamMarksDetails> studentExamMarksDetails = new ArrayList<>();

        if (!CollectionUtils.isEmpty(classExamData.getClassExamMarks().get(studentId))) {
            studentExamMarksDetails.addAll(classExamData.getClassExamMarks().get(studentId));
        }

        if (!CollectionUtils.isEmpty(classEffectiveExamMarks.get(studentId))) {
            studentExamMarksDetails.addAll(classEffectiveExamMarks.get(studentId));
        }
        return studentExamMarksDetails;
    }

    private ExamReportData getExamReportDataWithoutRank(int instituteId, Student student,
                                                        ExamReportStructure examReportCardMetaData, String reportType,
                                                        Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {
        final StudentExamData studentExamData = getExamReportMarksGrid(instituteId, student, examReportCardMetaData,
                courseTypeExamGrades);

        final List<StudentExamMarksDetails> studentExamMarksDetails = getStudentMarksDetails(instituteId, student,
                examReportCardMetaData, studentExamData);

        //StudentId, ExamId, Percentage
        final Map<UUID, Map<UUID, Double>> studentExamPercentageMap = new HashMap<>();
        //StudentId, ExamId, ExamResultStatus
        final Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap = new HashMap<>();

        UUID studentId = student.getStudentId();
        if(!studentExamPercentageMap.containsKey(studentId)) {
            studentExamPercentageMap.put(studentId, new HashMap<>());
        }

        if(!studentExamResultStatusMap.containsKey(studentId)) {
            studentExamResultStatusMap.put(studentId, new HashMap<>());
        }

        final ExamReportData examReportData = computeExamReportMarksData(instituteId, examReportCardMetaData,
                studentExamData.getCourseTypeExamReportMarksGrid(), courseTypeExamGrades, student,
                studentExamMarksDetails, studentExamPercentageMap.get(studentId), studentExamResultStatusMap.get(studentId));

        examReportData.setAdditionalCourseTypeExamReportMarksGrid(studentExamData.getAdditionalCourseTypeExamReportMarksGrid());

        if (examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC) != null) {
            Map<String, Map<UUID, Map<AttendanceStatus, Integer>>> columnStudentAttendanceMap = getColumnStudentAttendanceMap(instituteId,
                    student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                    Collections.singleton(student.getStudentId()),
                    examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC));
            populateExamAttendanceColumns(examReportData.getCourseTypeExamReportMarksGrid(), student.getStudentId(), columnStudentAttendanceMap);
        }

        Map<String, Map<UUID, Integer>> columnStudentRankMap = getColumnStudentRankMap(examReportData.getCourseTypeExamReportMarksGrid().get(CourseType.SCHOLASTIC), studentExamPercentageMap, studentExamResultStatusMap, instituteId);
        populateExamWiseRankColumns(examReportData.getCourseTypeExamReportMarksGrid(), student.getStudentId(), columnStudentRankMap);

        updateExamReportData(instituteId, student, reportType, examReportData);

        return examReportData;
    }

    private List<StudentExamMarksDetails> getStudentMarksDetails(int instituteId, Student student,
                                                                 ExamReportStructure examReportCardMetaData, final StudentExamData studentExamData) {
        final List<StudentExamMarksDetails> studentExamMarksDetails = new ArrayList<>();
        final Map<UUID, List<StudentExamMarksDetails>> classEffectiveExamMarks = fetchAdditionalExamMarksDetails(
                instituteId, examReportCardMetaData);
        if (!CollectionUtils.isEmpty(studentExamData.getStudentExamMarksDetails())) {
            studentExamMarksDetails.addAll(studentExamData.getStudentExamMarksDetails());
        }

        if (!CollectionUtils.isEmpty(classEffectiveExamMarks.get(student.getStudentId()))) {
            studentExamMarksDetails.addAll(classEffectiveExamMarks.get(student.getStudentId()));
        }
        return studentExamMarksDetails;
    }

    /**
     * To get marks for exams which are not included in header. There are custom
     * report cards required where exams are not part of standard header but
     * need to show in report card.
     *
     * @param instituteId
     * @param examReportCardMetaData
     * @return
     */
    private Map<UUID, List<StudentExamMarksDetails>> fetchAdditionalExamMarksDetails(int instituteId,
                                                                                     ExamReportStructure examReportCardMetaData) {
        if (examReportCardMetaData.getExamReportCourseStructure() == null) {
            return new HashMap<>();
        }

        final Set<UUID> exams = new HashSet<>();
        if (examReportCardMetaData.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) != null
                && !CollectionUtils.isEmpty(examReportCardMetaData.getExamReportCourseStructure()
                .get(CourseType.SCHOLASTIC).getAdditionalExamMarksToFetch())) {
            exams.addAll(examReportCardMetaData.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
                    .getAdditionalExamMarksToFetch());
        }

        if (examReportCardMetaData.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC) != null
                && !CollectionUtils.isEmpty(examReportCardMetaData.getExamReportCourseStructure()
                .get(CourseType.COSCHOLASTIC).getAdditionalExamMarksToFetch())) {
            exams.addAll(examReportCardMetaData.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC)
                    .getAdditionalExamMarksToFetch());
        }

        final Map<UUID, List<StudentExamMarksDetails>> classEffectiveExamMarks = new HashMap<>();
        boolean filterOnBasisOfMarksSubmitted = userPreferenceSettings.getExaminationPreferences(instituteId).isReportCardViewOnlySubmittedMarks();

        for (final UUID examId : exams) {
            final List<StudentExamMarksDetails> classExamMarksDetails = examinationManager
                    .getResolvedClassMarks(instituteId, examId, null, null, filterOnBasisOfMarksSubmitted);

            for (final StudentExamMarksDetails studentExamMarksDetails : classExamMarksDetails) {
                if (!classEffectiveExamMarks.containsKey(studentExamMarksDetails.getStudent().getStudentId())) {
                    classEffectiveExamMarks.put(studentExamMarksDetails.getStudent().getStudentId(), new ArrayList<>());
                }
                classEffectiveExamMarks.get(studentExamMarksDetails.getStudent().getStudentId())
                        .add(studentExamMarksDetails);
            }
        }

        return classEffectiveExamMarks;
    }

    /**
     * Updates the data like report card variables (attendance, remarks,
     * promoted students etc)
     *
     * @param instituteId
     * @param student
     * @param reportType
     * @param examReportData
     */
    private void updateExamReportData(int instituteId, Student student, String reportType,
                                      final ExamReportData examReportData) {

        final Standard promotedStandard = studentManager.getNextPropmotionStandard(instituteId, student);

        final StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId,
                student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());

        final ReportCardVariableDetails reportCardVariableDetails = getStudentsReportCardVariables(instituteId,
                student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(), reportType);

        final Map<UUID, StudentReportCardVariableDetails> reportCardVariablesDataMap = getReportCardVariablesDataMap(
                reportCardVariableDetails);

        final List<StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailList = getStudentPersonalityTraits(instituteId,
                student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(),
                CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
                        student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId(),
                student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
                reportType, null, StudentSortingParameters.ADMISSION_NUMBER);

        final Map<UUID, StudentPersonalityTraitsDetails> studentPersonalityTraitsResponseMap = getStudentPersonalityTraitsResponseMap(
                studentPersonalityTraitsDetailList);

        updateExamReportData(examReportData.getStudentLite(), promotedStandard, standardMetaData, reportCardVariableDetails,
                reportCardVariablesDataMap, studentPersonalityTraitsResponseMap, examReportData);

    }

    private Map<UUID, StudentPersonalityTraitsDetails> getStudentPersonalityTraitsResponseMap(
            List<StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailList) {
        final Map<UUID, StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailMap = new HashMap<>();
        if(CollectionUtils.isEmpty(studentPersonalityTraitsDetailList)){
            return studentPersonalityTraitsDetailMap;
        }

        for (final StudentPersonalityTraitsDetails studentPersonalityTraitsDetails : studentPersonalityTraitsDetailList) {
            studentPersonalityTraitsDetailMap.put(studentPersonalityTraitsDetails.getStudent().getStudentId(),
                    studentPersonalityTraitsDetails);
        }
        return studentPersonalityTraitsDetailMap;
    }

    private Map<UUID, StudentReportCardVariableDetails> getReportCardVariablesDataMap(
            ReportCardVariableDetails reportCardVariableDetails) {
        final Map<UUID, StudentReportCardVariableDetails> reportCardVariablesDataMap = new HashMap<>();
        if(reportCardVariableDetails == null){
            return reportCardVariablesDataMap;
        }

        for (final StudentReportCardVariableDetails studentReportCardVariableDetails : reportCardVariableDetails
                .getStudentReportCardVariableDetails()) {
            reportCardVariablesDataMap.put(studentReportCardVariableDetails.getStudent().getStudentId(),
                    studentReportCardVariableDetails);
        }
        return reportCardVariablesDataMap;
    }

    private void updateExamReportData(StudentLite studentLite, Standard promotedStandard, StandardMetadata standardMetaData,
                                      ReportCardVariableDetails reportCardVariableDetails,
                                      Map<UUID, StudentReportCardVariableDetails> studentReportCardVariableDetailMap,
                                      Map<UUID, StudentPersonalityTraitsDetails> studentPersonalityTraitsResponseMap,
                                      final ExamReportData examReportData) {

        if (examReportData.getExamResultStatus() == ExamResultStatus.PASS || examReportData.getExamResultStatus() == ExamResultStatus.PASS_WITH_GRACE || examReportData.getExamResultStatus() == ExamResultStatus.CONDITIONALLY_PROMOTED || examReportData.getExamResultStatus() == ExamResultStatus.PROMOTED) {
            if (promotedStandard != null) {
                examReportData.setPromotedTo(promotedStandard);
            }
        }
        examReportData.setStandardMetaData(standardMetaData);

        if(studentLite == null) {
            return;
        }

        final StudentReportCardVariableDetails studentReportCardVariablesData = studentReportCardVariableDetailMap
                .get(studentLite.getStudentId());
        if (studentReportCardVariablesData != null) {
            final Integer workingDays = studentReportCardVariablesData.getTotalDays() == null ? null
                    : (int) Math.round(studentReportCardVariablesData.getTotalDays());
            final Integer attendedDays = studentReportCardVariablesData.getAttendedDays() == null ? null
                    : (int) Math.round(studentReportCardVariablesData.getAttendedDays());

            examReportData.setTotalWorkingDays(workingDays);
            examReportData.setTotalAttendedDays(attendedDays);
            /**
             * https://embrate.atlassian.net/browse/PD-2637
             */
            examReportData.setHeight(StringUtils.isBlank(studentReportCardVariablesData.getHeight())
                    ? studentLite.getStudentSessionData().getHeight() : studentReportCardVariablesData.getHeight());
            examReportData.setWeight(StringUtils.isBlank(studentReportCardVariablesData.getWeight())
                    ? studentLite.getStudentSessionData().getWeight() : studentReportCardVariablesData.getWeight());
            
            // Assuming that report card total grade is already setup
            examReportData.setRemarks(StringUtils.isBlank(studentReportCardVariablesData.getRemarks()) ? examReportData.getTotalGrade() == null
                    ? null : examReportData.getTotalGrade().getRemarks() : studentReportCardVariablesData.getRemarks());
            examReportData.setPrincipalRemarks(studentReportCardVariablesData.getPrincipalRemarks());
            examReportData.setDateOfResultDeclaration(studentReportCardVariablesData.getDateOfResultDeclaration());
        }

        final StudentPersonalityTraitsDetails studentPersonalityTraitsDetails = studentPersonalityTraitsResponseMap
                .get(studentLite.getStudentId());
        if (studentPersonalityTraitsDetails != null) {
            examReportData.setStudentPersonalityTraitsResponseList(studentPersonalityTraitsDetails.getStudentPersonalityTraitsResponseList());
        }

    }

    private ExamReportData computeExamReportMarksData(int instituteId, ExamReportStructure examReportStructure,
                                                      final Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap,
                                                      Map<CourseType, List<ExamGrade>> courseTypeExamGrades, Student student,
                                                      List<StudentExamMarksDetails> studentExamMarksDetails, Map<UUID, Double> studentExamWisePercentage,
                                                      Map<UUID, ExamResultStatus> studentExamResultStatusMap) {
        final ExamReportData examReportMarksData = new ExamReportData(Student.getStudentLite(student));
        examReportMarksData.setCourseTypeExamReportMarksGrid(examReportMarksGridMap);
        examReportMarksData.setExamReportStructure(examReportStructure);
        examReportMarksData.setCourseTypeExamGrades(courseTypeExamGrades);

        final List<StudentExamMarksDetailsLite> studentExamMarksDetailsLiteList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(studentExamMarksDetails)) {
            for (final StudentExamMarksDetails studentExamMarksDetailsEntry : studentExamMarksDetails) {
                studentExamMarksDetailsLiteList.add(new StudentExamMarksDetailsLite(studentExamMarksDetailsEntry));
            }
        }
        examReportMarksData.setStudentExamMarksDetailsLiteList(studentExamMarksDetailsLiteList);

        Double totalOriginalMaxMarks = null;
        Double totalMaxMarks = null;
        Double totalObtainedMarks = null;
        Double totalObtainedGradeValue = null;

        if (examReportMarksGridMap.get(CourseType.SCHOLASTIC) != null) {
            Pair<Triplet<Double, Double, Double>, Double> totalValues = getTotalMarks(
                    examReportStructure.getExamReportCourseStructure() == null ? null
                            : examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC),
                    examReportMarksGridMap.get(CourseType.SCHOLASTIC).getExamReportTotalMarksColumns());

            final Triplet<Double, Double, Double> totalMarks = totalValues.getFirst();
            totalOriginalMaxMarks = totalMarks.getFirst();
            totalMaxMarks = totalMarks.getSecond();
            totalObtainedMarks = totalMarks.getThird();
            totalObtainedGradeValue = totalValues.getSecond();
        }

        // SEARCH_EXP : AdjustedMaxMarksUsage
        if (courseTypeExamGrades.containsKey(CourseType.SCHOLASTIC) && totalMaxMarks != null && totalMaxMarks > 0d && totalObtainedMarks != null) {
            final ExamGrade examGrade = ExamMarksUtils.getExamGradeByPercentage(
                    courseTypeExamGrades.get(CourseType.SCHOLASTIC), totalObtainedMarks / totalMaxMarks);
            examReportMarksData.setTotalGrade(examGrade);
        } else if (courseTypeExamGrades.containsKey(CourseType.SCHOLASTIC) && totalObtainedGradeValue != null) {
            final ExamGrade examGrade = ExamMarksUtils.getExamGradeByValue(
                    courseTypeExamGrades.get(CourseType.SCHOLASTIC), totalObtainedGradeValue);
            examReportMarksData.setTotalGrade(examGrade);
        }

        examReportMarksData.setTotalObtainedMarks(totalObtainedMarks);
        examReportMarksData.setTotalMaxMarks(totalMaxMarks);
        examReportMarksData.setTotalOriginalMaxMarks(totalOriginalMaxMarks);

        String calcId = examReportStructure.getExamReportResultConfigs() == null ? null : examReportStructure.getExamReportResultConfigs().getResultCalculatorId();
        IExamResultCalculator examResultCalculator = examResultCalculatorFactory.
                getExamResultCalculator(calcId);
        examResultCalculator.updateExamResult(examReportStructure, examReportMarksData, studentExamWisePercentage,
                studentExamResultStatusMap);
//        final ExamResultStatus examResultStatus = examReportMarksData.getPercentage() != null
//                && examReportMarksData.getPercentage() >= PASSING_THRESHOLD * 100 ? ExamResultStatus.PASS
//                : ExamResultStatus.FAIL;
//        examReportMarksData.setExamResultStatus(examResultStatus);

        return examReportMarksData;

    }

    private static void populateExamAttendanceColumns(Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap, UUID studentId, Map<String, Map<UUID, Map<AttendanceStatus, Integer>>> columnStudentAttendanceMap) {
        if(examReportMarksGridMap.get(CourseType.SCHOLASTIC) == null){
            return;
        }

        for (ExamReportAdditionalAttributesRow examReportAdditionalAttributesRow : examReportMarksGridMap.get(CourseType.SCHOLASTIC).getExamReportAdditionalAttributesRows()) {
            if (examReportAdditionalAttributesRow.getAttribute() != ExamGridRowAttribute.ATTENDANCE) {
                continue;
            }

            for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow.getExamReportMarksColumns()) {
                if(examReportMarksColumn.isHide()) {
                    continue;
                }
                if (examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM) {
                    Map<UUID, Map<AttendanceStatus, Integer>> columnAttendanceMap = columnStudentAttendanceMap.get(examReportMarksColumn.getId());
                    if (MapUtils.isEmpty(columnAttendanceMap)) {
                        continue;
                    }
                    Map<AttendanceStatus, Integer> studentAttendanceMap = columnAttendanceMap.get(studentId);
                    if (MapUtils.isEmpty(studentAttendanceMap)) {
                        continue;
                    }

                    String noOfMeetings = null;
                    String noOfPresent = null;

                    Integer presentCount = studentAttendanceMap.get(AttendanceStatus.PRESENT);
                    Integer absentCount = studentAttendanceMap.get(AttendanceStatus.ABSENT);
                    Integer leaveCount = studentAttendanceMap.get(AttendanceStatus.LEAVE);
                    Integer halfDayCount = studentAttendanceMap.get(AttendanceStatus.HALF_DAY);
                    Integer totalCount = NumberUtils.addValues(halfDayCount, NumberUtils.addValues(leaveCount, NumberUtils.addValues(presentCount, absentCount)));
                    Double presentCountTotal = NumberUtils.addValues(presentCount * 1.0, halfDayCount / 2d);
                    noOfMeetings = totalCount == null ? null : String.valueOf(totalCount);
                    noOfPresent = presentCountTotal == null ? "-" : NumberUtils.formatDouble(presentCountTotal, 1);

                    if (noOfMeetings != null) {
                        examReportMarksColumn.setColumnValue(noOfPresent + "/" + noOfMeetings);
                    }
                }
            }
        }
    }

    private static void populateExamWiseRankColumns(Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap, UUID studentId, Map<String, Map<UUID, Integer>> columnStudentRankMap) {
        if(examReportMarksGridMap.get(CourseType.SCHOLASTIC) == null){
            return;
        }

        for (ExamReportAdditionalAttributesRow examReportAdditionalAttributesRow : examReportMarksGridMap.get(CourseType.SCHOLASTIC).getExamReportAdditionalAttributesRows()) {
            if (examReportAdditionalAttributesRow.getAttribute() != ExamGridRowAttribute.RANK) {
                continue;
            }

            for (ExamReportMarksColumn examReportMarksColumn : examReportAdditionalAttributesRow.getExamReportMarksColumns()) {
                if(examReportMarksColumn.isHide()) {
                    continue;
                }
                if (examReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM) {
                    Map<UUID, Integer> rankMap = columnStudentRankMap.get(examReportMarksColumn.getId());
                    if (MapUtils.isEmpty(rankMap)) {
                        continue;
                    }
                    Integer rank = rankMap.get(studentId);
                    if (rank == null) {
                        continue;
                    }
                    examReportMarksColumn.setColumnValue(rank.toString());
                }
            }
        }
    }

    private Pair<Triplet<Double, Double, Double>, Double> getTotalMarks(ExamReportCourseStructure examReportCourseStructure,
                                                          List<ExamReportCourseMarksColumn> examReportCourseMarksColumns) {
        Double totalMaxMarks = null;
        Double totalOriginalMaxMarks = null;
        Double totalObtainedMarks = null;
        Double totalGradeValueSum = null;
        Integer totalGradeValueCount = null;

        final boolean useComputeColumns = examReportCourseStructure != null
                && !CollectionUtils.isEmpty(examReportCourseStructure.getNetTotalComputationColumns());

        for (final ExamReportCourseMarksColumn examReportCourseMarksColumn : examReportCourseMarksColumns) {

            /**
             * If using computation columns then skip ids which are not part of
             * it
             */
            if (useComputeColumns && !examReportCourseStructure.getNetTotalComputationColumns()
                    .contains(examReportCourseMarksColumn.getId())) {
                continue;
            }
            /**
             * Marks summation will be only for exam columns
             */
            if (!useComputeColumns
                    && examReportCourseMarksColumn.getExamReportGridColumnType() != ExamReportGridColumnType.EXAM
                    && examReportCourseMarksColumn
                    .getExamReportGridColumnType() != ExamReportGridColumnType.EXAM_GRADE
                    && examReportCourseMarksColumn
                    .getExamReportGridColumnType() != ExamReportGridColumnType.SHOW_GRADE_WITH_MARKS_GRID) {
                continue;
            }

            if (examReportCourseMarksColumn.getExamDimensionObtainedValuesList().size() == 1) {
                totalOriginalMaxMarks = ExamMarksUtils.addValues(totalOriginalMaxMarks,
                        examReportCourseMarksColumn.getExamDimensionObtainedValuesList().get(0).getOriginalMaxMarks());
                totalMaxMarks = ExamMarksUtils.addValues(totalMaxMarks,
                        examReportCourseMarksColumn.getExamDimensionObtainedValuesList().get(0).getMaxMarks());
                totalObtainedMarks = ExamMarksUtils.addValues(totalObtainedMarks,
                        examReportCourseMarksColumn.getExamDimensionObtainedValuesList().get(0).getObtainedMarks());

                ExamGrade obtainedExamGrade = examReportCourseMarksColumn.getExamDimensionObtainedValuesList().get(0).getObtainedGrade();
                totalGradeValueSum = ExamMarksUtils.addValues(totalGradeValueSum, obtainedExamGrade == null ? null : obtainedExamGrade.getGradeValue());
                totalGradeValueCount = ExamMarksUtils.addValues(totalGradeValueCount, obtainedExamGrade == null ? null : 1);

            } else {
                for (final ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumn
                        .getExamDimensionObtainedValuesList()) {
                    if (examDimensionObtainedValues.getExamDimension().isTotal()) {
                        continue;
                    }
                    totalOriginalMaxMarks = ExamMarksUtils.addValues(totalOriginalMaxMarks,
                            examDimensionObtainedValues.getOriginalMaxMarks());
                    totalMaxMarks = ExamMarksUtils.addValues(totalMaxMarks,
                            examDimensionObtainedValues.getMaxMarks());
                    totalObtainedMarks = ExamMarksUtils.addValues(totalObtainedMarks,
                            examDimensionObtainedValues.getObtainedMarks());
                }
            }
        }
        return new Pair<Triplet<Double, Double, Double>, Double>(new Triplet<Double, Double, Double>(totalOriginalMaxMarks, totalMaxMarks, totalObtainedMarks),totalGradeValueSum == null ? null : totalGradeValueSum / totalGradeValueCount);
    }

    public StudentExamData getExamReportMarksGrid(int instituteId, Student student,
                                                  ExamReportStructure examReportStructure, Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {

        final UUID studentId = student.getStudentId();
        final UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
        final int academicSessionId = student.getStudentAcademicSessionInfoResponse().getAcademicSession()
                .getAcademicSessionId();

        final List<StudentExamMarksDetails> studentEffectiveExamMarks = getStudentEffectiveExamMarks(instituteId,
                academicSessionId, standardId, studentId, examReportStructure);

        /**
         * Assuming that atleast 1 exam is present if report card is getting
         * generated
         */
        final Map<CourseType, ExamDimension> courseTypeTotalExamDimensionMap = getCourseTypeTotalDimensions(
                studentEffectiveExamMarks);

        final Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap = getExamReportMarksGrid(instituteId,
                studentId, academicSessionId, examReportStructure, studentEffectiveExamMarks,
                courseTypeTotalExamDimensionMap, courseTypeExamGrades, false);
        
        final Map<CourseType, ExamReportMarksGrid> additionalExamReportMarksGridMap = getExamReportMarksGrid(instituteId,
                studentId, academicSessionId, examReportStructure, studentEffectiveExamMarks,
                courseTypeTotalExamDimensionMap, courseTypeExamGrades, true);
        return new StudentExamData(examReportMarksGridMap, additionalExamReportMarksGridMap, studentEffectiveExamMarks);
    }

    public ClassExamData getClassExamReportMarksGrid(int instituteId, UUID standardId, Set<Integer> sectionIdSet,
                                                 int academicSessionId, ExamReportStructure examReportStructure,
                                                 Map<CourseType, List<ExamGrade>> courseTypeExamGrades) {
    
        final Map<UUID, Map<CourseType, ExamReportMarksGrid>> classExamReportMarksGridMap = new LinkedHashMap<>();
        final Map<UUID, Map<CourseType, ExamReportMarksGrid>> additionalClassExamReportMarksGridMap = new LinkedHashMap<>();

        // Compute all students' exam marks
        final Map<UUID, List<StudentExamMarksDetails>> studentEffectiveExamMarks = getClassEffectiveExamMarks(
                instituteId, academicSessionId, standardId, sectionIdSet, examReportStructure);

        final Map<CourseType, ExamDimension> courseTypeTotalExamDimensionMap = getCourseTypeTotalDimensions(studentEffectiveExamMarks);

        for (final Entry<UUID, List<StudentExamMarksDetails>> studentEntry : studentEffectiveExamMarks.entrySet()) {
            UUID studentId = studentEntry.getKey();
            List<StudentExamMarksDetails> examMarks = studentEntry.getValue();

            // Get regular exam report marks
            classExamReportMarksGridMap.put(studentId, getExamReportMarksGrid(
                    instituteId, studentId, academicSessionId, examReportStructure, examMarks,
                    courseTypeTotalExamDimensionMap, courseTypeExamGrades, false));

            // Check if additional courses exist
            if (hasAdditionalCourses(examReportStructure)) {
                boolean hasAdditionalHeaders = hasAdditionalHeaders(examReportStructure);
                additionalClassExamReportMarksGridMap.put(studentId, getExamReportMarksGrid(
                        instituteId, studentId, academicSessionId, examReportStructure, examMarks,
                        courseTypeTotalExamDimensionMap, courseTypeExamGrades, hasAdditionalHeaders));
            }
        }

        return new ClassExamData(classExamReportMarksGridMap, additionalClassExamReportMarksGridMap, studentEffectiveExamMarks);
    }

    /**
     * Checks if additional courses exist in the exam report structure.
     */
    private boolean hasAdditionalCourses(ExamReportStructure examReportStructure) {
        if (examReportStructure == null || examReportStructure.getExamReportCourseStructure() == null) {
            return false;
        }

        ExamReportCourseStructure scholasticCourse = examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC);
        ExamReportCourseStructure coScholasticCourse = examReportStructure.getExamReportCourseStructure().get(CourseType.COSCHOLASTIC);

        return (scholasticCourse != null && !CollectionUtils.isEmpty(scholasticCourse.getAdditionalCourses())) ||
            (coScholasticCourse != null && !CollectionUtils.isEmpty(coScholasticCourse.getAdditionalCourses()));
    }

    /**
     * Checks if additional headers exist in the exam report structure.
     */
    private boolean hasAdditionalHeaders(ExamReportStructure examReportStructure) {
        if (examReportStructure == null || examReportStructure.getExamReportAdditionalSubjectHeaderStructureColumns() == null) {
            return false;
        }

        List<ExamReportHeaderStructureColumn> scholasticHeaders = 
                examReportStructure.getExamReportAdditionalSubjectHeaderStructureColumns().get(CourseType.SCHOLASTIC);
        List<ExamReportHeaderStructureColumn> coScholasticHeaders = 
                examReportStructure.getExamReportAdditionalSubjectHeaderStructureColumns().get(CourseType.COSCHOLASTIC);

        return !CollectionUtils.isEmpty(scholasticHeaders) || !CollectionUtils.isEmpty(coScholasticHeaders);
    }


    private Map<CourseType, ExamDimension> getCourseTypeTotalDimensions(
            Map<UUID, List<StudentExamMarksDetails>> studentEffectiveExamMarks) {

        for (final Entry<UUID, List<StudentExamMarksDetails>> entry : studentEffectiveExamMarks.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            return getCourseTypeTotalDimensions(entry.getValue());
        }
        return new HashMap<>();
    }

    private Map<CourseType, ExamDimension> getCourseTypeTotalDimensions(
            List<StudentExamMarksDetails> studentEffectiveExamMarks) {
        final Map<CourseType, ExamDimension> courseTypeTotalExamDimensionMap = new HashMap<>();
        if (CollectionUtils.isEmpty(studentEffectiveExamMarks)) {
            return courseTypeTotalExamDimensionMap;
        }

        /**
         * Need to loop over all exams as all course type might not be added as
         * part of all exams
         */
        for (final StudentExamMarksDetails studentExamMarksDetails : studentEffectiveExamMarks) {
            if (courseTypeTotalExamDimensionMap.get(CourseType.SCHOLASTIC) != null
                    && courseTypeTotalExamDimensionMap.get(CourseType.COSCHOLASTIC) != null) {
                return courseTypeTotalExamDimensionMap;
            }

            for (final Entry<CourseType, List<ExamDimensionValues>> courseTypeDimensionListEntry : studentExamMarksDetails
                    .getExamDimensionValues().entrySet()) {
                ExamDimension totalExamDimension = null;

                for (final ExamDimensionValues examDimensionValues : courseTypeDimensionListEntry.getValue()) {
                    /**
                     * Exam dimensions are filtered already , so total dimension
                     * would be applicable one
                     */
                    if (examDimensionValues.getExamDimension().isTotal()) {
                        totalExamDimension = examDimensionValues.getExamDimension();
                        break;
                    }
                }
                courseTypeTotalExamDimensionMap.put(courseTypeDimensionListEntry.getKey(), totalExamDimension);
            }

        }

        return courseTypeTotalExamDimensionMap;
    }


    private Map<CourseType, ExamReportMarksGrid> getExamReportMarksGrid(int instituteId, UUID studentId,
                                                                    int academicSessionId, ExamReportStructure examReportStructure,
                                                                    final List<StudentExamMarksDetails> studentEffectiveExamMarks,
                                                                    Map<CourseType, ExamDimension> courseTypeTotalExamDimensionMap,
                                                                    Map<CourseType, List<ExamGrade>> courseTypeExamGrades, boolean additionalCourses) {

        final Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap = new HashMap<>();
        final List<Course> courses = courseManager.getStudentAssignedCourses(instituteId, studentId, academicSessionId);
        UUID standardId = org.apache.commons.collections4.CollectionUtils.isEmpty(studentEffectiveExamMarks) ? null :
                studentEffectiveExamMarks.get(0).getExamMetaData().getStandardId();
        StandardMetadata standardMetadata = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        logger.debug("student {}, courses {}", studentId, courses);
        
        Map<Integer, ExamDimension> dimensionMap = getExamDimensionMap(instituteId);
        if(additionalCourses && hasAdditionalCourses(examReportStructure)){
            additionalCourses = hasAdditionalHeaders(examReportStructure); 
        }
        else{
            additionalCourses = false;
        }
        // Determine which header structure columns to use
        Map<CourseType, List<ExamReportHeaderStructureColumn>> structureColumns = 
            additionalCourses ? examReportStructure.getExamReportAdditionalSubjectHeaderStructureColumns() : examReportStructure.getExamReportHeaderStructureColumns();

        processExamReportStructure(structureColumns, studentId, examReportStructure, courses, studentEffectiveExamMarks,
                                courseTypeTotalExamDimensionMap, courseTypeExamGrades, dimensionMap, examReportMarksGridMap, additionalCourses,
                standardMetadata.isScholasticGradingEnabled(), standardMetadata.isCoScholasticGradingEnabled());

        return examReportMarksGridMap;
    }

    private void processExamReportStructure(Map<CourseType, List<ExamReportHeaderStructureColumn>> structureColumns, UUID studentId,
                                            ExamReportStructure examReportStructure, List<Course> courses,
                                            List<StudentExamMarksDetails> studentEffectiveExamMarks,
                                            Map<CourseType, ExamDimension> courseTypeTotalExamDimensionMap,
                                            Map<CourseType, List<ExamGrade>> courseTypeExamGrades,
                                            Map<Integer, ExamDimension> dimensionMap,
                                            Map<CourseType, ExamReportMarksGrid> examReportMarksGridMap, boolean additionalCourses,
                                            boolean isScholasticGradingEnabled, boolean isCoScholasticGradingEnabled) {
        for (final Entry<CourseType, List<ExamReportHeaderStructureColumn>> entry : structureColumns.entrySet()) {
            final CourseType courseType = entry.getKey();
            try {
                examReportMarksGridMap.put(courseType,
                        getCourseTypeExamReportMarksGrid(studentId, courseType, entry.getValue(), examReportStructure, courses,
                                studentEffectiveExamMarks, courseTypeTotalExamDimensionMap.get(courseType),
                                courseTypeExamGrades.get(courseType), dimensionMap, additionalCourses,
                                courseType == CourseType.SCHOLASTIC ? isScholasticGradingEnabled : isCoScholasticGradingEnabled));
            } catch (final EmbrateRunTimeException e) {
                logger.warn("Invalid structure configuration for courseType {} , skipping courseType ... ", courseType);
            }
        }
    }


    private Map<Integer, ExamDimension> getExamDimensionMap(int instituteId) {
        Map<Integer, ExamDimension> dimensionMap = EMapUtils.getMap(examinationManager.getExamDimensions(instituteId), new EMapUtils.MapFunction<ExamDimension, Integer, ExamDimension>() {
            @Override
            public Integer getKey(ExamDimension entry) {
                return entry.getDimensionId();
            }

            @Override
            public ExamDimension getValue(ExamDimension entry) {
                return entry;
            }
        });
        return dimensionMap;
    }

    private ExamReportMarksGrid getCourseTypeExamReportMarksGrid(UUID studentId, CourseType courseType,
                                                                 List<ExamReportHeaderStructureColumn> examReportHeaderStructureColumns,
                                                                 ExamReportStructure examReportStructure, List<Course> courses,
                                                                 List<StudentExamMarksDetails> studentEffectiveExamMarks, ExamDimension totalExamDimension,
                                                                 List<ExamGrade> examGrades, Map<Integer,ExamDimension> dimensionMap, boolean additionalCourses,
                                                                 boolean isGradeEnabled) {

        final ExamReportStructureMetaData examReportStructureMetaData = examReportStructure
                .getExamReportStructureMetaData();

        if (totalExamDimension == null) {
            logger.error("Invalid exam state, total dimension is null. student {}, courseType {}", studentId,
                    courseType);
            throw new EmbrateRunTimeException("Invalid exam state, total dimension is null");
        }

        final Map<UUID, ExamMetaData> examMetaDataMap = getExamMetaDataMap(studentEffectiveExamMarks);
        Set<UUID> courseIdset = new HashSet<>();
        if(additionalCourses){
            if(!CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(courseType).getAdditionalCourses())){
                courseIdset.addAll(examReportStructure.getExamReportCourseStructure().get(courseType).getAdditionalCourses());
            }
        }
        final Map<UUID, Map<Integer, ExamDimensionObtainedValues>> headerExamDimensionsMap = getHeaderExamDimensionsMap(
                courseType, studentEffectiveExamMarks, courseIdset, additionalCourses);

        final Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValuesMap = getCourseExamDimensionValuesMap(
                studentEffectiveExamMarks);

        final List<ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksHeaderColumns = new ArrayList<>();
        final List<ExamReportCourseMarksRow> examReportCourseMarksRows = new ArrayList<>();
        final List<ExamReportCourseMarksColumn> examReportCourseTotalMarksColumns = new ArrayList<>();
        final Map<Integer, Set<String>> treeLevelColumnIdsMap = new HashMap<>();
        final Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap = new HashMap<>();

        for (final ExamReportHeaderStructureColumn examReportHeaderStructureColumn : examReportHeaderStructureColumns) {
            /**
             * This try catch is required to skip courses which are not
             * configured for given report card structure exams but present in
             * parent exam. In this case
             * courseExamDimensionValuesMap.get(courseId) don't have exam
             * mapping and whole report card fails
             *
             * This case occurred when parent exam is common and having all
             * subjects but for one of course there are separate child exams
             * which are not part of report card header exams
             */
            try {
                examReportMarksHeaderColumns.add(getExamReportMarksColumn(examReportHeaderStructureColumn,
                        examReportMarksColumnsMap, headerExamDimensionsMap, examMetaDataMap, treeLevelColumnIdsMap, 0,
                        totalExamDimension, new ArrayList<>(), examGrades, totalExamDimension, dimensionMap, isGradeEnabled));
            } catch (final EmbrateRunTimeException e) {
                logger.warn("Invalid structure configuration");
            }
        }

        for (final Course course : courses) {
            if (course.getCourseType() != courseType) {
                continue;
            }
            final UUID courseId = course.getCourseId();
            if (!courseExamDimensionValuesMap.containsKey(courseId)) {
//                logger.info("Exam does not have course {} assigned to student {}", courseId, studentId);
                continue;
            }
            final List<ExamReportMarksColumn<ExamDimensionObtainedValues>> courseExamReportMarksColumns = new ArrayList<>();
            final List<ExamReportMarksColumn<ExamDimensionObtainedValues>> leafColumns = new ArrayList<>();

            final Map<Integer, Set<String>> courseTreeLevelColumnIdsMap = new HashMap<>();
            /**
             * This try catch is required to skip courses which are not
             * configured for given report card structure exams but present in
             * parent exam. In this case
             * courseExamDimensionValuesMap.get(courseId) don't have exam
             * mapping and whole report card fails
             *
             * This case occurred when parent exam is common and having all
             * subjects but for one of course there are separate child exams
             * which are not part of report card header exams
             */
            try {
                for (final ExamReportHeaderStructureColumn examReportHeaderStructureColumn : examReportHeaderStructureColumns) {
                    courseExamReportMarksColumns.add(getExamReportMarksColumn(examReportHeaderStructureColumn,
                            examReportMarksColumnsMap, courseExamDimensionValuesMap.get(courseId), examMetaDataMap,
                            courseTreeLevelColumnIdsMap, 0, totalExamDimension, leafColumns, examGrades, totalExamDimension, dimensionMap, isGradeEnabled));
                }
            } catch (final EmbrateRunTimeException e) {
                logger.warn("Invalid structure configuration for course {} , skipping course ... ", courseId);
                continue;
            }

            final List<ExamReportCourseMarksColumn> examReportCourseMarksColumns = new ArrayList<>();

            for (final ExamReportMarksColumn<ExamDimensionObtainedValues> leafColumn : leafColumns) {
                examReportCourseMarksColumns
                        .add(new ExamReportCourseMarksColumn(leafColumn.getId(), leafColumn.getColorHexCode(),
                                leafColumn.getExamReportGridColumnType(),
                                leafColumn.getRoundOff(),
                                leafColumn.getExamDimensionValues(),
                                leafColumn.isHideTotalDimensionMaxMarks(),
                                leafColumn.isHideRemainingDimensionMaxMarks(),
                                leafColumn.getCourseWiseClassMetricsComputeColumn(),
                                leafColumn.getCourseWiseGainMetricsComputeColumns(), leafColumn.getColumnValue(), leafColumn.isHide()));
            }

            examReportCourseMarksRows.add(new ExamReportCourseMarksRow(course, examReportCourseMarksColumns));
        }

        final Set<UUID> reportCardCourses = getReportCardCourses(examReportStructure, examReportCourseMarksRows,
                courseType);

        for (final ExamReportCourseMarksRow examReportCourseMarksRow : examReportCourseMarksRows) {

            /**
             * Skip additional courses for total row computation
             */
            if (isAdditionalCourse(examReportStructure, courseType,
                    examReportCourseMarksRow.getCourse().getCourseId())) {
                continue;
            }
            /**
             * Course is not mandatory, skip
             */
            if (!CollectionUtils.isEmpty(reportCardCourses)
                    && !reportCardCourses.contains(examReportCourseMarksRow.getCourse().getCourseId())) {
                continue;
            }

            computeTotalMarksRow(examGrades, examReportCourseTotalMarksColumns, examReportCourseMarksRow);

        }

        final List<ExamReportCourseMarksRow> orderedExamReportCourseMarksRows = getOrderedExamReportCourseMarksRows(
                examReportCourseMarksRows, examReportStructure, courseType);

        List<ExamReportAdditionalAttributesRow> examReportAdditionalAttributesRows = new ArrayList<>();
        if(!CollectionUtils.isEmpty(examReportStructure.getExamReportStructureMetaData().getScholasticAdditionalRowAttributes())){
            for(ExamGridRowAttribute examGridRowAttribute : examReportStructure.getExamReportStructureMetaData().getScholasticAdditionalRowAttributes()){
                List<ExamReportMarksColumn> examReportMarksColumns = new ArrayList<>();
                for(ExamReportMarksColumn<ExamDimensionObtainedValues> headerCol : examReportMarksHeaderColumns){
                    String columnValue = null;
                    switch (headerCol.getExamReportGridColumnType()){
                        case EXAM:
                            columnValue = getColumnValue(examGridRowAttribute, headerCol);
                            break;
                        default:
                    }
                    examReportMarksColumns.add(new ExamReportMarksColumn<>(headerCol.getId(),
                            headerCol.getTitle(), headerCol.getSubTitle(),
                            headerCol.getColorHexCode(),
                            headerCol.isDisplayTotalMarks(),
                            headerCol.getExamReportGridColumnType(),
                            headerCol.getRoundOff(), headerCol.getExamMetaData(),
                            headerCol.getChildExamReportMarksColumns(), headerCol.getExamDimensionValues(),
                            headerCol.isHideTotalDimensionMaxMarks(), headerCol.isHideRemainingDimensionMaxMarks(),
                            headerCol.getCourseWiseClassMetricsComputeColumn(),
                            headerCol.getCourseWiseGainMetricsComputeColumns(), columnValue, headerCol.isHide()));
                }
                examReportAdditionalAttributesRows.add(new ExamReportAdditionalAttributesRow(examGridRowAttribute, examReportMarksColumns));
            }
        }

        return new ExamReportMarksGrid(examReportMarksHeaderColumns, orderedExamReportCourseMarksRows,
                examReportCourseTotalMarksColumns, examReportAdditionalAttributesRows);

    }

    private String getColumnValue(ExamGridRowAttribute examGridRowAttribute, ExamReportMarksColumn<ExamDimensionObtainedValues> column){
        switch (examGridRowAttribute){
            case EXAM_DATE:
                Integer examStartDate =  column.getExamMetaData().getExamStartDate();
                return examStartDate == null ? null : DateUtils.getFormattedDate(examStartDate);
            case RESULT_DATE:
                Integer dateOfResultDeclaration =  column.getExamMetaData().getDateOfResultDeclaration();
                return dateOfResultDeclaration == null ? null : DateUtils.getFormattedDate(dateOfResultDeclaration);
        }
        return null;
    }
    private List<ExamReportCourseMarksRow> getOrderedExamReportCourseMarksRows(
            List<ExamReportCourseMarksRow> examReportCourseMarksRows, ExamReportStructure examReportStructure,
            CourseType courseType) {

        Collections.sort(examReportCourseMarksRows);

        if (!useExamCourseStructure(examReportStructure, courseType)) {
            return examReportCourseMarksRows;
        }

        final ExamReportCourseStructure examReportCourseStructure = examReportStructure.getExamReportCourseStructure()
                .get(courseType);
        if (CollectionUtils.isEmpty(examReportCourseStructure.getCourseOrder())) {
            return examReportCourseMarksRows;
        }

        final List<ExamReportCourseMarksRow> orderedExamReportCourseMarksRows = new ArrayList<>();
        final Map<UUID, ExamReportCourseMarksRow> courseRowMap = new HashMap<>();
        for (final ExamReportCourseMarksRow examReportCourseMarksRow : examReportCourseMarksRows) {
            courseRowMap.put(examReportCourseMarksRow.getCourse().getCourseId(), examReportCourseMarksRow);
        }

        for (final UUID courseId : examReportCourseStructure.getCourseOrder()) {
            if (courseRowMap.get(courseId) != null) {
                orderedExamReportCourseMarksRows.add(courseRowMap.get(courseId));
                courseRowMap.remove(courseId);
            }
        }

        for (final ExamReportCourseMarksRow examReportCourseMarksRow : examReportCourseMarksRows) {
            if (courseRowMap.containsKey(examReportCourseMarksRow.getCourse().getCourseId())) {
                orderedExamReportCourseMarksRows.add(examReportCourseMarksRow);
            }
        }

        return orderedExamReportCourseMarksRows;
    }

    private void computeTotalMarksRow(List<ExamGrade> examGrades,
                                      final List<ExamReportCourseMarksColumn> examReportCourseTotalMarksColumns,
                                      final ExamReportCourseMarksRow examReportCourseMarksRow) {
        int i = 0;
        for (final ExamReportCourseMarksColumn examReportCourseMarksColumnEntry : examReportCourseMarksRow
                .getExamReportCourseMarksColumns()) {

            if (i < examReportCourseTotalMarksColumns.size()) {
                final ExamReportCourseMarksColumn totalExamReportCourseMarksColumn = examReportCourseTotalMarksColumns
                        .get(i);
                int j = 0;
                for (final ExamDimensionObtainedValues examDimensionObtainedValues : examReportCourseMarksColumnEntry
                        .getExamDimensionObtainedValuesList()) {
                    final ExamDimensionObtainedValues sumExamDimensionObtainedValues = totalExamReportCourseMarksColumn
                            .getExamDimensionObtainedValuesList().get(j);
                    sumExamDimensionObtainedValues.setMaxMarks(ExamMarksUtils.addValues(
                                    sumExamDimensionObtainedValues.getOriginalMaxMarks(), examDimensionObtainedValues.getOriginalMaxMarks()),
                            ExamMarksUtils.addValues(
                                    sumExamDimensionObtainedValues.getMaxMarks(), examDimensionObtainedValues.getMaxMarks()));

                    sumExamDimensionObtainedValues.setMinMarks(ExamMarksUtils.addValues(
                            sumExamDimensionObtainedValues.getMinMarks(), examDimensionObtainedValues.getMinMarks()));

                    sumExamDimensionObtainedValues.setObtainedMarks(
                            ExamMarksUtils.addValues(sumExamDimensionObtainedValues.getObtainedMarks(),
                                    examDimensionObtainedValues.getObtainedMarks()));

                    ExamMarksUtils.updateMarksBasedGrade(examGrades, sumExamDimensionObtainedValues);
                    j++;
                }
            } else {
                examReportCourseTotalMarksColumns
                        .add(new ExamReportCourseMarksColumn(examReportCourseMarksColumnEntry.getId(),
                                examReportCourseMarksColumnEntry.getColorHexCode(),
                                examReportCourseMarksColumnEntry.getExamReportGridColumnType(),
                                examReportCourseMarksColumnEntry.getRoundOff(),
                                copyExamDimensionObtainedValuesList(examReportCourseMarksColumnEntry.getExamDimensionObtainedValuesList(), examGrades),
                                examReportCourseMarksColumnEntry.isHideTotalDimensionMaxMarks(),
                                examReportCourseMarksColumnEntry.isHideRemainingDimensionMaxMarks(),
                                examReportCourseMarksColumnEntry.getCourseWiseClassMetricsComputeColumn(),
                                examReportCourseMarksColumnEntry.getCourseWiseGainMetricsComputeColumns(), examReportCourseMarksColumnEntry.getColumnValue(),
                                examReportCourseMarksColumnEntry.isHide()));
            }
            i++;
        }
    }

    private boolean isAdditionalCourse(ExamReportStructure examReportStructure, CourseType courseType, UUID courseId) {
        if (!useExamCourseStructure(examReportStructure, courseType)) {
            return false;
        }
        final ExamReportCourseStructure examReportCourseStructure = examReportStructure.getExamReportCourseStructure()
                .get(courseType);

        if (!CollectionUtils.isEmpty(examReportCourseStructure.getAdditionalCourses())
                && examReportCourseStructure.getAdditionalCourses().contains(courseId)) {
            return true;
        }

        return false;
    }

    private Set<UUID> getReportCardCourses(ExamReportStructure examReportStructure,
                                           List<ExamReportCourseMarksRow> examReportCourseMarksRows, CourseType courseType) {

        final Set<UUID> reportCardCourses = new LinkedHashSet<>();
        if (!useExamCourseStructure(examReportStructure, courseType)) {
            return reportCardCourses;
        }

        final ExamReportCourseStructure examReportCourseStructure = examReportStructure.getExamReportCourseStructure()
                .get(courseType);

        if (!CollectionUtils.isEmpty(examReportCourseStructure.getMandatoryCourses())) {
            reportCardCourses.addAll(examReportCourseStructure.getMandatoryCourses());
        }

        if (!CollectionUtils.isEmpty(examReportCourseStructure.getSelectionCourses())) {
            if (examReportCourseStructure.getTopCoursesSelectionCount() == null
                    || examReportCourseStructure.getTopCoursesSelectionCount() < 0) {
                reportCardCourses.addAll(examReportCourseStructure.getSelectionCourses());
            } else if (examReportCourseStructure.getTopCoursesSelectionCount() > 0) {
                if (examReportCourseStructure.getTopCoursesSelectionCount() >= examReportCourseStructure
                        .getSelectionCourses().size()) {
                    reportCardCourses.addAll(examReportCourseStructure.getSelectionCourses());
                } else {
                    final List<Pair<UUID, Double>> courseTotalPercentage = new ArrayList<>();
                    for (final ExamReportCourseMarksRow examReportCourseMarksRow : examReportCourseMarksRows) {
                        /**
                         * Comparison on selection courses only
                         */
                        if (!examReportCourseStructure.getSelectionCourses()
                                .contains(examReportCourseMarksRow.getCourse().getCourseId())) {
                            continue;
                        }
                        Pair<Triplet<Double, Double, Double>, Double> totalValues = getTotalMarks(examReportCourseStructure,
                                examReportCourseMarksRow.getExamReportCourseMarksColumns());

                        final Triplet<Double, Double, Double> totalMarks = totalValues.getFirst();
                        // SEARCH_EXP : AdjustedMaxMarksUsage
//                        final Double percentage = totalMarks.getFirst() == null || totalMarks.getSecond() == null ? null
//                                : Double.compare(totalMarks.getFirst(), 0) > 0
//                                ? totalMarks.getSecond() / totalMarks.getFirst()
//                                : null;

                        final Double percentage = totalMarks.getSecond() == null || totalMarks.getThird() == null ? null
                                : Double.compare(totalMarks.getSecond(), 0) > 0
                                ? totalMarks.getThird() / totalMarks.getSecond()
                                : null;

                        courseTotalPercentage
                                .add(new Pair<>(examReportCourseMarksRow.getCourse().getCourseId(), percentage));
                    }

                    Collections.sort(courseTotalPercentage, new Comparator<Pair<UUID, Double>>() {

                        @Override
                        public int compare(Pair<UUID, Double> s1, Pair<UUID, Double> s2) {
                            if (s1.getSecond() == null) {
                                return 1;

                            } else if (s2.getSecond() == null) {
                                return -1;
                            }
                            return -1 * Double.compare(s1.getSecond(), s2.getSecond());
                        }
                    });
                    final int maxIndex = courseTotalPercentage.size() > examReportCourseStructure
                            .getTopCoursesSelectionCount() ? examReportCourseStructure.getTopCoursesSelectionCount()
                            : courseTotalPercentage.size();
                    final List<Pair<UUID, Double>> topCourseTotalPercentage = courseTotalPercentage.subList(0,
                            maxIndex);
                    for (final Pair<UUID, Double> topCourse : topCourseTotalPercentage) {
                        reportCardCourses.add(topCourse.getFirst());
                    }
                }

            }
        }

        return reportCardCourses;

    }

    private boolean useExamCourseStructure(ExamReportStructure examReportStructure, CourseType courseType) {

        return !CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
                && examReportStructure.getExamReportCourseStructure().get(courseType) != null;

    }

    private List<ExamDimensionObtainedValues> copyExamDimensionObtainedValuesList(
            List<ExamDimensionObtainedValues> examDimensionObtainedValuesList, List<ExamGrade> examGrades) {
        final List<ExamDimensionObtainedValues> copiedExamDimensionObtainedValuesList = new ArrayList<>();
        for (final ExamDimensionObtainedValues examDimensionObtainedValues : examDimensionObtainedValuesList) {
            final ExamDimensionObtainedValues copiedExamDimensionObtainedValues = new ExamDimensionObtainedValues(
                    examDimensionObtainedValues.getExamDimension(), examDimensionObtainedValues.getObtainedMarks(),
                    examDimensionObtainedValues.getGraceMarks(), examDimensionObtainedValues.getObtainedGrade(),
                    examDimensionObtainedValues.getAttendanceStatus(),
                    examDimensionObtainedValues.getOriginalMaxMarks(), examDimensionObtainedValues.getMaxMarks(), examDimensionObtainedValues.getMinMarks(),
                    examDimensionObtainedValues.getMaxGrade(), examDimensionObtainedValues.getMinGrade(),
                    examDimensionObtainedValues.getExamCoursePublishedStatus());

            ExamMarksUtils.updateMarksBasedGrade(examGrades, copiedExamDimensionObtainedValues);
            copiedExamDimensionObtainedValuesList.add(copiedExamDimensionObtainedValues);
        }
        return copiedExamDimensionObtainedValuesList;
    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getExamReportMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            final Map<UUID, Map<Integer, ExamDimensionObtainedValues>> headerExamDimensionsMap,
            Map<UUID, ExamMetaData> examMetaDataMap, Map<Integer, Set<String>> treeLevelColumnIdsMap, int level,
            ExamDimension totalExamDimension, List<ExamReportMarksColumn<ExamDimensionObtainedValues>> leafColumns,
            List<ExamGrade> examGrades, ExamDimension defaultTotalDimension, Map<Integer,ExamDimension> dimensionMap, boolean isGradeEnabled) {

        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getChildExamReportHeaderStructureColumns())) {

            final ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = getLeafNodeExamReportMarksColumn(
                    examReportHeaderStructureColumn, examReportMarksColumnsMap, headerExamDimensionsMap,
                    examMetaDataMap, treeLevelColumnIdsMap, level, totalExamDimension, examGrades, defaultTotalDimension, dimensionMap, isGradeEnabled);
            examReportMarksColumnsMap.put(examReportHeaderStructureColumn.getId(), examReportMarksColumn);

            if (!treeLevelColumnIdsMap.containsKey(level)) {
                treeLevelColumnIdsMap.put(level, new LinkedHashSet<>());
            }
            treeLevelColumnIdsMap.get(level).add(examReportHeaderStructureColumn.getId());
            leafColumns.add(examReportMarksColumn);

            return examReportMarksColumn;

        }

        if (examReportHeaderStructureColumn.getExamReportGridColumnType() != ExamReportGridColumnType.STATIC) {
            logger.error("Invalid report structure. Nodes apart from STATIC should not have children. Column Id {}",
                    examReportHeaderStructureColumn.getId());
            throw new EmbrateRunTimeException(
                    "Invalid report structure. Nodes apart from STATIC should not have children");
        }

        final List<ExamReportMarksColumn<ExamDimensionObtainedValues>> childExamReportMarksColumns = new ArrayList<>();
        final Map<Integer, ExamDimensionObtainedValues> dimensionSumAcrossChildren = new LinkedHashMap<>();
        for (final ExamReportHeaderStructureColumn childExamReportHeaderStructureColumn : examReportHeaderStructureColumn
                .getChildExamReportHeaderStructureColumns()) {
            final ExamReportMarksColumn<ExamDimensionObtainedValues> childExamReportMarksColumn = getExamReportMarksColumn(
                    childExamReportHeaderStructureColumn, examReportMarksColumnsMap, headerExamDimensionsMap,
                    examMetaDataMap, treeLevelColumnIdsMap, level + 1, totalExamDimension, leafColumns, examGrades, defaultTotalDimension, dimensionMap,
                    isGradeEnabled);

            childExamReportMarksColumns.add(childExamReportMarksColumn);

            if (childExamReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM
                    || childExamReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.EXAM
                    || childExamReportMarksColumn.getExamReportGridColumnType() == ExamReportGridColumnType.STATIC) {

                if (CollectionUtils.isEmpty(childExamReportMarksColumn.getExamDimensionValues())) {
                    continue;
                }
                for (final ExamDimensionObtainedValues examDimensionValues : childExamReportMarksColumn
                        .getExamDimensionValues()) {
                    final Integer dimensionId = examDimensionValues.getExamDimension().getDimensionId();
                    if (!dimensionSumAcrossChildren.containsKey(dimensionId)) {
                        dimensionSumAcrossChildren.put(dimensionId,
                                new ExamDimensionObtainedValues(examDimensionValues.getExamDimension(),
                                        examDimensionValues.getObtainedMarks(), examDimensionValues.getGraceMarks(),
                                        examDimensionValues.getObtainedGrade(), examDimensionValues.getAttendanceStatus(),
                                        examDimensionValues.getOriginalMaxMarks(), examDimensionValues.getMaxMarks(), null,
                                        null, null, examDimensionValues.getExamCoursePublishedStatus()));
                    } else {
                        final ExamDimensionObtainedValues totalExamDimensionValues = dimensionSumAcrossChildren
                                .get(dimensionId);
                        totalExamDimensionValues.setMaxMarks(ExamMarksUtils
                                        .addValues(totalExamDimensionValues.getOriginalMaxMarks(), examDimensionValues.getOriginalMaxMarks()),
                                ExamMarksUtils
                                        .addValues(totalExamDimensionValues.getMaxMarks(), examDimensionValues.getMaxMarks()));

                        totalExamDimensionValues.setMinMarks(ExamMarksUtils
                                .addValues(totalExamDimensionValues.getMinMarks(), examDimensionValues.getMinMarks()));

                        totalExamDimensionValues.setObtainedMarks(ExamMarksUtils.addValues(
                                totalExamDimensionValues.getObtainedMarks(), examDimensionValues.getObtainedMarks()));

                        totalExamDimensionValues.setAttendanceStatus(ExamAttendanceStatus.getNetExamAttendanceStatus(
                                totalExamDimensionValues.getAttendanceStatus(), examDimensionValues.getAttendanceStatus()));
                    }
                }

            }
        }

        final List<ExamDimensionObtainedValues> totalExamDimensionValues = new ArrayList<>(
                dimensionSumAcrossChildren.values());

        final ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = getExamReportMarksColumn(
                examReportHeaderStructureColumn, null, childExamReportMarksColumns, totalExamDimensionValues);
        examReportMarksColumnsMap.put(examReportHeaderStructureColumn.getId(), examReportMarksColumn);
        if (!treeLevelColumnIdsMap.containsKey(level)) {
            treeLevelColumnIdsMap.put(level, new LinkedHashSet<>());
        }
        treeLevelColumnIdsMap.get(level).add(examReportHeaderStructureColumn.getId());
        return examReportMarksColumn;

    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getLeafNodeExamReportMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            final Map<UUID, Map<Integer, ExamDimensionObtainedValues>> headerExamDimensionsMap,
            Map<UUID, ExamMetaData> examMetaDataMap, Map<Integer, Set<String>> treeLevelColumnIdsMap, int level,
            ExamDimension totalDimension, List<ExamGrade> examGrades, ExamDimension defaultTotalDimension, Map<Integer,ExamDimension> dimensionMap,
            boolean isGradeEnabled) {

        switch (examReportHeaderStructureColumn.getExamReportGridColumnType()) {
            case STATIC:
//                return getExamReportMarksColumn(examReportHeaderStructureColumn, null, new ArrayList<>(),
//                        new ArrayList<>());
            case COURSE_RANK:
            case CLASS_COURSE_AVG:
            case CLASS_COURSE_HIGH:
                // Adding default dimension as in pdf rendering if there is no dimension, entire column is skipped
                final List<ExamDimensionObtainedValues> examDimensionValuesList = new ArrayList<>();
                examDimensionValuesList.add(new ExamDimensionObtainedValues(new ExamDimension()));
                return getExamReportMarksColumn(examReportHeaderStructureColumn, null, new ArrayList<>(),
                        examDimensionValuesList);
            case MIN_MARKS:
                return getMinMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                        treeLevelColumnIdsMap, level, totalDimension);
            case CREDIT_SCORE:
                if(isGradeEnabled) {
                    //below logic is for exam grade, if we are sending exam grade in config, exam name should be send, this is mandatory.
                    if (examReportHeaderStructureColumn.getExamId() == null
                            || examMetaDataMap.get(examReportHeaderStructureColumn.getExamId()) == null) {
                        logger.error("Invalid exam configured {}", examReportHeaderStructureColumn.getExamId());
                        throw new EmbrateRunTimeException("Invalid exam configured");
                    }
                    final ExamMetaData examMetaData = examMetaDataMap.get(examReportHeaderStructureColumn.getExamId());
                    return getExamReportMarksColumn(examReportHeaderStructureColumn, examMetaData, new ArrayList<>(),
                            getHeaderExamDimensionValues(examReportHeaderStructureColumn.getExamId(), headerExamDimensionsMap,
                                    examReportHeaderStructureColumn, examGrades, defaultTotalDimension, dimensionMap));
                } else {
                    //below logic is for grade, if we are sending grade in config, exam name should be null
                    return getSummationExamReportMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                            treeLevelColumnIdsMap, level, totalDimension, examGrades,
                            examReportHeaderStructureColumn.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE, new HashMap<>());
                }
            case SHOW_GRADE_WITH_MARKS_GRID:
            case EXAM_GRADE:
            case EXAM:
                if (examReportHeaderStructureColumn.getExamId() == null
                        || examMetaDataMap.get(examReportHeaderStructureColumn.getExamId()) == null) {
                    logger.error("Invalid exam configured {}", examReportHeaderStructureColumn.getExamId());
                    throw new EmbrateRunTimeException("Invalid exam configured");
                }
                final ExamMetaData examMetaData = examMetaDataMap.get(examReportHeaderStructureColumn.getExamId());
                return getExamReportMarksColumn(examReportHeaderStructureColumn, examMetaData, new ArrayList<>(),
                        getHeaderExamDimensionValues(examReportHeaderStructureColumn.getExamId(), headerExamDimensionsMap,
                                examReportHeaderStructureColumn, examGrades, defaultTotalDimension, dimensionMap));
            case SUM:
            case PERCENT:
            case GRADE:
                return getSummationExamReportMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                        treeLevelColumnIdsMap, level, totalDimension, examGrades,
                        examReportHeaderStructureColumn.getExamReportGridColumnType() == ExamReportGridColumnType.GRADE || examReportHeaderStructureColumn.getExamReportGridColumnType() == ExamReportGridColumnType.CREDIT_SCORE, new HashMap<>());
            case AVERAGE:
                Map<Integer, Integer> adjustedMaxMarksSumColumnCountMap = new HashMap<>();
                final ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = getSummationExamReportMarksColumn(
                        examReportHeaderStructureColumn, examReportMarksColumnsMap, treeLevelColumnIdsMap, level,
                        totalDimension, examGrades, false, adjustedMaxMarksSumColumnCountMap);
                if (examReportMarksColumn != null
                        && !CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionValues())) {
                    /*
                     * Validation is already there for computationColumnCount in
                     * getSummationExamReportMarksColumn method
                     */
                    final int computationColumnCount = examReportHeaderStructureColumn.getComputationColumns().size();
                    for (final ExamDimensionObtainedValues examDimensionObtainedValues : examReportMarksColumn
                            .getExamDimensionValues()) {
                        Integer adjustedMaxMarksSumColumnCount = adjustedMaxMarksSumColumnCountMap.get(examDimensionObtainedValues.getExamDimension().getDimensionId());
                        if (examDimensionObtainedValues.getOriginalMaxMarks() != null) {
                            examDimensionObtainedValues
                                    .setMaxMarks(examDimensionObtainedValues.getOriginalMaxMarks() / computationColumnCount,
                                            NumberUtils.isNullOrZero(adjustedMaxMarksSumColumnCount)
                                                    || examDimensionObtainedValues.getMaxMarks() == null ? null :
                                                    examDimensionObtainedValues.getMaxMarks() / adjustedMaxMarksSumColumnCount);
                        }

                        if (examDimensionObtainedValues.getMinMarks() != null) {
                            examDimensionObtainedValues
                                    .setMinMarks(examDimensionObtainedValues.getMinMarks() / computationColumnCount);
                        }

                        if (examDimensionObtainedValues.getObtainedMarks() != null) {
                            // SEARCH_EXP : AdjustedMaxMarksUsage
//                            examDimensionObtainedValues.setObtainedMarks(
//                                    examDimensionObtainedValues.getObtainedMarks() / computationColumnCount);

                            // Assuming adjustedMaxMarksSumColumnCount to be not null and not zero as obtains marks is not null
                            examDimensionObtainedValues.setObtainedMarks(
                                    examDimensionObtainedValues.getObtainedMarks() / adjustedMaxMarksSumColumnCount);
                        }

                    }
                    return examReportMarksColumn;
                }

            case COURSE_GAIN:
                return getCourseGainExamReportMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                        totalDimension);

            default:
                logger.error("Invalid exam configured {}. Invalid ExamReportGridColumnType {}",
                        examReportHeaderStructureColumn.getExamId(),
                        examReportHeaderStructureColumn.getExamReportGridColumnType());
                throw new EmbrateRunTimeException("Invalid exam configured. Invalid ExamReportGridColumnType");
        }

    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getCourseGainExamReportMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            ExamDimension totalDimension) {

        validateCourseGainColumnComputation(examReportHeaderStructureColumn, examReportMarksColumnsMap);

        /**
         * Empty dimension implies only total dimension is to be computed
         */
        //TODO:add dependency on examReportCardMetaData.getExamReportStructureMetaData().isShowScholasticCourseGainMetrics(); & examReportCardMetaData.getExamReportStructureMetaData().isShowCoScholasticCourseGainMetrics();
        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getObtainedMarksDimensions())) {
            return getTotalDimensionCourseGainColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                    totalDimension);
        }

        throw new NotImplementedException("Selected dimension course gain computation not implemented");
    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getSummationExamReportMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            Map<Integer, Set<String>> treeLevelColumnIdsMap, int level, ExamDimension totalDimension,
            List<ExamGrade> examGrades, boolean computeGradeFromMarks, Map<Integer, Integer> adjustedMaxMarksSumColumnCountMap) {

        validateSumColumnComputation(examReportHeaderStructureColumn, examReportMarksColumnsMap, treeLevelColumnIdsMap,
                level);

        /**
         * Empty dimension implies only total dimension is to be computed
         */
        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getObtainedMarksDimensions())) {
            return getTotalDimensionSummationExamMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                    totalDimension, examGrades, computeGradeFromMarks, adjustedMaxMarksSumColumnCountMap);
        }

        return getGivenDimensionSummationExamMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                totalDimension, examGrades, computeGradeFromMarks, adjustedMaxMarksSumColumnCountMap);

    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getMinMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            Map<Integer, Set<String>> treeLevelColumnIdsMap, int level, ExamDimension totalDimension) {

        /**
         * Empty dimension implies only total dimension is to be computed
         */
        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getObtainedMarksDimensions())) {
            return getTotalDimensionMinMarksColumn(examReportHeaderStructureColumn, examReportMarksColumnsMap,
                    totalDimension);
        }

        throw new NotImplementedException("Selected dimension min marks computation not implemented");
    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getGivenDimensionSummationExamMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            ExamDimension totalDimension, List<ExamGrade> examGrades, boolean computeGradeFromMarks,
            Map<Integer, Integer> adjustedMaxMarksSumColumnCountMap) {
        boolean hasTotalDimension = false;

        final ExamDimensionObtainedValues totalObtainedExamDimensionValues = new ExamDimensionObtainedValues(
                totalDimension);

        final List<ExamDimensionObtainedValues> requiredExamDimensionValuesList = new ArrayList<>();
        for (final Integer dimensionId : examReportHeaderStructureColumn.getObtainedMarksDimensions()) {

            /**
             * Total dimension will be computed based on other dimension values
             */
            if (dimensionId == totalDimension.getDimensionId()) {
                hasTotalDimension = true;
                continue;
            }

            ExamDimensionObtainedValues requiredObtainedExamDimensionValues = null;
            for (final String computationColumnId : examReportHeaderStructureColumn.getComputationColumns()) {
                final ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = examReportMarksColumnsMap
                        .get(computationColumnId);

                if (CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionValues())) {
                    continue;
                }

                for (final ExamDimensionObtainedValues examDimensionValues : examReportMarksColumn
                        .getExamDimensionValues()) {

                    if (examDimensionValues.getExamDimension().getDimensionId() != dimensionId) {
                        continue;
                    }

                    if (requiredObtainedExamDimensionValues == null) {
                        requiredObtainedExamDimensionValues = new ExamDimensionObtainedValues(
                                examDimensionValues.getExamDimension());
                    }

                    if (examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {

                        updateDimensionMarksSum(totalObtainedExamDimensionValues, requiredObtainedExamDimensionValues,
                                examDimensionValues, adjustedMaxMarksSumColumnCountMap);

                    } else {
                        /**
                         * TODO: Need to see , if grading summation column is
                         * required or not
                         */
                    }

                }
            }

            /**
             * For sum column , required dimensions must be there in at least
             * one of computation column. ow it will be skipped
             */
            if (requiredObtainedExamDimensionValues != null) {
                if (computeGradeFromMarks) {
                    final ExamGrade obtainedGrade = ExamMarksUtils.getExamGradeByPercentage(examGrades,
                            requiredObtainedExamDimensionValues.getObtainedMarksFraction());
                    requiredObtainedExamDimensionValues.setObtainedGrade(obtainedGrade);
                }

                requiredExamDimensionValuesList.add(requiredObtainedExamDimensionValues);
            }

        }

        if (hasTotalDimension) {
            if (computeGradeFromMarks) {
                final ExamGrade obtainedGrade = ExamMarksUtils.getExamGradeByPercentage(examGrades,
                        totalObtainedExamDimensionValues.getObtainedMarksFraction());
                totalObtainedExamDimensionValues.setObtainedGrade(obtainedGrade);
            }

            // Count is taken across distinct dimensions. If any nonzero count is present for dimension, it is taken in count
            int totalDimensionMaxMarksSumColumnCount = 0;
            for(Entry<Integer, Integer> entry : adjustedMaxMarksSumColumnCountMap.entrySet()){
                if(entry.getValue() != null && entry.getValue() > 0){
                    totalDimensionMaxMarksSumColumnCount++;
                }
            }
            adjustedMaxMarksSumColumnCountMap.put(totalObtainedExamDimensionValues.getExamDimension().getDimensionId(), totalDimensionMaxMarksSumColumnCount);
            requiredExamDimensionValuesList.add(totalObtainedExamDimensionValues);
        }

        return getExamReportMarksColumn(examReportHeaderStructureColumn, null, new ArrayList<>(),
                requiredExamDimensionValuesList);
    }

    private void updateDimensionMarksSum(final ExamDimensionObtainedValues totalObtainedExamDimensionValues,
                                         final ExamDimensionObtainedValues requiredObtainedExamDimensionValues,
                                         final ExamDimensionObtainedValues examDimensionValues,
                                         Map<Integer, Integer> adjustedMaxMarksSumColumnCountMap) {

        // Required to get count for avg computation over required adjustedMaxMarks Columns
        if(!NumberUtils.isNullOrZero(examDimensionValues.getMaxMarks())){
            int dimensionId = examDimensionValues.getExamDimension().getDimensionId();
            if(!adjustedMaxMarksSumColumnCountMap.containsKey(dimensionId)){
                adjustedMaxMarksSumColumnCountMap.put(dimensionId , 0);
            }
            adjustedMaxMarksSumColumnCountMap.put(dimensionId, adjustedMaxMarksSumColumnCountMap.get(dimensionId) + 1);
        }

        /**
         * Sum for individual non total dimensions
         */
        requiredObtainedExamDimensionValues.setMaxMarks(ExamMarksUtils
                        .addValues(requiredObtainedExamDimensionValues.getOriginalMaxMarks(), examDimensionValues.getOriginalMaxMarks()),
                ExamMarksUtils
                        .addValues(requiredObtainedExamDimensionValues.getMaxMarks(), examDimensionValues.getMaxMarks()));

        requiredObtainedExamDimensionValues.setMinMarks(ExamMarksUtils
                .addValues(requiredObtainedExamDimensionValues.getMinMarks(), examDimensionValues.getMinMarks()));

        requiredObtainedExamDimensionValues.setObtainedMarks(ExamMarksUtils.addValues(
                requiredObtainedExamDimensionValues.getObtainedMarks(), examDimensionValues.getObtainedMarks()));

        requiredObtainedExamDimensionValues.setAttendanceStatus(ExamAttendanceStatus.getNetExamAttendanceStatus(
                requiredObtainedExamDimensionValues.getAttendanceStatus(), examDimensionValues.getAttendanceStatus()));

        /**
         * Summing for total dimension
         */
        totalObtainedExamDimensionValues.setMaxMarks(ExamMarksUtils
                        .addValues(totalObtainedExamDimensionValues.getOriginalMaxMarks(), examDimensionValues.getOriginalMaxMarks()),
                ExamMarksUtils
                        .addValues(totalObtainedExamDimensionValues.getMaxMarks(), examDimensionValues.getMaxMarks()));

        totalObtainedExamDimensionValues.setMinMarks(ExamMarksUtils
                .addValues(totalObtainedExamDimensionValues.getMinMarks(), examDimensionValues.getMinMarks()));

        totalObtainedExamDimensionValues.setObtainedMarks(ExamMarksUtils.addValues(
                totalObtainedExamDimensionValues.getObtainedMarks(), examDimensionValues.getObtainedMarks()));

        totalObtainedExamDimensionValues.setAttendanceStatus(ExamAttendanceStatus.getNetExamAttendanceStatus(
                totalObtainedExamDimensionValues.getAttendanceStatus(), examDimensionValues.getAttendanceStatus()));

    }

    @SuppressWarnings("unchecked")
    private ExamReportMarksColumn<ExamDimensionObtainedValues> getTotalDimensionSummationExamMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            ExamDimension totalDimension, List<ExamGrade> examGrades, boolean computeGradeFromMarks,
            Map<Integer, Integer> adjustedMaxMarksSumColumnCountMap) {

        final ExamDimensionObtainedValues totalObtainedExamDimensionValues = new ExamDimensionObtainedValues(
                totalDimension);

        for (final String computationColumnId : examReportHeaderStructureColumn.getComputationColumns()) {

            final ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = examReportMarksColumnsMap
                    .get(computationColumnId);

            if (CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionValues())) {
                continue;
            }

            if (examReportMarksColumn.getExamDimensionValues().size() == 1) {
                if (totalDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {

                    updateTotalDimensionMarks(totalObtainedExamDimensionValues, examReportMarksColumn.getExamDimensionValues().get(0), adjustedMaxMarksSumColumnCountMap);

                } else if (totalDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
                    /**
                     * TODO: Need to see , if grading summation column is
                     * required or not
                     */
                }

                continue;
            }

            for (final ExamDimensionObtainedValues examDimensionValues : examReportMarksColumn.getExamDimensionValues()) {

                /**
                 * Skipping total and aggregating rest of the dimensions to make
                 * sure that total is always correct
                 */
                if (examDimensionValues.getExamDimension().isTotal()) {
                    continue;
                }

                if (totalDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                    updateTotalDimensionMarks(totalObtainedExamDimensionValues, examDimensionValues, adjustedMaxMarksSumColumnCountMap);
                }

            }

        }
        if (computeGradeFromMarks) {
            final ExamGrade obtainedGrade = ExamMarksUtils.getExamGradeByPercentage(examGrades,
                    totalObtainedExamDimensionValues.getObtainedMarksFraction());
            totalObtainedExamDimensionValues.setObtainedGrade(obtainedGrade);
        }
        return getExamReportMarksColumn(examReportHeaderStructureColumn, null, new ArrayList<>(),
                Arrays.asList(totalObtainedExamDimensionValues));
    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getTotalDimensionCourseGainColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            ExamDimension totalDimension) {

        String firstComputationColumnId = examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns().getFirstColumn();
        ExamReportMarksColumn<ExamDimensionObtainedValues> firstExamReportMarksColumn = examReportMarksColumnsMap
                .get(firstComputationColumnId);

        Double firstPercentage = getExamReportMarksColumnPercentage(firstExamReportMarksColumn, totalDimension);

        String secondComputationColumnId = examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns().getSecondColumn();
        ExamReportMarksColumn<ExamDimensionObtainedValues> secondExamReportMarksColumn = examReportMarksColumnsMap
                .get(secondComputationColumnId);

        Double secondPercentage = getExamReportMarksColumnPercentage(secondExamReportMarksColumn, totalDimension);

        Double courseGain = ExamMarksUtils.subValues(secondPercentage,  firstPercentage);
        examReportHeaderStructureColumn.setColumnValue(courseGain == null ? null : NumberUtils.formatDouble(courseGain));

        return new ExamReportMarksColumn<>(examReportHeaderStructureColumn.getId(),
                examReportHeaderStructureColumn.getTitle(), examReportHeaderStructureColumn.getSubTitle(),
                examReportHeaderStructureColumn.getColorHexCode(),
                examReportHeaderStructureColumn.isDisplayTotalMarks(),
                examReportHeaderStructureColumn.getExamReportGridColumnType(),
                examReportHeaderStructureColumn.getRoundOff(), null,
                new ArrayList<>(), Arrays.asList(new ExamDimensionObtainedValues(totalDimension)),
                examReportHeaderStructureColumn.isHideTotalDimensionMaxMarks(), examReportHeaderStructureColumn.isHideRemainingDimensionMaxMarks(),
                examReportHeaderStructureColumn.getCourseWiseClassMetricsComputeColumn(),
                examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns(),
                examReportHeaderStructureColumn.getColumnValue(),
                examReportHeaderStructureColumn.isHide());
    }

    private Double getExamReportMarksColumnPercentage(ExamReportMarksColumn<ExamDimensionObtainedValues> firstExamReportMarksColumn, ExamDimension totalDimension) {
        Double percentage = null;
        if (!CollectionUtils.isEmpty(firstExamReportMarksColumn.getExamDimensionValues())) {
            for (final ExamDimensionObtainedValues examDimensionValues : firstExamReportMarksColumn.getExamDimensionValues()) {

                /**
                 * Skipping total and aggregating rest of the dimensions to make
                 * sure that total is always correct
                 */
                if (!examDimensionValues.getExamDimension().isTotal()) {
                    continue;
                }

                if (totalDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                    Double obtainedMarks = examDimensionValues.getObtainedMarks();
                    Double maxMarks = examDimensionValues.getOriginalMaxMarks();

                    percentage = ExamMarksUtils.getPercentage(obtainedMarks, maxMarks);
                }
            }
        }
        return percentage;
    }

    private ExamReportMarksColumn<ExamDimensionObtainedValues> getTotalDimensionMinMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<ExamDimensionObtainedValues>> examReportMarksColumnsMap,
            ExamDimension totalDimension) {

        final ExamDimensionObtainedValues totalObtainedExamDimensionValues = new ExamDimensionObtainedValues(
                totalDimension);

        for (final String computationColumnId : examReportHeaderStructureColumn.getComputationColumns()) {

            final ExamReportMarksColumn<ExamDimensionObtainedValues> examReportMarksColumn = examReportMarksColumnsMap
                    .get(computationColumnId);

            if(examReportMarksColumn == null) {
                continue;
            }

            if (CollectionUtils.isEmpty(examReportMarksColumn.getExamDimensionValues())) {
                continue;
            }

            if (examReportMarksColumn.getExamDimensionValues().size() == 1) {
                if (totalDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                    totalObtainedExamDimensionValues
                            .setMaxMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getOriginalMaxMarks(),
                                    examReportMarksColumn.getExamDimensionValues().get(0).getOriginalMaxMarks()), ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getMaxMarks(),
                                    examReportMarksColumn.getExamDimensionValues().get(0).getMaxMarks()));

                    totalObtainedExamDimensionValues.setMinMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getMinMarks(),
                            examReportMarksColumn.getExamDimensionValues().get(0).getMinMarks()));

                } else if (totalDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
                    /**
                     * TODO: Need to see , if grading summation column is
                     * required or not
                     */
                }

                continue;
            }

            for (final ExamDimensionObtainedValues examDimensionValues : examReportMarksColumn.getExamDimensionValues()) {

                /**
                 * Skipping total and aggregating rest of the dimensions to make
                 * sure that total is always correct
                 */
                if (examDimensionValues.getExamDimension().isTotal()) {
                    continue;
                }

                if (totalDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
                    totalObtainedExamDimensionValues
                            .setMaxMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getOriginalMaxMarks(),
                                    examDimensionValues.getOriginalMaxMarks()), ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getMaxMarks(),
                                    examDimensionValues.getMaxMarks()));

                    totalObtainedExamDimensionValues
                            .setMinMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getMinMarks(),
                                    examDimensionValues.getMinMarks()));
                }
            }
        }


        if(totalObtainedExamDimensionValues.getMinMarks() == null){
            String factor = examReportHeaderStructureColumn.getColumnValue();
            double minFactor = 0.33;
            if(StringUtils.isNotBlank(factor)){
                try{
                    minFactor = Double.parseDouble(factor);
                }catch (Exception e){
                    logger.error("invalid min factor provided");
                }
            }

            totalObtainedExamDimensionValues.setMinMarks(totalObtainedExamDimensionValues.getOriginalMaxMarks() == null ? null :
                    totalObtainedExamDimensionValues.getOriginalMaxMarks() * minFactor);
        }


        return getExamReportMarksColumn(examReportHeaderStructureColumn, null, new ArrayList<>(),
                Arrays.asList(totalObtainedExamDimensionValues));
    }

    private void updateTotalDimensionMarks(final ExamDimensionObtainedValues totalObtainedExamDimensionValues,
                                           final ExamDimensionObtainedValues examDimensionObtainedValues,
                                           Map<Integer, Integer> adjustedMaxMarksSumColumnCountMap) {

        // Required to get count for avg computation over required adjustedMaxMarks Columns
        if(!NumberUtils.isNullOrZero(examDimensionObtainedValues.getMaxMarks())){
            int dimensionId = examDimensionObtainedValues.getExamDimension().getDimensionId();
            if(!adjustedMaxMarksSumColumnCountMap.containsKey(dimensionId)){
                adjustedMaxMarksSumColumnCountMap.put(dimensionId , 0);
            }
            adjustedMaxMarksSumColumnCountMap.put(dimensionId, adjustedMaxMarksSumColumnCountMap.get(dimensionId) + 1);
        }

        totalObtainedExamDimensionValues
                .setMaxMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getOriginalMaxMarks(),
                        examDimensionObtainedValues.getOriginalMaxMarks()),  ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getMaxMarks(),
                        examDimensionObtainedValues.getMaxMarks()));

        totalObtainedExamDimensionValues.setMinMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getMinMarks(),
                examDimensionObtainedValues.getMinMarks()));

        totalObtainedExamDimensionValues
                .setObtainedMarks(ExamMarksUtils.addValues(totalObtainedExamDimensionValues.getObtainedMarks(),
                        examDimensionObtainedValues.getObtainedMarks()));

        totalObtainedExamDimensionValues
                .setAttendanceStatus(ExamAttendanceStatus.getNetExamAttendanceStatus(totalObtainedExamDimensionValues.getAttendanceStatus(),
                        examDimensionObtainedValues.getAttendanceStatus()));
    }

    private <T extends ExamDimensionValues> void validateCourseGainColumnComputation(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<T>> examReportMarksColumnsMap) {
        /**
         * Computation columns must be there for computation of sum. Other-wise
         * there is no meaning of showing sum column
         */
        if (examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns() == null ||
                StringUtils.isBlank(examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns().getFirstColumn()) ||
                StringUtils.isBlank(examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns().getSecondColumn())) {
            logger.error("Invalid exam configured {}. No course gain columns for {}",
                    examReportHeaderStructureColumn.getExamId(),
                    examReportHeaderStructureColumn.getExamReportGridColumnType());
            throw new EmbrateRunTimeException("Invalid exam configured. . No course gain columns provided");
        }
        /**
         * checking if first column is present in structure or not.
         */
        String computationColumnId = examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns().getFirstColumn();
        ExamReportMarksColumn<T> examReportMarksColumn = examReportMarksColumnsMap.get(computationColumnId);
        if (examReportMarksColumn == null) {
            logger.error("Invalid state for exam {}. Required computation column for course gain {} is null",
                    examReportHeaderStructureColumn.getExamId(), computationColumnId);
            throw new EmbrateRunTimeException("Invalid state for exam. Required computation column for course gain is null");
        }

        /**
         * checking if first column is second in structure or not.
         */
        computationColumnId = examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns().getSecondColumn();
        examReportMarksColumn = examReportMarksColumnsMap.get(computationColumnId);
        if (examReportMarksColumn == null) {
            logger.error("Invalid state for exam {}. Required computation column for course gain {} is null",
                    examReportHeaderStructureColumn.getExamId(), computationColumnId);
            throw new EmbrateRunTimeException("Invalid state for exam. Required computation column for course gain is null");
        }
    }

    private <T extends ExamDimensionValues> void validateSumColumnComputation(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn,
            Map<String, ExamReportMarksColumn<T>> examReportMarksColumnsMap,
            Map<Integer, Set<String>> treeLevelColumnIdsMap, int level) {
        /**
         * Computation columns must be there for computation of sum. Other wise
         * there is no meaning of showing sum column
         */
        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getComputationColumns())) {
            logger.error("Invalid exam configured {}. No computation columns for {}",
                    examReportHeaderStructureColumn.getExamId(),
                    examReportHeaderStructureColumn.getExamReportGridColumnType());
            throw new EmbrateRunTimeException("Invalid exam configured. . No computation columns provided");
        }

        final Set<String> siblingColumnIds = treeLevelColumnIdsMap.get(level);
        /**
         * Sibling columns must be there for computation of sum. Other wise
         * there is no meaning of showing sum column
         */
        // if (CollectionUtils.isEmpty(siblingColumnIds)) {
        // logger.error("Invalid exam configured {}. No computation sibling
        // columns for {}",
        // examReportHeaderStructureColumn.getExamId(),
        // examReportHeaderStructureColumn.getExamReportGridColumnType());
        // throw new LernenRunTimeException("Invalid exam configured. . No
        // computation sibling columns present");
        // }

        /**
         * Validates computation columns
         */
        for (final String computationColumnId : examReportHeaderStructureColumn.getComputationColumns()) {
            /**
             * Summation can be done only over sibling columns
             */
            // if (!siblingColumnIds.contains(computationColumnId)) {
            // logger.error("Invalid exam configured {}. Computation column is
            // not sibling column for {}",
            // examReportHeaderStructureColumn.getExamId(),
            // computationColumnId);
            // throw new LernenRunTimeException("Invalid exam configured.
            // Computation column is not sibling column");
            // }

            final ExamReportMarksColumn<T> examReportMarksColumn = examReportMarksColumnsMap.get(computationColumnId);

            if (examReportMarksColumn == null) {
                logger.error("Invalid state for exam {}. Required computation column {} is null",
                        examReportHeaderStructureColumn.getExamId(), computationColumnId);
                throw new EmbrateRunTimeException("Invalid state for exam. Required computation column is null");
            }
        }
    }

    private <T extends ExamDimensionValues> ExamReportMarksColumn<T> getExamReportMarksColumn(
            ExamReportHeaderStructureColumn examReportHeaderStructureColumn, ExamMetaData examMetaData,
            List<ExamReportMarksColumn<T>> childExamReportMarksColumns, List<T> examDimensionValues) {
        return new ExamReportMarksColumn<>(examReportHeaderStructureColumn.getId(),
                examReportHeaderStructureColumn.getTitle(), examReportHeaderStructureColumn.getSubTitle(),
                examReportHeaderStructureColumn.getColorHexCode(),
                examReportHeaderStructureColumn.isDisplayTotalMarks(),
                examReportHeaderStructureColumn.getExamReportGridColumnType(),
                examReportHeaderStructureColumn.getRoundOff(), examMetaData,
                childExamReportMarksColumns, examDimensionValues,
                examReportHeaderStructureColumn.isHideTotalDimensionMaxMarks(), examReportHeaderStructureColumn.isHideRemainingDimensionMaxMarks(),
                examReportHeaderStructureColumn.getCourseWiseClassMetricsComputeColumn(),
                examReportHeaderStructureColumn.getCourseWiseGainMetricsComputeColumns(), examReportHeaderStructureColumn.getColumnValue(),
                examReportHeaderStructureColumn.isHide());
    }

    private List<ExamDimensionObtainedValues> getHeaderExamDimensionValues(UUID examId,
                                                                           Map<UUID, Map<Integer, ExamDimensionObtainedValues>> examDimensionsMap,
                                                                           ExamReportHeaderStructureColumn examReportHeaderStructureColumn, List<ExamGrade> examGrades, ExamDimension defaultTotalDimension, Map<Integer,ExamDimension> dimensionMap) {

        // FIX_PD_4202
//        if (examDimensionsMap.get(examId) == null) {
//            logger.error("exam {} dimensions does not exists. Exitting", examId);
//            throw new EmbrateRunTimeException("Exam does not exist");
//        }

        final List<ExamDimensionObtainedValues> examDimensionValuesList = new ArrayList<>();
        /**
         * Empty dimension list is assumed to be total marks across all
         * dimensions
         */
        if (CollectionUtils.isEmpty(examReportHeaderStructureColumn.getObtainedMarksDimensions())) {
            ExamDimensionObtainedValues examDimensionValues = null;
            if (examDimensionsMap.get(examId) == null) {
                // FIX_PD_4202
                examDimensionValues = new ExamDimensionObtainedValues(defaultTotalDimension);
            } else {
                examDimensionValues = getTotalExamDimensionValues(
                        examDimensionsMap.get(examId));
            }

            if (examDimensionValues == null) {
                logger.error("Total dimension does not exist for exam {}. Exitting", examId);
                throw new EmbrateRunTimeException("Total dimension does not exist");
            }

            ExamMarksUtils.updateMarksBasedGrade(examGrades, examDimensionValues);
            examDimensionValuesList.add(examDimensionValues);
            return examDimensionValuesList;
        }

        for (final Integer dimensionId : examReportHeaderStructureColumn.getObtainedMarksDimensions()) {
            ExamDimensionObtainedValues examDimensionValues = null;
            if (examDimensionsMap.get(examId) == null || examDimensionsMap.get(examId).get(dimensionId) == null) {
                // FIX_PD_4202
                examDimensionValues = new ExamDimensionObtainedValues(dimensionMap.get(dimensionId));
            } else {
                examDimensionValues = examDimensionsMap.get(examId).get(dimensionId);
            }


            ExamMarksUtils.updateMarksBasedGrade(examGrades, examDimensionValues);
            examDimensionValuesList.add(examDimensionValues);
        }

        return examDimensionValuesList;
    }

    private Map<UUID, ExamMetaData> getExamMetaDataMap(List<StudentExamMarksDetails> studentEffectiveExamMarks) {
        final Map<UUID, ExamMetaData> examMetaDataMap = new HashMap<>();
        for (final StudentExamMarksDetails studentExamMarksDetails : studentEffectiveExamMarks) {
            examMetaDataMap.put(studentExamMarksDetails.getExamMetaData().getExamId(),
                    studentExamMarksDetails.getExamMetaData());
        }
        return examMetaDataMap;
    }

    /**
     * Map<ExamId, Map<Dimension, Double>>
     * <p>
     * This method assumes that all course have same max marks as it has to be
     * shown in header column
     *
     * @param courseType
     * @param studentEffectiveExamMarks
     * @return
     */
    private Map<UUID, Map<Integer, ExamDimensionObtainedValues>> getHeaderExamDimensionsMap(CourseType courseType,
                                                                                            List<StudentExamMarksDetails> studentEffectiveExamMarks, Set<UUID> additionalCourseIds, boolean isAdditionalGridLoop) {

        final Map<UUID, Map<Integer, ExamDimensionObtainedValues>> examDimensionValuesMap = new HashMap<>();

        for (final StudentExamMarksDetails studentExamMarksDetails : studentEffectiveExamMarks) {
            final UUID examId = studentExamMarksDetails.getExamMetaData().getExamId();
            if (!examDimensionValuesMap.containsKey(examId)) {
                examDimensionValuesMap.put(examId, new HashMap<>());
            }

            if (CollectionUtils.isEmpty(studentExamMarksDetails.getCourseMarksMatrix().get(courseType))) {
                continue;
            }
            List<ExamDimensionObtainedValues> examDimensionObtainedValuesList = studentExamMarksDetails
            .getCourseMarksMatrix().get(courseType).get(0).getExamDimensionObtainedValues();
            if(isAdditionalGridLoop){
                for(ExamCourseMarks courseMarksMatric : studentExamMarksDetails.getCourseMarksMatrix().get(courseType)){
                    if(additionalCourseIds.contains(courseMarksMatric.getCourse().getCourseId())){
                        examDimensionObtainedValuesList = courseMarksMatric.getExamDimensionObtainedValues();
                        break;
                    }
                }
            }
            /**
             * Assuming max marks to be same for all courses as it has to be
             * shown in header column. If marks are different then
             * showCourseWiseMarks flag should be true in exam structure
             */
            for (final ExamDimensionObtainedValues examDimensionObtainedValues : examDimensionObtainedValuesList) {
                
                // Here getOriginalMaxMarks is kept intentionally in both columns as we do not want to show
                // adjusted max marks if present for first course in header row. It should be original max marks only
                // If we want to handle cases where some specific total max value needs to be shown, we should handle here.
                examDimensionValuesMap.get(examId).put(examDimensionObtainedValues.getExamDimension().getDimensionId(),
                        new ExamDimensionObtainedValues(examDimensionObtainedValues.getExamDimension(), null, null,
                                null, null, examDimensionObtainedValues.getOriginalMaxMarks(), examDimensionObtainedValues.getOriginalMaxMarks(),
                                examDimensionObtainedValues.getMinMarks(), examDimensionObtainedValues.getMaxGrade(),
                                examDimensionObtainedValues.getMinGrade(), examDimensionObtainedValues.getExamCoursePublishedStatus()));
            }

        }
        return examDimensionValuesMap;
    }

    /**
     * Map<CourseId, Map<ExamId, Map<Dimension, ExamDimensionObtainedValues>>>
     *
     * @param studentEffectiveExamMarks
     * @return
     */
    private Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> getCourseExamDimensionValuesMap(
            List<StudentExamMarksDetails> studentEffectiveExamMarks) {
        final Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> courseExamDimensionValuesMap = new HashMap<>();

        for (final StudentExamMarksDetails studentExamMarksDetails : studentEffectiveExamMarks) {
            final UUID examId = studentExamMarksDetails.getExamMetaData().getExamId();

            for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
                final UUID courseId = examCourseMarks.getCourse().getCourseId();
                if (!courseExamDimensionValuesMap.containsKey(courseId)) {
                    courseExamDimensionValuesMap.put(courseId, new HashMap<>());
                }
                if (!courseExamDimensionValuesMap.get(courseId).containsKey(examId)) {
                    courseExamDimensionValuesMap.get(courseId).put(examId, new HashMap<>());
                }
                for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
                        .getExamDimensionObtainedValues()) {
                    courseExamDimensionValuesMap.get(courseId).get(examId).put(
                            examDimensionObtainedValues.getExamDimension().getDimensionId(),
                            examDimensionObtainedValues);
                }
            }
        }
        return courseExamDimensionValuesMap;
    }

    private ExamDimensionObtainedValues getTotalExamDimensionValues(
            Map<Integer, ExamDimensionObtainedValues> examDimensionValuesMap) {
        for (final Entry<Integer, ExamDimensionObtainedValues> entry : examDimensionValuesMap.entrySet()) {
            if (entry.getValue().getExamDimension().isTotal()) {
                return entry.getValue();
            }
        }
        return null;
    }

    public ReportCardVariableDetails getStudentsReportCardVariables(int instituteId, int academicSessionId,
                                                                    UUID standardId, String reportType) {

        return examinationDao.getReportCardVariablesByReportType(instituteId, standardId, null, academicSessionId,
                reportType, null);

    }

    public ReportOutput generateReport(int instituteId, int academicSessionId, String reportType) {
        return generateAnnualMarksReport(instituteId, academicSessionId, reportType, "AnnualMarksReport.xlsx");
    }

    private ReportOutput generateAnnualMarksReport(int instituteId, int academicSessionId, String reportType,
                                                   String reportName) {
        final List<ExamResultReportData> examResultReportDatas = getExamResultReportData(instituteId, academicSessionId,
                reportType);
        final Workbook workbook = new XSSFWorkbook(); // new HSSFWorkbook() for
        // generating `.xlsx` file

        /*
         * CreationHelper helps us create instances of various things like
         * DataFormat, Hyperlink, RichTextString etc, in a format (HSSF, XSSF)
         * independent way
         */
        // CreationHelper createHelper = workbook.getCreationHelper();

        // Create a Font for styling header cells
        final Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.BLACK.getIndex());

        // Create a CellStyle with the font
        final CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);

        // Create a Sheet
        final Sheet sheet = workbook.createSheet("Report");
        // Create a Row
        final Row headerRow = sheet.createRow(0);
        // Create cells
        for (int i = 0; i < ANNUAL_RESULT_REPORT_HEADER.length; i++) {
            final Cell cell = headerRow.createCell(i);
            cell.setCellValue(ANNUAL_RESULT_REPORT_HEADER[i]);
            cell.setCellStyle(headerCellStyle);
        }
        if (!CollectionUtils.isEmpty(examResultReportDatas)) {

            // Create Other rows and cells with inventory data
            int rowNum = 1;
            int count = 1;
            for (final ExamResultReportData examResultReportData : examResultReportDatas) {
                final Row row = sheet.createRow(rowNum++);
                int colNum = 0;
                row.createCell(colNum++).setCellValue(String.valueOf(count));
                row.createCell(colNum++).setCellValue(examResultReportData.getAdmissionNumber());
                row.createCell(colNum++).setCellValue(examResultReportData.getStudentName());
                row.createCell(colNum++).setCellValue(StringUtils.isEmpty(examResultReportData.getFathername()) ? "-"
                        : examResultReportData.getFathername());
                row.createCell(colNum++).setCellValue(examResultReportData.getStandardName());
                row.createCell(colNum++).setCellValue(examResultReportData.getMarksObtained() == null ? "-"
                        : String.valueOf(examResultReportData.getMarksObtained()));
                row.createCell(colNum++).setCellValue(examResultReportData.getTotalMarks() == null ? "-"
                        : String.valueOf(examResultReportData.getTotalMarks()));
                row.createCell(colNum++).setCellValue(examResultReportData.getPercentage() == null ? "-"
                        : String.valueOf(Math.round(examResultReportData.getPercentage() * 100) / 100d) + "%");
                row.createCell(colNum++).setCellValue(

                        examResultReportData.getGrade() == null ? "-" : examResultReportData.getGrade().getGradeName());
                count++;
            }
        }
        // Resize all columns to fit the content size
        for (int i = 0; i < ANNUAL_RESULT_REPORT_HEADER.length; i++) {
            sheet.autoSizeColumn(i);
        }
        final ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            workbook.write(bos);
            workbook.close();
            return new ReportOutput(reportName, bos);
        } catch (final IOException e) {
            e.printStackTrace();
        }
        return null;

    }

    private List<ExamResultReportData> getExamResultReportData(int instituteId, int academicSessionId,
                                                               String reportType) {

        final List<ExamResultReportData> examResultReportDatas = new ArrayList<>();
        final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
        for (final Standard standard : standards) {
            final ExamReportCardConfiguration examReportCardConfiguration = getExamReportCardConfiguration(instituteId,
                    academicSessionId, standard.getStandardId(), reportType);
            final ExamReportStructure examReportStructure = examReportCardConfiguration.getExamReportStructure();
            if (examReportStructure == null
                    || StringUtils.isEmpty(examReportStructure.getExamReportStructureMetaData().getTemplateClass())
                    || CollectionUtils.isEmpty(examReportStructure.getExamReportHeaderStructureColumns())) {
                logger.error(
                        "Exam report meta data is not configured for institute {}, academicSessionId {}, standardId {},reportType{}",
                        instituteId, academicSessionId, standard.getStandardId(), reportType);
                continue;
            }
            final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationManager.getExamGrades(instituteId,
                    academicSessionId, standard.getStandardId());

            final ClassExamData classExamData = getClassExamReportMarksGrid(instituteId, standard.getStandardId(), null,
                    academicSessionId, examReportStructure, courseTypeExamGrades);

            //StudentId, ExamId, Percentage
            final Map<UUID, Map<UUID, Double>> studentExamPercentageMap = new HashMap<>();
            //StudentId, ExamId, ExamResultStatus
            final Map<UUID, Map<UUID, ExamResultStatus>> studentExamResultStatusMap = new HashMap<>();

            for (final Entry<UUID, Map<CourseType, ExamReportMarksGrid>> studentLevelEntry : classExamData
                    .getClassExamReportMarksGridMap().entrySet()) {
                final UUID studentId = studentLevelEntry.getKey();
                final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId,
                        academicSessionId, studentId);
                if(!studentExamPercentageMap.containsKey(studentId)) {
                    studentExamPercentageMap.put(studentId, new HashMap<>());
                }
                if(!studentExamResultStatusMap.containsKey(studentId)) {
                    studentExamResultStatusMap.put(studentId, new HashMap<>());
                }
                final ExamReportData examReportData = computeExamReportMarksData(instituteId, examReportStructure,
                        studentLevelEntry.getValue(), courseTypeExamGrades, student,
                        classExamData.getClassExamMarks().get(studentId), studentExamPercentageMap.get(studentId),
                        studentExamResultStatusMap.get(studentId));
                final ExamResultReportData examResultReportData = new ExamResultReportData(studentId,
                        student.getStudentBasicInfo().getAdmissionNumber(), student.getStudentBasicInfo().getName(),
                        student.getStudentFamilyInfo().getFathersName(), standard.getLevel(), standard.getDisplayName(),
                        examReportData.getTotalObtainedMarks(), examReportData.getTotalMaxMarks(),
                        examReportData.getPercentage(), examReportData.getTotalGrade());
                examResultReportDatas.add(examResultReportData);
            }
        }
        Collections.sort(examResultReportDatas, new Comparator<ExamResultReportData>() {

            @Override
            public int compare(ExamResultReportData s1, ExamResultReportData s2) {
                if (s1.getStandardLevel() > s2.getStandardLevel()) {
                    return 1;
                } else if (s1.getStandardLevel() < s2.getStandardLevel()) {
                    return -1;
                } else {
                    return s1.getStudentName().compareToIgnoreCase(s2.getStudentName());
                }
            }
        });
        return examResultReportDatas;

    }

    public ReportCardVariableDetails getClassList(int instituteId, UUID standardId, Integer sectionId,
                                                  int academicSessionId, String reportType, UUID studentId,
                                                  StudentSortingParameters studentSortingParameters) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid institute."));
        }
        if (sectionId != null && sectionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid section."));
        }
        if (standardId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid standard details"));
        }

        ReportCardVariableDetails reportCardVariableDetails = examinationDao.getReportCardVariablesByReportType(instituteId, standardId, sectionId, academicSessionId,
                reportType, studentId);

        StudentSorter.sortStudents(
                reportCardVariableDetails.getStudentReportCardVariableDetails(),
                studentSortingParameters,
                new StudentSorter.StudentKeyExtractor<StudentReportCardVariableDetails>() {
                    @Override
                    public String getAdmissionNumber(StudentReportCardVariableDetails studentReportCardVariableDetails) {
                        return studentReportCardVariableDetails.getStudent().getStudentBasicInfo().getAdmissionNumber();
                    }

                    @Override
                    public String getName(StudentReportCardVariableDetails studentReportCardVariableDetails) {
                        return studentReportCardVariableDetails.getStudent().getStudentBasicInfo().getName();
                    }

                    @Override
                    public String getRollNumber(StudentReportCardVariableDetails studentReportCardVariableDetails) {
                        return studentReportCardVariableDetails.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber();
                    }
                    @Override
                    public int getStandardLevel(StudentReportCardVariableDetails studentReportCardVariableDetails) {
                        return studentReportCardVariableDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getLevel();
                    }

                    @Override
                    public String getSectionName(StudentReportCardVariableDetails studentReportCardVariableDetails) {
                        List<StandardSections> sections = studentReportCardVariableDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
                        return (sections != null && !sections.isEmpty()) ? sections.get(0).getSectionName() : null;
                    }

                    @Override
                    public Integer getAdmissionDate(StudentReportCardVariableDetails studentReportCardVariableDetails) {
                        return studentReportCardVariableDetails.getStudent().getStudentBasicInfo().getAdmissionDate();
                    }
                }
        );


        return reportCardVariableDetails;
    }

    public boolean updateReportCardAttribute(int instituteId,
                                             ReportCardVariableDetailsPayload reportCardVariableDetails, UUID userId) {

        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid user id"));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_REPORT_CARD_DETAILS);

        validateReportCardVariables(reportCardVariableDetails);
        String reportType = reportCardVariableDetails.getReportType();
        int academicSessionId = reportCardVariableDetails.getAcademicSessionId();
        ExaminationPreferences examinationPreferences = userPreferenceSettings.getExaminationPreferences(instituteId);
        List<ReportCardVariableFieldProperties> reportCardVariableParametersList = examinationPreferences.getReportCardVariableFieldPropertiesList();
        Set<ReportCardVariableParameters> reportCardVariableParametersSet = new HashSet<>();
        if(CollectionUtils.isEmpty(reportCardVariableParametersList)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "No report card variable setuped!"));
        }
        for(ReportCardVariableFieldProperties reportCardVariableFieldProperties : reportCardVariableParametersList) {
            reportCardVariableParametersSet.add(reportCardVariableFieldProperties.getReportCardVariableParameters());
        }
        List<StudentReportCardVariableDetailsPayload> studentReportCardVariableDetailsPayload = reportCardVariableDetails.getStudentReportCardVariableDetailsPayload();

        ReportCardVariableDetailsPayloadLite reportCardVariableDetailsLite = new ReportCardVariableDetailsPayloadLite(
                instituteId, reportType, academicSessionId, reportCardVariableParametersSet, studentReportCardVariableDetailsPayload);

        return liteUpdateReportCardAttribute(instituteId, reportCardVariableDetailsLite, userId);
    }

    public boolean liteUpdateReportCardAttribute(int instituteId,
                                                 ReportCardVariableDetailsPayloadLite reportCardVariableDetailsLite, UUID userId) {

        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid user id"));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_REPORT_CARD_DETAILS);

        validateReportCardVariablesLite(reportCardVariableDetailsLite);
        return examinationDao.liteUpdateReportCardAttribute(reportCardVariableDetailsLite);
    }

    private void validateReportCardVariablesLite(ReportCardVariableDetailsPayloadLite reportCardVariableDetailsLite) {
        if (reportCardVariableDetailsLite == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid standard details"));
        }
        if (reportCardVariableDetailsLite.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid institute."));
        }
        if (reportCardVariableDetailsLite.getAcademicSessionId() <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid academicSessionId."));
        }
        if (StringUtils.isBlank(reportCardVariableDetailsLite.getReportType())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid report type."));
        }

        if (CollectionUtils.isEmpty(reportCardVariableDetailsLite.getReportCardVariableParametersSet())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid report card variable parameters."));
        }

        if (CollectionUtils.isEmpty(reportCardVariableDetailsLite.getStudentReportCardVariableDetailsPayload())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid report card variable details."));
        }
    }

    private void validateReportCardVariables(ReportCardVariableDetailsPayload reportCardVariableDetails) {
        if (reportCardVariableDetails == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid standard details"));
        }
        if (reportCardVariableDetails.getInstituteId() <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid institute."));
        }
        if (reportCardVariableDetails.getAcademicSessionId() <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid academicSessionId."));
        }
        if (StringUtils.isBlank(reportCardVariableDetails.getReportType())) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid report type."));
        }

        if (!CollectionUtils.isEmpty(reportCardVariableDetails.getStudentReportCardVariableDetailsPayload())) {
            for (final StudentReportCardVariableDetailsPayload studentReportCardVariableDetailsPayload : reportCardVariableDetails
                    .getStudentReportCardVariableDetailsPayload()) {

                if (studentReportCardVariableDetailsPayload.getAttendedDays() != null
                        && studentReportCardVariableDetailsPayload.getTotalDays() == null) {
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                                    "Total days are not set, so cannot fill attended days."));
                }

                if (studentReportCardVariableDetailsPayload.getAttendedDays() != null
                        && studentReportCardVariableDetailsPayload.getAttendedDays() < 0d) {
                    throw new ApplicationException(new ErrorResponse(
                            ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Negative attended days."));
                }
                if (studentReportCardVariableDetailsPayload.getAttendedDays() != null
                        && studentReportCardVariableDetailsPayload
                        .getAttendedDays() > studentReportCardVariableDetailsPayload.getTotalDays()) {
                    throw new ApplicationException(
                            new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                                    "Attended days cannot be greater than total days."));
                }
            }
        }
    }

    public List<StudentPersonalityTraitsDetails> getStudentPersonalityTraits(int instituteId, UUID standardId, Integer sectionId,
                                                                             int academicSessionId, String reportType, UUID studentId,
                                                                             StudentSortingParameters studentSortingParameters) {

        if (instituteId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid institute."));
        }
        if (sectionId != null && sectionId <= 0) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid section."));
        }
        if (standardId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid standard details"));
        }

        List<StudentPersonalityTraitsDetailsRow> studentPersonalityTraitsDetailsRowList = examinationDao.getStudentPersonalityTraits(
                instituteId, standardId, sectionId, academicSessionId, reportType, studentId);

        if(CollectionUtils.isEmpty(studentPersonalityTraitsDetailsRowList)) {
            return null;
        }
        List<PersonalityTraitsDetails> personalityTraitsDetailsList = examinationManager.getStandardPersonalityTraits(
                instituteId, academicSessionId, standardId);

        Map<UUID, PersonalityTraitsDetails> personalityTraitsDetailsMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(personalityTraitsDetailsList)) {
            for(PersonalityTraitsDetails personalityTraitsDetails : personalityTraitsDetailsList) {
                personalityTraitsDetailsMap.put(personalityTraitsDetails.getPersonalityTraitId(), personalityTraitsDetails);
            }
        }

        //StudentId, PersonalityTraitId, StudentPersonalityTraitsDetailsRow
        Map<UUID, Map<UUID, StudentPersonalityTraitsDetailsRow>> studentPersonalityTraitsDetailsRowMap = new HashMap<>();
        Map<UUID, Student> studentMap = new HashMap<>();
        for(StudentPersonalityTraitsDetailsRow studentPersonalityTraitsDetailsRow : studentPersonalityTraitsDetailsRowList) {
            UUID currentStudentId = studentPersonalityTraitsDetailsRow.getStudent().getStudentId();
            if(!studentMap.containsKey(currentStudentId)) {
                studentMap.put(currentStudentId, studentPersonalityTraitsDetailsRow.getStudent());
            }
            if(!studentPersonalityTraitsDetailsRowMap.containsKey(currentStudentId)) {
                studentPersonalityTraitsDetailsRowMap.put(currentStudentId, new HashMap<>());
            }
            if(studentPersonalityTraitsDetailsRow.getPersonalityTraitsDetails() == null) {
                continue;
            }
            UUID currentPersonalityTraitId = studentPersonalityTraitsDetailsRow.getPersonalityTraitsDetails().getPersonalityTraitId();
            if(!studentPersonalityTraitsDetailsRowMap.get(currentStudentId).containsKey(currentPersonalityTraitId)) {
                studentPersonalityTraitsDetailsRowMap.get(currentStudentId).put(currentPersonalityTraitId, studentPersonalityTraitsDetailsRow);
            }
        }

        for(Entry<UUID, Map<UUID, StudentPersonalityTraitsDetailsRow>> studentPersonalityTraitsDetailsRowEntry : studentPersonalityTraitsDetailsRowMap.entrySet()) {
            UUID currentStudentId = studentPersonalityTraitsDetailsRowEntry.getKey();
            Student student = studentMap.get(currentStudentId);
            Map<UUID, StudentPersonalityTraitsDetailsRow> personalityTraitsDetailsRowMap = studentPersonalityTraitsDetailsRowEntry.getValue();
            Set<UUID> personalityTraitIdSet = personalityTraitsDetailsRowMap.keySet();
            for(Entry<UUID, PersonalityTraitsDetails> personalityTraitsDetailsEntry : personalityTraitsDetailsMap.entrySet()) {
                UUID currentPersonalityTraitId = personalityTraitsDetailsEntry.getKey();
                PersonalityTraitsDetails personalityTraitsDetails = personalityTraitsDetailsEntry.getValue();
                if(!personalityTraitIdSet.contains(currentPersonalityTraitId)) {
                    personalityTraitsDetailsRowMap.put(currentPersonalityTraitId, new StudentPersonalityTraitsDetailsRow(
                            student, null, personalityTraitsDetails));
                }
            }
        }

        Map<UUID, StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailsMap = new HashMap<>();
        for(Entry<UUID, Map<UUID, StudentPersonalityTraitsDetailsRow>> studentPersonalityTraitsDetailsRowEntry : studentPersonalityTraitsDetailsRowMap.entrySet()) {
            UUID currentStudentId = studentPersonalityTraitsDetailsRowEntry.getKey();
            Student student = studentMap.get(currentStudentId);
            Map<UUID, StudentPersonalityTraitsDetailsRow> personalityTraitsDetailsRowMap = studentPersonalityTraitsDetailsRowEntry.getValue();
            if(!studentPersonalityTraitsDetailsMap.containsKey(currentStudentId)) {
                studentPersonalityTraitsDetailsMap.put(currentStudentId,
                        new StudentPersonalityTraitsDetails(student, new ArrayList<>()));
            }
            for(Entry<UUID, StudentPersonalityTraitsDetailsRow> personalityTraitsDetailsRowEntry : personalityTraitsDetailsRowMap.entrySet()) {
                UUID currentPersonalityTrait = personalityTraitsDetailsRowEntry.getKey();
                StudentPersonalityTraitsDetailsRow studentPersonalityTraitsDetailsRow = personalityTraitsDetailsRowEntry.getValue();
                studentPersonalityTraitsDetailsMap.get(currentStudentId).getStudentPersonalityTraitsResponseList().add(
                        new StudentPersonalityTraitsResponse(studentPersonalityTraitsDetailsRow.getResponse(),
                                studentPersonalityTraitsDetailsRow.getPersonalityTraitsDetails())
                );
            }
        }

        List<StudentPersonalityTraitsDetails> studentPersonalityTraitsDetailsList = new ArrayList<>(studentPersonalityTraitsDetailsMap.values());
        for(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails : studentPersonalityTraitsDetailsList) {
            StudentPersonalityTraitsResponse.sortStudentPersonalityTraitsResponseBySequence(studentPersonalityTraitsDetails.getStudentPersonalityTraitsResponseList());
        }

        StudentSorter.sortStudents(
                studentPersonalityTraitsDetailsList,
                studentSortingParameters,
                new StudentSorter.StudentKeyExtractor<StudentPersonalityTraitsDetails>() {
                    @Override
                    public String getAdmissionNumber(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails) {
                        return studentPersonalityTraitsDetails.getStudent().getStudentBasicInfo().getAdmissionNumber();
                    }

                    @Override
                    public String getName(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails) {
                        return studentPersonalityTraitsDetails.getStudent().getStudentBasicInfo().getName();
                    }

                    @Override
                    public String getRollNumber(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails) {
                        return studentPersonalityTraitsDetails.getStudent().getStudentAcademicSessionInfoResponse().getRollNumber();
                    }

                    @Override
                    public int getStandardLevel(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails) {
                        return studentPersonalityTraitsDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getLevel();
                    }

                    @Override
                    public String getSectionName(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails) {
                        List<StandardSections> sections = studentPersonalityTraitsDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
                        return (sections != null && !sections.isEmpty()) ? sections.get(0).getSectionName() : null;
                    }

                    @Override
                    public Integer getAdmissionDate(StudentPersonalityTraitsDetails studentPersonalityTraitsDetails) {
                        return studentPersonalityTraitsDetails.getStudent().getStudentBasicInfo().getAdmissionDate();
                    }
                }
        );

        return studentPersonalityTraitsDetailsList;
    }

    public boolean updateStudentPersonalityTraitsDetails(int instituteId, int academicSessionId, String reportType, List<StudentPersonalityTraitsPayload> studentPersonalityTraitsPayloadList,
                                                         UUID userId) {
        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid institute id."));
        }
        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid user id"));
        }
        userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_STUDENT_PERSONALITY_TRAITS);

        if (StringUtils.isBlank(reportType)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid report type"));
        }
        if (academicSessionId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid user id"));
        }
        validateStudentPersonalityTraitsDetails(studentPersonalityTraitsPayloadList);
        return examinationDao.updateStudentPersonalityTraitsDetails(instituteId, academicSessionId, reportType, studentPersonalityTraitsPayloadList);
    }

    private void validateStudentPersonalityTraitsDetails(List<StudentPersonalityTraitsPayload> studentPersonalityTraitsPayloadList) {
        if (CollectionUtils.isEmpty(studentPersonalityTraitsPayloadList)) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION,
                    "Invalid personality traits payload"));
        }
        for(StudentPersonalityTraitsPayload studentPersonalityTraitsPayload : studentPersonalityTraitsPayloadList) {
            if (studentPersonalityTraitsPayload.getStudentId() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid student id."));
            }
            if (studentPersonalityTraitsPayload.getPersonalityTraitId() == null) {
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_REPORT_CONFIGURATION, "Invalid personality trait."));
            }
        }
    }

    private class StudentExamData {

        private final Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid;
        private final Map<CourseType, ExamReportMarksGrid> additionalCourseTypeExamReportMarksGrid;
        private final List<StudentExamMarksDetails> studentExamMarksDetails;

        public StudentExamData(Map<CourseType, ExamReportMarksGrid> courseTypeExamReportMarksGrid, Map<CourseType, ExamReportMarksGrid> additionalCourseTypeExamReportMarksGrid,
                               List<StudentExamMarksDetails> studentExamMarksDetails) {
            this.courseTypeExamReportMarksGrid = courseTypeExamReportMarksGrid;
            this.additionalCourseTypeExamReportMarksGrid = additionalCourseTypeExamReportMarksGrid;
            this.studentExamMarksDetails = studentExamMarksDetails;
        }

        public Map<CourseType, ExamReportMarksGrid> getCourseTypeExamReportMarksGrid() {
            return courseTypeExamReportMarksGrid;
        }

        public List<StudentExamMarksDetails> getStudentExamMarksDetails() {
            return studentExamMarksDetails;
        }

        public Map<CourseType, ExamReportMarksGrid> getAdditionalCourseTypeExamReportMarksGrid() {
            return additionalCourseTypeExamReportMarksGrid;
        }
        
        @Override
        public String toString() {
            return "StudentExamData [courseTypeExamReportMarksGrid=" + courseTypeExamReportMarksGrid
                    + ", additionalCourseTypeExamReportMarksGrid=" + additionalCourseTypeExamReportMarksGrid
                    + ", studentExamMarksDetails=" + studentExamMarksDetails + "]";
        }

        

    }

    private class ClassExamData {

        private final Map<UUID, Map<CourseType, ExamReportMarksGrid>> classExamReportMarksGridMap;
        private final Map<UUID, Map<CourseType, ExamReportMarksGrid>> additionalClassExamReportMarksGridMap;
        private final Map<UUID, List<StudentExamMarksDetails>> classExamMarks;

        public ClassExamData(Map<UUID, Map<CourseType, ExamReportMarksGrid>> classExamReportMarksGridMap,
                             Map<UUID, Map<CourseType, ExamReportMarksGrid>> additionalClassExamReportMarksGridMap,
                             Map<UUID, List<StudentExamMarksDetails>> classExamMarks) {
            this.classExamReportMarksGridMap = classExamReportMarksGridMap;
            this.additionalClassExamReportMarksGridMap = additionalClassExamReportMarksGridMap;
            this.classExamMarks = classExamMarks;
        }

        public Map<UUID, Map<CourseType, ExamReportMarksGrid>> getClassExamReportMarksGridMap() {
            return classExamReportMarksGridMap;
        }

        public Map<UUID, Map<CourseType, ExamReportMarksGrid>> getAdditionalClassExamReportMarksGridMap() {
            return additionalClassExamReportMarksGridMap;
        }

        public Map<UUID, List<StudentExamMarksDetails>> getClassExamMarks() {
            return classExamMarks;
        }

        @Override
        public String toString() {
            return "ClassExamData [classExamReportMarksGridMap=" + classExamReportMarksGridMap
                    + ", additionalClassExamReportMarksGridMap=" + additionalClassExamReportMarksGridMap
                    + ", classExamMarks=" + classExamMarks + "]";
        }

    }

    public StudentManagementFieldDetails getStudentExamResultDetails(int instituteId, int academicSessionId, UUID standardId,
            Set<UUID> examIdSet, Set<UUID> additionalCoursesSet, String sectionIdSetStr, String fieldListCSV) {

        Set<Integer> sectionIdSet = StringHelper.convertStringToSetInteger(sectionIdSetStr);

        final List<ExamCoursesData> examCoursesDataList = examinationManager.getExamCourses(instituteId,
                academicSessionId, standardId);

        StandardMetadata standardMetadata = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
        boolean isCoScholasticGrade = standardMetadata.isCoScholasticGradingEnabled();
        boolean isScholasticGrade = standardMetadata.isScholasticGradingEnabled();

        //For exam rank data we just need scholastic subjects, so skipping coscholstic ones
        boolean containsScholasticSubjects = true;
        boolean containsCoScholasticSubjects = false;

        Map<UUID, Boolean> examScholasticContainMap = new HashMap<>();
        Map<UUID, Boolean> examCoScholasticContainMap = new HashMap<>();
        ExamMarksUtils.getCoursesContainMapDetails(examCoursesDataList, examScholasticContainMap, examCoScholasticContainMap);

        String reportCardType = "REPORT";
        String examResultCalculatorId = examResultCalculatorFactory.getExamResultCalculatorId(instituteId, standardId);
        ExamReportCardConfiguration examReportCardConfiguration = ExamReportCardConfigurationUtils
                .getReportExamReportCardConfiguration(instituteId, academicSessionId, standardId, examIdSet, reportCardType,
                        containsScholasticSubjects, containsCoScholasticSubjects, examScholasticContainMap, examCoScholasticContainMap,
                        null, true, true, examResultCalculatorId, null, null,
                        isCoScholasticGrade, isScholasticGrade, additionalCoursesSet);

        List<ExamReportData> classExamReportData = getClassExamReportData(instituteId,
                academicSessionId, standardId, null, examReportCardConfiguration.getExamReportStructure(),
                reportCardType, true);


        boolean includeCoscholasticCourses = false;
        IRankCalculator rankCalculator = rankCalculatorFactory.getRankCalculator(instituteId);
        ExamResultAnalysisDetails examResultAnalysisDetails = ExamMarksUtils.getSubjectWiseRankReportData(classExamReportData,
                new StringBuilder(), new StringBuilder(), new HashMap<>(), sectionIdSet,
                null, includeCoscholasticCourses, additionalCoursesSet, isScholasticGrade, isCoScholasticGrade, rankCalculator);


        Map<UUID, StudentLite> studentMap = examResultAnalysisDetails.getStudentLiteMap();

        if(studentMap == null || CollectionUtils.isEmpty(studentMap.entrySet())) {
            return null;
        }

        List<StudentLite> studentLiteList = new ArrayList<>(studentMap.values());
        StudentDataUtils.sortStudentOnRollNumberAndName(studentLiteList);
        //StudentId/Marks
        Map<UUID, Double> studentObtainedMarksMap = examResultAnalysisDetails.getStudentObtainedMarksMap();
        //StudentId/Marks
        Map<UUID, Double> studentMaxMarksMap = examResultAnalysisDetails.getStudentMaxMarksMap();
        //StudentId/Percentage
        Map<UUID, Double> studentPercentageMap = examResultAnalysisDetails.getStudentPercentageMap();
        //StudentId/Grade
        Map<UUID, String> studentGradeMap = examResultAnalysisDetails.getStudentGradeMap();
        //StudentId/Division
        Map<UUID, ExamDivision> studentDivisionMap = examResultAnalysisDetails.getStudentDivisionMap();
        //StudentId/Result
        Map<UUID, ExamResultStatus> studentExamResultStatusMap = examResultAnalysisDetails.getStudentExamResultStatusMap();
        //StudentId/Rank
        Map<UUID, Integer> studentRankMap = examResultAnalysisDetails.getStudentRankMap();


        Map<UUID, StudentExamResultDetails> studentExamResultDetailsMap = new HashMap<>();
        for(StudentLite studentLite : studentLiteList) {
            UUID studentId = studentLite.getStudentId();
            String standardNameWithSection = studentLite.getStudentSessionData().getStandardNameWithSection();
            String rollNumber = studentLite.getStudentSessionData().getRollNumber();
            String studentName = studentLite.getName();
            String admissionNumber = studentLite.getAdmissionNumber();
            String fatherName = studentLite.getFathersName();
            String dob = studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? null :
                    DateUtils.getFormattedDate(studentLite.getDateOfBirth());

            Double totalMarks = studentMaxMarksMap.get(studentId);
            Double obtainedMarks = studentObtainedMarksMap.get(studentId);
            Double percentage = studentPercentageMap.get(studentId);
            String grade = studentGradeMap.get(studentId);
            ExamDivision examDivision = studentDivisionMap.get(studentId);
            ExamResultStatus examResultStatus = studentExamResultStatusMap.get(studentId);
            Integer rank = studentRankMap.get(studentId);

            studentExamResultDetailsMap.put(studentId, new StudentExamResultDetails(studentId, examIdSet, admissionNumber, rollNumber, studentName,
                    dob, fatherName, standardNameWithSection, NumberUtils.formatDoubleNumber(obtainedMarks), NumberUtils.formatDoubleNumber(totalMarks),
                    NumberUtils.formatDoubleNumber(percentage), grade, examDivision, examResultStatus, rank));
        }

        return studentManager.getClassStudentsFieldData(instituteId, academicSessionId, standardId,
                sectionIdSetStr, fieldListCSV, studentExamResultDetailsMap);
    }
}
